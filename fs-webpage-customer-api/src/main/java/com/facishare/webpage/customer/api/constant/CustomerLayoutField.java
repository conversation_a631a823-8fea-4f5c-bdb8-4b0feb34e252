package com.facishare.webpage.customer.api.constant;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/9/6
 */
public class CustomerLayoutField {

    public static final String TYPE_SLIDEIMAGE = "slideImage";
    //布局的常用字段
    public static final String layout = "layout";
    public static final String components = "components";
    public static final String columns = "columns";
    public static final String column = "column";

    public static final String menuGroupShowStyle = "menuGroupShowStyle";
    public static final String menuGroupAcceptShowLine = "menuGroupAcceptShowLine";
    public static final int defaultColumn = 4;
    public static final String globalSettings = "globalSettings";
    public static final String layoutStructure = "layout_structure";
    public static final String quickCreateMenu = "quickCreateMenu";

    public static final String IMGS = "imgs";
    //component的常用字段
    public static final String apiName = "api_name";
    public static final String type = "type";
    public static final String title = "title";
    public static final String titleName = "titleName";
    public static final String header = "header";
    public static final String name = "name";
    public static final String propsType = "propsType";
    public static final String sceneType = "sceneCard";
    public static final String sceneKey = "filters";
    public static final String toolKey = "tools";
    public static final String dataId = "dataId";
    public static final String cardId = "cardId";
    public static final String nameI18nKey = "nameI18nKey";
    public static final String newHeader = "newHeader";
    public static final String originHeader = "origin_header";
    public static final String menus = "menus";
    public static final String menu = "menu";
    public static final String menuType = "menuType";
    public static final String dataSourceEnv = "dataSourceEnv";

    public static final String isCateGory = "isCateGory";
    public static final String newIcon = "newIcon";
    public static final String id = "id";
    public static final String imgs = "imgs";
    public static final String img = "img";
    public static final String props = "props";
    public static final String supportTabs = "supportTabs";
    public static final String limit = "limit";
    public static final int defaultLimit = 1;
    public static final String groupDefaultGroup = "group-defaultGroup";
    public static final String group = "group";
    public static final String menuGroup = "menuGroup";
    public static final String menuGroupREAPINAME = "menuGroup_REAPINAME_";
    public static final String REAPINAME = "_REAPINAME_";
    public static final String menuEntry = "menuEntry";
    public static final String map = "map";
    public static final String icon = "icon";
    public static final String selectIcon = "selectIcon";
    public static final String background = "background";
    public static final String defaultGroupName = "未分组"; //ignoreI18n
    public static final String defaultMenuGroupName = "菜单入口"; //ignoreI18n
    public static final String globalTopBar = "globalTopBar";
    public static final String childComponent = "childComponent";
    public static final String searchBar = "searchBar";
    public static final String searchBarHeader = "搜索框";//ignoreI18n
    public static final String IndexHome = "IndexHome";
    public static final String quickCreate = "quickCreate";
    public static final String quickCreateHeader = "快速新建"; //ignoreI18n
    public static final String topBarheader = "顶部工具栏"; //ignoreI18n
    public static final String localKeys = "localKeys";
    public static final String fixed = "fixed";
    public static final String isHideAdd = "isHideAdd";
    public static final String top = "top";
    public static final String apiNameLink = "apiName";
    public static final String typeBiCard = "biCard";
    public static final String itemI18nKeys = "itemI18nKeys";
    public static final String startWithBI = "BI_";
    public static final String scopeList = "scopeList";
    public static final String scopeDataID = "DataID";
    public static final String scopeDataType = "DataType";
    public static final String biCardApiNamePref = "biCustom_";
    public static final String tabCollection = "tabs";
    public static final String functionEnter = "menu_entry";    // 功能入口的api
    public static final String children = "children";

    //老数据的常用字段
    public static final String oldFilterCardId = "PS_Filter";
    public static final String oldToolCardId = "PS_Tool";

    //场景对应的字段
    public static final String sceneFilterMainID = "FilterMainID";
    public static final String sceneFilterKey = "FilterKey";
    public static final String sceneFilterName = "FilterName";
    public static final String sceneObjectApiName = "ObjectApiName";
    public static final String sceneSearchApiName = "SearchApiName";

    //工具对应的字段
    public static final String toolID = "ToolID";
    public static final String toolName = "ToolName";
    public static final String toolType = "ToolType";
    public static final String toolURL = "URL";
    public static final String toolIsShow = "IsShow";
    public static final String LABEL_PAGE_NAME_CN = "自定义页面"; //ignoreI18n
    public static final String LABEL_PAGE_NAME = "labelPageName";
    public static final String LABEL_INDEX = "labelIndex";
    public static final String SHOW_UPDATE_LAYOUT_BUTTON = "hiddenUpdateLayoutButton";
    public static final String LABEL_INDEX_STRING = "0";
    public static final String LABEL_IS_OPEN_RANGE = "isOpenRange";

    public static final String TAB = "tabs";     // 页签容器

}
