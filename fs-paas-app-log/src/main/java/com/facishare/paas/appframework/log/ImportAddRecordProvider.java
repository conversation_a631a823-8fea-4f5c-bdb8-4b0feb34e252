package com.facishare.paas.appframework.log;

import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import org.springframework.stereotype.Component;

@Component("importAddRecordProvider")
public class ImportAddRecordProvider extends ImportCreateModifyRecordProvider {
    @Override
    public String getApiName() {
        return ActionType.IMPORT_ADD.getId();
    }

    @Override
    public ModifyRecord getModifyRecord(LogInfo logInfo) {
        return super.getModifyRecord(logInfo);
    }
}
