package com.facishare.paas.appframework.core.predef.service

import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.DescribeLogicService
import com.facishare.paas.appframework.metadata.TagLogicService
import com.facishare.paas.appframework.metadata.dto.tag.TagGroupTag
import com.facishare.paas.metadata.api.describe.ISubTagDescribe
import com.facishare.paas.metadata.api.describe.ITagDescribe
import com.facishare.paas.metadata.impl.describe.SubTagDescribe
import com.facishare.paas.metadata.impl.describe.TagDescribe
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * <AUTHOR> @date 2020/2/26 7:39 下午
 *
 */
class TagServiceTest extends Specification {
    ServiceFacade serviceFacade
    TagService tagService;
    ServiceContext serviceContext
    TagLogicService tagLogicService = Mock(TagLogicService.class)
    DescribeLogicService describeLogicService = Mock(DescribeLogicService.class)
    OrgService orgService = Mock(OrgService.class)

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        tagService = new TagService()
        tagService.tagLogicService = tagLogicService
        tagService.describeLogicService = describeLogicService
        tagService.orgService = orgService
        serviceContext = new ServiceContext(
                RequestContext.builder().user(new User("78057", "1000")).build(),
                "", "")
    }

    def "test find all tags"() {
        expect:
        1 == 1
        /*
        given:
        def arg = new ListAllTags.Arg(name)

        when:
        def result = tagService.findAllTagsByDescribeApiName(arg, serviceContext)

        then:
        1 * serviceFacade.findTagsByRest(_, _, _) >> { return Lists.newArrayList(buildTagGroupTag()) }
        noExceptionThrown()
        JSON.toJSONString(result) == json

        where:
        name  | json
        "lab" | '''{"groups":[{"tag_group_name":"default","tag_names":["label1","labor"]}]}'''
        */

    }

    def "test find data tags"() {
        expect:
        1 == 1
        /*
        given:
        def arg = new FindDataTags.Arg(describeApiName, dataId)

        when:
        def result = tagService.findDataTags(arg, serviceContext)

        then:
        1 * serviceFacade.findTagsByDataId(_, _, _) >> { return buildTagDescribes() }
        1 * serviceFacade.findTagGroupsByIds(_, _, _) >> { return buildTagGroups() }
        noExceptionThrown()

        where:
        describeApiName | dataId
        "AccountObj"    | "**********"
        */
    }

    def buildTagGroupTag() {
        TagGroupTag tag = new TagGroupTag()
        tag.setTagGroupName("default")
        tag.setTagNames(Lists.newArrayList("label1", "labor"))
        return Lists.newArrayList(tag)
    }

    def buildTagDescribes() {
        ISubTagDescribe tag = new SubTagDescribe()
        tag.setName("tag1")
        tag.setDescribeApiName("AccountObj")
        tag.setGrade(1)
        tag.setId("123")
        tag.setTagId("456")
        return Lists.newArrayList(tag)
    }

    def buildTagGroups() {
        ITagDescribe group = new TagDescribe()
        group.setId("456")
        group.setDescribeApiName("AccountObj")
        group.setType("默认分组")
        return Lists.newArrayList(group)
    }

}
