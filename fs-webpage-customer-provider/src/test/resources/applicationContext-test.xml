<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>

    <context:component-scan base-package="com.facishare.webpage.customer"/>
    <bean id="webPageHttpClient" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>

    <import resource="classpath:spring/fs-webpage-customer-aop.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-dao.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-service.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-servlet.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-dubbo.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-sys-service.xml"/>
    <import resource="classpath:enterpriserelation/enterpriserelation-objrest.xml"/>
    <import resource="classpath:spring/spring-cms.xml"/>
    <import resource="classpath:META-INF/spring/objgroup-common.xml"/>
    <import resource="classpath:META-INF/spring/fs-qixin-common-http.xml"/>
    <import resource="classpath:META-INF/spring/fs-qixin-common-log.xml"/>
    <import resource="classpath:META-INF/spring/fs-qixin-enterprise-converter.xml"/>
    <import resource="classpath:META-INF/spring/fs-qixin-permission-crm.xml"/>
    <import resource="classpath:/META-INF/spring/fs-qixin-permission.xml"/>
    <import resource="classpath:enterpriserelation2/enterpriserelation-noappid.xml"/>
    <import resource="classpath:fs-qixin-enterpriserelation.xml"/>
    <import resource="classpath:spring/fs-qixin-relation-rest.xml"/>
    <import resource="classpath:spring/license-client.xml"/>

    <import resource="classpath:springmvc-resteasy.xml"/>
    <!-- 动态调整日志级别 -->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <import resource="classpath:spring/fs-webpage-customer-designer.xml"/>
    <import resource="classpath:appcenterrest/appcenterrest.xml"/>
    <import resource="classpath:fs-uc-rest.xml"/>
    <import resource="classpath:fs-uc-cache-no-dubbo.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-rest-remote.xml"/>
    <!-- 数据引用关系 -->
    <import resource="classpath:fs-qixin-reference-relationship.xml"/>
    <!-- CRM通知服务 -> 梁岩超 -->
    <import resource="classpath:META-INF/spring/fs-crm-notify-rest-api.xml"/>
    <!-- 文件服务 -->
    <import resource="classpath:spring/fs-warehouse-rest-client.xml"/>
    <!-- 功能权限相关 -->
    <import resource="classpath:spring/fs-webpage-permission.xml"/>
    <import resource="classpath:spring/fs-webpage-customer-redis.xml"/>

    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service-forstone.xml"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <!-- bizConf -> 田文杰 -->
    <import resource="classpath:/fs-paas-bizconf-client.xml"/>
    <!-- 功能权限接口 -> 买年顺 -->
    <import resource="classpath:paasauthrest/paasauthrest.xml"/>
    <import resource="classpath:crmrest/crmrest-not-seialize-nulls.xml"/>
    <import resource="classpath:META-INF/spring/fs-crm-notify-remind-mq.xml"/>

    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
    <import resource="classpath:spring/fs-qixin-sysdb.xml"/>
    <import resource="classpath:/spring/fs-paas-auth-client.xml"/>

    <import resource="classpath:META-INF/spring/fs-user-extension-rest-api.xml"/>
    <import resource="classpath:META-INF/spring/fs-user-extension-rest-client.xml"/>
    <import resource="classpath:spring/fs-paas-service-api-support.xml"/>

</beans>
