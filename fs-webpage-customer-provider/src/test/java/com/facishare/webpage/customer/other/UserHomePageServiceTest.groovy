package com.facishare.webpage.customer.other

import com.facishare.cep.plugin.model.OuterUserInfo
import com.facishare.cep.plugin.model.UserInfo
import com.facishare.webpage.customer.api.InterErrorCode
import com.facishare.webpage.customer.api.model.HomePageLayoutCard
import com.facishare.webpage.customer.api.model.HomePageLayoutFilter
import com.facishare.webpage.customer.api.model.HomePageLayoutTO

import com.facishare.webpage.customer.api.model.result.BaseApiResult
import com.facishare.webpage.customer.api.service.TenantPageTempleService
import com.facishare.webpage.customer.controller.impl.UserHomePageActionImpl
import com.facishare.webpage.customer.dao.HomePageLayoutDaoImpl
import com.facishare.webpage.customer.controller.model.arg.homepage.GetUserHomePageLayoutArg
import com.facishare.webpage.customer.service.impl.HomePageBaseServiceImpl
import com.github.fakemongo.junit.FongoRule
import com.google.common.collect.Lists
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia
import spock.lang.Shared
import spock.lang.Specification

/**
 * Created by zhangyu on 2019/9/23
 */
class UserHomePageServiceTest extends Specification {

    public final FongoRule fongoRule = new FongoRule(false);

    private Datastore datastore;

    private UserHomePageActionImpl userHomePageAction

    private HomePageBaseServiceImpl homePageBaseService;

    private HomePageLayoutDaoImpl homePageLayoutDao;

    def tenantPageTempleService = Mock(TenantPageTempleService.class)

    private ExpectedException exception = ExpectedException.none();

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule);

    @Shared
    HomePageLayoutTO homePageLayout;

    def setup() {

        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test")
        homePageLayoutDao = new HomePageLayoutDaoImpl()
        homePageLayoutDao.setDatastore(datastore)
        homePageBaseService = new HomePageBaseServiceImpl()
        homePageBaseService.setHomePageLayoutDao(homePageLayoutDao)
        userHomePageAction = new UserHomePageActionImpl()
        userHomePageAction.setTenantPageTempleService(tenantPageTempleService)
        userHomePageAction.setHomePageBaseService(homePageBaseService)

        createMessage()
    }

    def createMessage() {
        def homePageLayoutTO = new HomePageLayoutTO()
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.setCardId("PS_Filter")
        homePageLayoutCard.setType(103)
        homePageLayoutCard.setTitle("商品/全部")
        homePageLayoutCard.setRow(0)
        homePageLayoutCard.setColumn(0)
        homePageLayoutCard.setWidth(4)
        homePageLayoutCard.setHeight(2)
        homePageLayoutCard.setMobileHeight(0)
        homePageLayoutCard.setOrder(1)
        def homePageLayoutFilter = new HomePageLayoutFilter()
        homePageLayoutFilter.setFilterKey("out_user_all_id")
        homePageLayoutFilter.setFilterMainID("out_user_all_id")
        homePageLayoutFilter.setFilterName("全部")
        homePageLayoutFilter.setObjectApiName("SPUObj")
        homePageLayoutCard.setHomePageLayoutFilters(Lists.newArrayList(homePageLayoutFilter))

        def homePageLayoutCard1 = new HomePageLayoutCard()
        homePageLayoutCard1.setCardId("PS_BI_CUSTOMER")
        homePageLayoutCard1.setType(2)
        homePageLayoutCard1.setTitle("图表")
        homePageLayoutCard1.setRow(0)
        homePageLayoutCard1.setColumn(1)
        homePageLayoutCard1.setWidth(1)
        homePageLayoutCard1.setHeight(1)
        homePageLayoutCard1.setMobileHeight(2)
        homePageLayoutCard1.setOrder(2)
        homePageLayoutTO.setHomePageLayouts(Lists.newArrayList(homePageLayoutCard,homePageLayoutCard1))
        homePageLayout = homePageBaseService.insertHomePageLayout("PRM", 2, 1001, homePageLayoutTO)

    }

    def "getUserHomePageByLayoutId"(){
        given:
        def layoutId = homePageLayout.layoutId
        def baseApiResult = new BaseApiResult<>()
        baseApiResult.setContent(true)
        tenantPageTempleService.checkUserPermission(*_) >>> [baseApiResult]

        def userInfo = new UserInfo()
        userInfo.setEnterpriseId(2)
        userInfo.setEmployeeId(1007)
        def outerUserInfo = new OuterUserInfo()
        outerUserInfo.setOutTenantId(2001)
        outerUserInfo.setOutUserId(10007)
        def arg = new GetUserHomePageLayoutArg()
        arg.layoutID = layoutId

        when:
        def result = userHomePageAction.getUserHomePageLayoutByLayoutId(userInfo, outerUserInfo, arg)
        then:
        homePageLayout == result.homePageLayout
    }

    def "getUserHomePageByLayoutId1"(){
        given:
        def layoutId = homePageLayout.layoutId
        def baseApiResult = new BaseApiResult<>()
        baseApiResult.setContent(false)
        tenantPageTempleService.checkUserPermission(*_) >>> [baseApiResult]

        def employeeId = 1007

        def userInfo = new UserInfo()
        userInfo.setEnterpriseId(2)
        userInfo.setEmployeeId(employeeId)
        def outerUserInfo = new OuterUserInfo()
        outerUserInfo.setOutTenantId(2001)
        outerUserInfo.setOutUserId(10007)
        def arg = new GetUserHomePageLayoutArg()
        arg.layoutID = layoutId

        when:

        homePageLayoutDao.makeHomePageFormal(layoutId, employeeId, UUID.randomUUID().toString(), appType)

        String exceptionMessage = ""
        try {
            def result = userHomePageAction.getUserHomePageLayoutByLayoutId(userInfo, outerUserInfo, arg)
        }catch(Exception e){
            exceptionMessage = e.message
        }
        then:
        exceptionMessage == InterErrorCode.IDENTITY_VERIFICATION_FAILED
    }



}
