package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSON;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.webpage.customer.core.model.DateFilter;
import com.fxiaoke.bizconf.arg.BatchQueryConfigArg;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.bean.ValueType;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

/**
 * Created by z<PERSON><PERSON> on 2021/2/1
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:/fs-paas-bizconf-client.xml")
public class BizConfServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private BizConfClient bizConfClient;

    @Test
    public void createConfig() throws FRestClientException {
        DateFilter dateFilter = new DateFilter();
        dateFilter.setEndTime("1");
        dateFilter.setStartTime("1");
        ConfigArg arg = new ConfigArg();
        arg.setKey("fadada-accessToken");
        arg.setValueType(ValueType.JSON);
        arg.setRank(Rank.TENANT);
        arg.setPkg("CRM");
        arg.setValue(JSON.toJSONString(dateFilter));
        arg.setTenantId("1");
        int config = bizConfClient.createConfig(arg);
        System.out.println(config);
    }

    @Test
    public void upsertConfig() {
        
    }

    @Test
    public void queryConfig() throws FRestClientException {
        QueryConfigByRankArg arg = new QueryConfigByRankArg();
        arg.setKey("fadada-person-79410");
        arg.setPkg("CRM");
        arg.setTenantId("79410");
        arg.setRank(Rank.TENANT);
        String result = bizConfClient.queryConfigByRank(arg);
        System.out.println(result);
    }

    @Test
    public void batchQueryConfig() throws FRestClientException {
        BatchQueryConfigArg arg = new BatchQueryConfigArg();
        arg.setTenantId("74164");
        arg.setKeyList(Lists.newArrayList("eservice_cases_data_overview"));
        arg.setPkg("CRM");
        arg.setRank(Rank.TENANT);
        Map<String, String> map = bizConfClient.batchQueryConfig(arg);
        System.out.println(map);
    }
}
