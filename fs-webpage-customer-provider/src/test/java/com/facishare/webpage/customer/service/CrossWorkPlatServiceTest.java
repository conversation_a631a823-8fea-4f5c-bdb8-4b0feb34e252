package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.constant.WebPageConstants;
import com.fxiaoke.enterpriserelation2.arg.ListBenchAppsArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.LinkAppData;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Yu
 * @date 2021/9/14 3:10 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:enterpriserelation2/enterpriserelation-noappid.xml")
public class CrossWorkPlatServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private UpstreamService upstreamService;

    @Test
    public void listBenchApps() {
        HeaderObj headerObj = HeaderObj.newInstance(71574);
        headerObj.setAppId(WebPageConstants.CROSS_APPID);

        ListBenchAppsArg arg = new ListBenchAppsArg();
        arg.setTenantId(71574);
        arg.setEmployeeId(-10000);
        RestResult<List<LinkAppData>> listRestResult = upstreamService.listBenchApps(headerObj, arg);
        System.out.println(listRestResult.getData());
    }
}
