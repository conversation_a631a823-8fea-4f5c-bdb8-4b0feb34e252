package com.facishare.webpage.customer.test;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Data;
import org.junit.Test;

import java.util.List;

/**
 * Created by zhangyu on 2019/11/13
 */
public class TestBoolean {
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    @Data
    public class HomePageDefaultValue{
        private Boolean isAll;
    }

    @Test
    public void test(){
        HomePageDefaultValue homePageDefaultValue = new HomePageDefaultValue();
        homePageDefaultValue.setIsAll(false);

        HomePageDefaultValue homePageDefaultValue1 = new HomePageDefaultValue();
        homePageDefaultValue1.setIsAll(true);
        List<HomePageDefaultValue> list = Lists.newArrayList();
        list.add(homePageDefaultValue);
        list.add(homePageDefaultValue1);
        System.out.println(list);
    }

}
