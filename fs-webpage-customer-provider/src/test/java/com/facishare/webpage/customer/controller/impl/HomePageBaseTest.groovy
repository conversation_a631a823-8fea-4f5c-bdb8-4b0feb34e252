package com.facishare.webpage.customer.controller.impl

import com.facishare.organization.adapter.api.permission.service.PermissionService
import com.facishare.qixin.objgroup.common.service.PaasOrgGroupService
import com.facishare.webpage.customer.api.model.HomePageLayoutCard
import com.facishare.webpage.customer.api.model.HomePageLayoutTO
import com.facishare.webpage.customer.api.model.Scope
import com.facishare.webpage.customer.config.DefaultTenantConfig
import com.facishare.webpage.customer.config.HomePageMaxConfig
import com.facishare.webpage.customer.core.business.ComponentListManager
import com.facishare.webpage.customer.core.util.BIUrlUtil
import com.facishare.webpage.customer.dao.HomePageLayoutDaoImpl
import com.facishare.webpage.customer.remote.ObjectService
import com.facishare.webpage.customer.remote.RoleNameService
import com.facishare.webpage.customer.remote.impl.ObjectServiceImpl
import com.facishare.webpage.customer.remote.impl.RoleNameServiceImpl
import com.facishare.webpage.customer.service.HomePageBaseService
import com.facishare.webpage.customer.service.RemoteService
import com.facishare.webpage.customer.service.UserHomePageBaseService
import com.facishare.webpage.customer.service.impl.HomePageBaseServiceImpl
import com.facishare.webpage.customer.service.impl.RemoteServiceImpl
import com.facishare.webpage.customer.service.impl.UserHomePageBaseServiceImpl
import com.github.fakemongo.junit.FongoRule
import com.google.common.collect.Lists
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia
import spock.lang.Specification

/**
 * Created by zhangyu on 2020/3/13
 */
class HomePageBaseTest extends Specification {

    public final FongoRule fongoRule = new FongoRule(false);
    private Datastore datastore;

    private HomePageBaseServiceImpl homePageBaseService;
    private UserHomePageBaseServiceImpl userHomePageBaseService;
    private ObjectServiceImpl objectService
    private RoleNameServiceImpl roleNameService
    private HomePageLayoutDaoImpl homePageLayoutDao
    def remoteService = Mock(RemoteService.class);
    def homePageMaxConfig = Mock(HomePageMaxConfig.class);
    def permissionService = Mock(PermissionService.class);
    def defaultTenantConfig = Mock(DefaultTenantConfig.class);
    def biUrlUtil = Mock(BIUrlUtil.class);
    def paasOrgGroupService = Mock(PaasOrgGroupService.class);
    def componentListManager = Mock(ComponentListManager.class);

    public int tenantId = 71574
    public int employeeId = 1000
    public String name = "测试首页"
    public String description = "首页描述"
    public String dataId = "999999"
    public String dataType = 2
    public int status = 1
    public int layoutType = 2
    public boolean isSystem = false
    public String cardId = "BI_5dc138db6eafd00001eeba82"
    public int type = 1
    public String title = "回款目标完成情况"
    public int row = 0
    public int column = 0
    public int order = 1
    public int width = 8
    public int height = 2
    public int mobileHeight = 0
    public String appId = "CRM"


    private ExpectedException exception = ExpectedException.none()

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule)

    def setup() {
        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test-pageTemplate")

        homePageBaseService = new HomePageBaseServiceImpl()
        homePageLayoutDao.datastore = datastore
        userHomePageBaseService = new UserHomePageBaseServiceImpl()
        homePageMaxConfig = homePageMaxConfig
        remoteService = new RemoteServiceImpl()
        defaultTenantConfig = new DefaultTenantConfig()
        biUrlUtil = new BIUrlUtil()
        objectService = new ObjectServiceImpl()
        roleNameService = new RoleNameServiceImpl()


        createMessage()
    }

    private HomePageLayoutTO buildHomePageLayoutTO(){
        def homePageLayoutCard = new HomePageLayoutCard()
        homePageLayoutCard.cardId = cardId
        homePageLayoutCard.type = type
        homePageLayoutCard.appId = appId
        homePageLayoutCard.column = column
        homePageLayoutCard.row = row
        homePageLayoutCard.height = height
        homePageLayoutCard.mobileHeight = mobileHeight
        homePageLayoutCard.order = order
        homePageLayoutCard.width = width
        homePageLayoutCard.title = title
        def scope = new Scope()
        scope.dataType = dataType
        scope.dataId = dataId
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.name = name
        homePageLayout.description = description
        homePageLayout.scopes = Lists.newArrayList(scope)
        homePageLayout.status = status
        homePageLayout.layoutType = layoutType
        homePageLayout.homePageLayouts = Lists.newArrayList(homePageLayoutCard)
        homePageLayout.isSystem = isSystem
        return homePageLayout
    }

}
