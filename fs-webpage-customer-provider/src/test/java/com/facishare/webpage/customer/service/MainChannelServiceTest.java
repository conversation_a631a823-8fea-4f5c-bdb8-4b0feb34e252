package com.facishare.webpage.customer.service;


import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.model.MainChannelMenu;
import com.facishare.webpage.customer.api.model.MainChannelMenuVO;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.service.MainChannelService;
import com.facishare.webpage.customer.api.service.TenantMenuRestService;
import com.facishare.webpage.customer.config.MainChannelMenuIconConfig;
import com.facishare.webpage.customer.config.model.IconData;
import com.facishare.webpage.customer.controller.*;
import com.facishare.webpage.customer.controller.model.ManageWebMainChannelVO;
import com.facishare.webpage.customer.controller.model.arg.GetIconListArg;
import com.facishare.webpage.customer.controller.model.arg.component.GetMenuDropListArg;
import com.facishare.webpage.customer.controller.model.arg.component.GetUtilityBarDropListArg;
import com.facishare.webpage.customer.controller.model.arg.homepage.GetDropListItemsArg;
import com.facishare.webpage.customer.controller.model.arg.homepage.GetUserCustomerLayoutArg;
import com.facishare.webpage.customer.controller.model.arg.menu.*;
import com.facishare.webpage.customer.controller.model.arg.paas.SetApathToCpathArg;
import com.facishare.webpage.customer.controller.model.result.component.GetMenuDropListResult;
import com.facishare.webpage.customer.controller.model.result.component.GetUtilityBarDropListResult;
import com.facishare.webpage.customer.controller.model.result.homepage.GetDropListItemsResult;
import com.facishare.webpage.customer.controller.model.result.homepage.GetUserCustomerLayoutResult;
import com.facishare.webpage.customer.controller.model.result.menu.*;
import com.facishare.webpage.customer.controller.model.result.menu.MenuListResult;
import com.facishare.webpage.customer.controller.model.result.paas.GetAppListResult;
import com.facishare.webpage.customer.controller.model.result.paas.GetIconResult;
import com.facishare.webpage.customer.controller.model.result.paas.GetPaaSIconResult;
import com.facishare.webpage.customer.remote.TempFileToFormalFile;
import com.facishare.webpage.customer.rest.UserWebMainChannelRestAction;
import com.facishare.webpage.customer.rest.WebPageRestAction;
import com.facishare.webpage.customer.util.GeneralUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static org.junit.Assert.assertNotNull;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class MainChannelServiceTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private MainChannelService mainChannelService;

    @Resource
    private UserWebMainChannelRestAction userWebMainChannelRestAction;

    @Autowired
    private DropListAction dropListAction;

    @Autowired
    private TenantHomePageAction tenantHomePageAction;

    @Resource
    private WebPageRestAction webPageRestAction;

    @Autowired
    private WebMainChannelService webMainChannelService;

    @Autowired
    private PaaSAppAction paaSAppAction;

    @Autowired
    private TenantMenuAction tenantMenuAction;

    @Autowired
    private UserHomePageAction userHomePageAction;

    @Autowired
    private UserMenuAction userMenuAction;

    @Autowired
    private TenantMenuRestService tenantMenuRestService;

    @Autowired
    private IconAction iconAction;

    @Autowired
    private LinkAppAction linkAppAction;
    @Resource
    private TempFileToFormalFile tempFileToFormalFile;


    @Test
    public void testQueryMainChannelMenu() {
        QueryMainChannelMenuArg arg = new QueryMainChannelMenuArg();
        arg.setTenantId(71574);
        arg.setEnterpriseAccount("fsceshi003");
        arg.setEmployeeId(1041);
        arg.setLocale(Locale.CHINESE);
        List<MainChannelMenu> result = mainChannelService.queryMainChannelMenu(arg);
    }

    @Test
    public void queryUserWebMainChannelVO() {
        QueryUserWebMainChannelArg arg = new QueryUserWebMainChannelArg();
        arg.setEmployeeId(1000);
        arg.setEnterpriseAccount("78810");
        QueryUserWebMainChannelResult ret = userWebMainChannelRestAction.queryUserWebMainChannelVO(
                "78810", null, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getTenantMainChannelMenuVO() {
        QueryUserWebMainChannelArg arg = new QueryUserWebMainChannelArg();
        arg.setEmployeeId(1001);
        arg.setEnterpriseAccount("78810");
        ManageWebMainChannelVO ret = webMainChannelService.getTenantMainChannelMenuVO(78810, "78810", 1000,
                "MainChannel_788101093419876__c", Locale.CHINESE);

        System.out.println(JSONObject.toJSONString(ret));

    }

    @Test
    public void queryMainChannelMenuVOList() {
        QueryUserWebMainChannelArg arg = new QueryUserWebMainChannelArg();
        arg.setEmployeeId(1000);
        arg.setEnterpriseAccount("78810");
        List<MainChannelMenuVO> ret = webMainChannelService.queryMainChannelMenuVOList(78810, "78810", 1000, Locale.CHINESE);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getMenuDropList() {
        GetMenuDropListArg arg = new GetMenuDropListArg();
        //渠道门户FSAID_11491009
        //代理通FSAID_11490d9e74255
        arg.setAppId("FSAID_11491096");
        arg.setAppType(2);
        arg.setDataSourceEnv(false);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1000, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetMenuDropListResult ret = dropListAction.getMenuDropList(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }
    @Test
    public void getMenuDropListbur() {
        GetUtilityBarDropListArg arg = new GetUtilityBarDropListArg();
        //渠道门户FSAID_11491009
        //代理通FSAID_11490d9e
        arg.setAppId("CROSS_PaaS");
        arg.setAppType(2);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetUtilityBarDropListResult ret = dropListAction.getUtilityBarDropList(userInfo, clientInfo, arg);
        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getDropListItems() {
        GetDropListItemsArg arg = new GetDropListItemsArg();
        arg.setAppId("FSAID_11490d9e");
        arg.setAppType(2);
        //OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetDropListItemsResult ret = tenantHomePageAction.getDropListItems(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getPageListItems() {
        GetDropListItemsArg arg = new GetDropListItemsArg();
        arg.setAppType(4);
        arg.setAppId("ObjectDetailPage_object_PA3jk__c");
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(78436, "78436", 1000);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetDropListItemsResult ret = tenantHomePageAction.getDropListItems(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void modifyHomePageLayout() {
        ModifyHomePageLayoutArg arg = new ModifyHomePageLayoutArg();
        arg.setAppId("CRM");
        arg.setAppType(1);
        String configString = "{\"LayoutID\":\"80941_e445b93285df4c66a593309c740f2d92\",\"Name\":\"工作圈\",\"Description\":\"\",\"Scope\":[{\"DataID\":999999,\"DataType\":2}],\"Status\":1,\"LayoutType\":\"2\",\"HomePageLayouts\":[{\"CardID\":\"BI_SaleReport\",\"apiName\":\"BI_SaleReport\",\"Type\":1,\"Title\":\"销售简报\",\"Row\":0,\"Column\":0,\"Width\":0,\"Height\":0,\"MobileHeight\":0,\"URL\":\"https://www.ceshi112.com/h5app/bi-report?fromapp=1#/report/salesbrief\",\"Order\":1},{\"CardID\":\"PS_Tool\",\"apiName\":\"PS_Tool-CRM\",\"HomePageLayoutTools\":[{\"IsShow\":true,\"ToolID\":\"Tool_Duplicate\",\"ToolName\":\"查重\",\"ToolType\":3,\"URL\":\"\"},{\"IsShow\":true,\"ToolID\":\"Tool_Import\",\"ToolName\":\"导入\",\"ToolType\":3,\"URL\":\"\"},{\"IsShow\":true,\"ToolID\":\"SFA_Tool_SearchPrice\",\"ToolName\":\"询价工具\",\"ToolType\":3,\"URL\":\"\"}],\"Type\":104,\"Title\":\"工具\",\"Row\":0,\"Column\":0,\"Width\":0,\"Height\":0,\"MobileHeight\":0,\"URL\":\"\",\"Order\":2}],\"IsCurrentLayout\":false,\"IsSystem\":false,\"customerLayout\":{\"layout\":[{\"components\":[[\"filters\",\"BI_SaleReport\",\"PS_Tool-CRM\"],[\"work\"]],\"columns\":[{\"width\":\"34%\"},{\"width\":\"66%\"}]}],\"components\":[{\"buttons\":[],\"filterData\":[{\"filterData\":\"{\\\"type\\\":3,\\\"canEdit\\\":true}\",\"filterType\":\"selector\"},{\"filterData\":\"{\\\"startTime\\\":0,\\\"endTime\\\":0,\\\"dateId\\\":4,\\\"dateType\\\":\\\"本月\\\",\\\"canEdit\\\":true}\",\"filterType\":\"date\"},{\"filterData\":\"{\\\"enableEmpFilterOfGlobalFilter\\\":1,\\\"enableDateFilterOfGlobalFilter\\\":1,\\\"enablePresetEmpFilterOfGlobalFilter\\\":1,\\\"enablePresetDateFilterOfGlobalFilter\\\":1}\",\"filterType\":\"pageDefault\"}],\"related_list_name\":\"\",\"nameI18nKey\":\"webpage_homepage.tool\",\"title\":\"工具\",\"layoutCardId\":\"PS_Tool-CRM\",\"type\":\"tools\",\"layoutId\":\"80941_e445b93285df4c66a593309c740f2d92\",\"tools\":[{\"ToolID\":\"Tool_Duplicate\",\"ToolName\":\"查重\",\"ToolType\":3,\"IsShow\":true,\"URL\":\"\"},{\"ToolID\":\"Tool_Import\",\"ToolName\":\"导入\",\"ToolType\":3,\"IsShow\":true,\"URL\":\"\"},{\"ToolID\":\"SFA_Tool_SearchPrice\",\"ToolName\":\"询价工具\",\"ToolType\":3,\"IsShow\":true,\"URL\":\"\"}],\"propsType\":104,\"dataId\":\"PS_Tool\",\"api_name\":\"PS_Tool-CRM\",\"cardId\":\"PS_Tool\",\"appId\":\"CRM\",\"header\":\"工具\",\"limit\":1},{\"buttons\":[],\"dataId\":\"BI_SaleReport\",\"api_name\":\"BI_SaleReport\",\"related_list_name\":\"\",\"cardId\":\"BI_SaleReport\",\"header\":\"销售简报\",\"nameI18nKey\":\"webpage_homepage.sale_report\",\"title\":\"销售简报\",\"type\":\"saleReport\",\"propsType\":1,\"limit\":1},{\"buttons\":[],\"api_name\":\"work\",\"related_list_name\":\"\",\"header\":\"工作圈\",\"nameI18nKey\":\"paas.udobj.work_component\",\"style\":{\"height\":\"500px\"},\"localKeys\":[],\"fixed\":\"bottom\",\"title\":\"工作圈\",\"limit\":1,\"type\":\"work\"},{\"buttons\":[],\"related_list_name\":\"\",\"nameI18nKey\":\"webpage_homepage.filter\",\"title\":\"筛选器\",\"dataId\":\"filters\",\"api_name\":\"filters\",\"cardId\":\"filters\",\"header\":\"筛选器\",\"localKeys\":[\"customEventId\",\"getComponentsData\"],\"customEventId\":1656923797681,\"limit\":1,\"type\":\"filters\"}],\"filters\":[{\"filterData\":\"{\\\"type\\\":3,\\\"canEdit\\\":true}\",\"filterType\":\"selector\"},{\"filterData\":\"{\\\"startTime\\\":0,\\\"endTime\\\":0,\\\"dateId\\\":4,\\\"dateType\\\":\\\"本月\\\",\\\"canEdit\\\":true}\",\"filterType\":\"date\"},{\"filterData\":\"{\\\"enableEmpFilterOfGlobalFilter\\\":1,\\\"enableDateFilterOfGlobalFilter\\\":1,\\\"enablePresetEmpFilterOfGlobalFilter\\\":1,\\\"enablePresetDateFilterOfGlobalFilter\\\":1}\",\"filterType\":\"pageDefault\"}],\"globalSettings\":{}},\"dataVersion\":200,\"pageLayoutType\":\"4\",\"createTime\":0,\"updateTime\":0,\"appId\":\"CRM\",\"priorityLevel\":0,\"appType\":1}";
        HomePageLayoutTO homePageLayoutTO = JSONObject.parseObject(configString, HomePageLayoutTO.class);
        homePageLayoutTO.setLayoutType(2);
        arg.setHomePageLayout(homePageLayoutTO);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(80941, "80941", 1003);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        ModifyHomePageLayoutResult ret = tenantHomePageAction.modifyHomePageLayout(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getHomePageLayoutByApiName() {
        GetHomePageLayoutByIdArg arg = new GetHomePageLayoutByIdArg();
        arg.setLayoutApiName("FSAID_11490c84-shopping");
        arg.setAppType(6);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(79382, "79382", 1000);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetHomePageLayoutByIdResult ret = tenantHomePageAction.getHomePageLayoutById(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getHomePageLayoutById() {
        GetHomePageLayoutByIdArg arg = new GetHomePageLayoutByIdArg();
        arg.setLayoutId("71574_5bad24775ed74bf7879d148cdc347964");
        arg.setAppType(1);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(85407, "85407", 1000);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetHomePageLayoutByIdResult ret = tenantHomePageAction.getHomePageLayoutById(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getUserCustomerLayout() {
        GetUserCustomerLayoutArg arg = new GetUserCustomerLayoutArg();
        arg.setLayoutApiName("layout_ANrk5X5R5t__c");
        arg.setAppType(4);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(85188, "85188", 1000);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetUserCustomerLayoutResult ret = userHomePageAction.getUserCustomerLayout(userInfo, outerUserInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getUserCRMLayout() {
        GetUserCustomerLayoutArg arg = new GetUserCustomerLayoutArg();
        arg.setLayoutId("71574_4bd4a2c541b6489aa9e80aed6ed8f059");
        arg.setAppType(1);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1000);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.ENGLISH);
        GetUserCustomerLayoutResult ret = userHomePageAction.getUserCustomerLayout(userInfo, outerUserInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void queryCustomerMenus() {
        QueryCustomerMenusArg arg = QueryCustomerMenusArg.builder().enterpriseId(71574).inner(true).build();
        QueryCustomerMenusResult ret = webPageRestAction.queryCustomerMenus("71574", arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getAppList() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1017);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINA);
        GetAppListResult ret = paaSAppAction.getAppList(userInfo, clientInfo);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getPaaSIconList(){
        UserInfo userInfo = GeneralUtil.buildUserInfo(85745, "85745", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.ENGLISH);
        GetPaaSIconResult ret = paaSAppAction.getPaaSIconList(userInfo);

        System.out.println(JSONObject.toJSONString(ret));
    }
    @Test
    public void setPaaSIconList() throws InterruptedException {
        UserInfo userInfo = GeneralUtil.buildUserInfo(85745, "85745", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.ENGLISH);
        SetApathToCpathArg arg = new SetApathToCpathArg();
        arg.setGrayTenantId(true);
        GetPaaSIconResult ret = null;
        try {
            paaSAppAction.setPaasIconToCPath(arg);
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        System.out.println(JSONObject.toJSONString(ret));
    }
    @Test
    public void getCpathFromApath(){
        String cPath = tempFileToFormalFile.copyFileToCpath("79337", 1000, "N_202207_15_dc2ede8e16df4e649f8448e52321573a");
        System.out.println(cPath);
    }
    @Test
    public void getCusIconList(){
        UserInfo userInfo = GeneralUtil.buildUserInfo(85745, "85745", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.ENGLISH);
        GetIconListArg arg = new GetIconListArg();
        List<String> l = Arrays.asList("Customer-obj");
        arg.setScopeList(l);
        GetIconResult ret = iconAction.getIconList(userInfo,arg);

        System.out.println(ret.getIconDataList().toString());

        System.out.println(JSONObject.toJSONString(ret));
    }
    @Test
    public void setCusIconList(){
        Map<String, IconData> iconDataMap =  MainChannelMenuIconConfig.queryIconMessage();
        for (Map.Entry<String, IconData> entry : iconDataMap.entrySet()) {
            if(entry.getKey().startsWith("paasApp-") || entry.getKey().startsWith("objApp-")){
                entry.getValue().setFacialIcon(entry.getValue().getGrayIcon().replaceAll("gray","facial"));
            }
            entry.getValue().setIconIndex(null);
        }
        System.out.println(JSONObject.toJSONString(iconDataMap));
  }

    @Test
    public void getMenuById() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(88242, "88242", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetMenuByIdArg arg = new GetMenuByIdArg();
        arg.setMenuId("88242_ba078958d0b84facaf173596aed8e623");
        GetMenuByIdResult ret = tenantMenuAction.getMenuById(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    /**
     * 代理通自定义首页获取菜单
     */
    @Test
    public void getCrossMenuById() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetCrossMenuByIdArg arg = new GetCrossMenuByIdArg();
        arg.setMenuTempleId("71574_c71f5a1c8fde41aea80d7194671c4790");
        GetCrossMenuByIdResult ret = tenantMenuAction.getCrossMenuById(userInfo, clientInfo, arg);
        System.out.println(ret);
    }

    @Test
    public void menuList() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        MenuListArg arg = new MenuListArg();
        MenuListResult ret = tenantMenuAction.menuList(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getUserMenus() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Android);
        clientInfo.setLocale(Locale.CHINESE);
        GetUserMenusArg arg = new GetUserMenusArg();
        GetUserMenusResult ret = userMenuAction.getUserMenus(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getUserMenuByAppId() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        OuterUserInfo outerUserInfo = new OuterUserInfo();
        outerUserInfo.setOutTenantId(300090620L);
        outerUserInfo.setOutUserId(300013128L);
        outerUserInfo.setAppId("FSAID_11491009");
        outerUserInfo.setIdentityType(1);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetUserMenuByAppIdArg arg = new GetUserMenuByAppIdArg();
        arg.setId("71574_98e690e05c414d199f58460cc55c8e7c");
        GetUserMenuByAppIdResult ret = userMenuAction.getUserMenuByAppId(userInfo, clientInfo, outerUserInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void queryCommonlyUsedMenu() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.iOS);
        clientInfo.setLocale(Locale.CHINESE);
        QueryCommonlyUsedMenuArg arg = new QueryCommonlyUsedMenuArg();
        CommonlyUsedMenuResult ret = userMenuAction.queryCommonlyUsedMenu(userInfo, clientInfo, arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getMenuByIdRest() {
        GetMenuByIdRestArg arg = new GetMenuByIdRestArg();

        arg.setType(ClientTypeEnum.Web);
        arg.setLocale(Locale.CHINESE);
        arg.setEmployeeId(1017);
        arg.setMenuId("71574_881c4ba7b25943e8a503993a853a2ed3");
        GetMenuByIdRestResult ret = tenantMenuRestService.getMenuById("71574", arg);

        System.out.println(JSONObject.toJSONString(ret));
    }

    @Test
    public void getUserMenuByIdRest() throws Exception {
        GetUserMenuByIdRestArg getUserMenuByIdRestArg = new GetUserMenuByIdRestArg();
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", 1017);
        getUserMenuByIdRestArg.setEmployeeId(userInfo.getEmployeeId());
        getUserMenuByIdRestArg.setEnterpriseId(userInfo.getEnterpriseId());
        getUserMenuByIdRestArg.setEnterpriseAccount(userInfo.getEnterpriseAccount());
        getUserMenuByIdRestArg.setType(ClientTypeEnum.Web);
        getUserMenuByIdRestArg.setLocale(Locale.CHINESE);
        getUserMenuByIdRestArg.setMenuId("71574_9ca79fc8d786459991e3e2f39594e64e");
        getUserMenuByIdRestArg.setPageTemplateType(2);
        GetUserMenuByIdRestResult result = tenantMenuRestService.getUserMenuById(String.valueOf(getUserMenuByIdRestArg.getEnterpriseId()), getUserMenuByIdRestArg);
        assertNotNull(result);
    }

}
