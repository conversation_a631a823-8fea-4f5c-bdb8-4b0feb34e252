package com.facishare.webpage.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.CustomerMenuAction;
import com.facishare.webpage.customer.controller.UserHomePageAction;
import com.facishare.webpage.customer.controller.UserMenuAction;
import com.facishare.webpage.customer.controller.model.arg.customer.GetCustomerMenuListArg;
import com.facishare.webpage.customer.controller.model.arg.homepage.GetUserHomePageLayoutArg;
import com.facishare.webpage.customer.controller.model.arg.menu.GetPaaSUserMenusArg;
import com.facishare.webpage.customer.controller.model.result.homepage.GetUserHomePageLayoutResult;
import com.facishare.webpage.customer.controller.model.result.menu.GetPaaSUserMenusResult;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.facishare.webpage.customer.rest.HomePageRestAction;
import com.facishare.webpage.customer.rest.arg.GetHomePageByIdArg;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2021/4/6
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class ActionTest {

    private UserInfo userInfo;
    private ClientInfo clientInfo;

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private UserHomePageAction userHomePageAction;
    @Resource
    private UserMenuAction userMenuAction;
    @Resource
    private CustomerMenuService customerMenuService;
    @Resource
    private CustomerMenuAction customerMenuAction;
    @Resource
    private HomePageRestAction homePageRestAction;
    @PostConstruct
    private void init() {

        userInfo = new UserInfo();
        userInfo.setEnterpriseId(71574);
        userInfo.setEnterpriseAccount("fsceshi003");
        userInfo.setEmployeeId(1017);

        clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setVersion("chrome");
        clientInfo.setLocale(Locale.CHINA);
    }

    @Test
    public void getUserHomePageLayoutByLayoutId() {
        OuterUserInfo outerUserInfo = new OuterUserInfo();
        outerUserInfo.setUpstreamOwnerId(85188);
        outerUserInfo.setOutUserId((long)*********);
        outerUserInfo.setOutTenantId((long)*********);

        GetUserHomePageLayoutArg arg = new GetUserHomePageLayoutArg();
        arg.setLayoutApiName("layout_ANrk5X5R5t__c");

        GetUserHomePageLayoutResult result = userHomePageAction.getUserHomePageLayoutByLayoutId(userInfo, outerUserInfo, clientInfo, arg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void testGetPaaSUserMenus() {
        GetPaaSUserMenusArg arg = new GetPaaSUserMenusArg();
        arg.setAppId(WebPageConstants.APP_CRM);
        GetPaaSUserMenusResult paaSUserMenus = userMenuAction.getPaaSUserMenus(userInfo, clientInfo, arg);
        assert paaSUserMenus != null;
        System.out.println();
    }

    @Test
    public void testQueryCustomerByType() {
        List<CustomerMenu> customerMenus = customerMenuService.queryCustomerByType(71574, null, 1, "FSAID_11490c84", "singleApp", Locale.CHINA, false);
        assert customerMenus != null;
        System.out.println();
    }

    @Test
    public void testGetCustomerMenuList() {
        GetCustomerMenuListArg arg = new GetCustomerMenuListArg();
        arg.setApplyType(1);
        customerMenuAction.getCustomerMenuList(userInfo, clientInfo, arg);
        System.out.println("dsdsa");
    }

    @Test
    public void testHomePageLayoutAfterAction(){
        GetHomePageByIdArg arg = new GetHomePageByIdArg();
        arg.setApiName("layout_mZR6xyXfrk__c");
        arg.setEnterpriseId(71574);
        arg.setOldEnterpriseId(81351);
        homePageRestAction.homePageLayoutAfterAction(arg);
        System.out.println("test end");
    }
}
