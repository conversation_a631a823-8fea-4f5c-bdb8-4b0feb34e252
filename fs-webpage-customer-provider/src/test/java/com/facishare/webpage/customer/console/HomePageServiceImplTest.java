package com.facishare.webpage.customer.console;

import com.facishare.webpage.customer.api.console.ConsoleHomePageService;
import com.facishare.webpage.customer.api.console.arg.QueryHomePageArg;
import com.facishare.webpage.customer.api.console.result.QueryHomePageResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class HomePageServiceImplTest {

    static {
        System.getProperties().setProperty("process.profile", "ceshi113");
    }

    @Autowired
    private ConsoleHomePageService consoleHomePageService;

    @Test
    public void testQueryHomePage() {
        QueryHomePageArg arg = new QueryHomePageArg();
        arg.setLayoutId("CRM_44a9fe59-c9b4-4bac-b63c-825cd1810c07");
        QueryHomePageResult result = consoleHomePageService.queryHomePage(arg);
        System.out.println(result);
    }
}