package com.facishare.webpage.customer.other

import com.facishare.cep.plugin.model.UserInfo
import com.facishare.webpage.customer.common.EmployeeConfigCommonServiceImpl
import com.facishare.webpage.customer.controller.impl.HomePageCommonActionImpl
import com.facishare.webpage.customer.dao.EmployeeConfigDaoImpl
import com.facishare.webpage.customer.dao.entity.EmployeeConfigEntity
import com.facishare.webpage.customer.api.model.EmployeeConfig
import com.facishare.webpage.customer.controller.model.arg.homepage.GetEmployeeConfigValueByKeysArg
import com.facishare.webpage.customer.controller.model.arg.homepage.SetEmployeeConfigValueArg
import com.facishare.webpage.customer.service.impl.EmployeeConfigBaseServiceImpl
import com.github.fakemongo.junit.FongoRule
import com.google.common.collect.Lists
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.junit.Rule
import org.junit.rules.ExpectedException
import org.junit.rules.RuleChain
import org.junit.rules.TestRule
import org.mongodb.morphia.Datastore
import org.mongodb.morphia.Morphia
import spock.lang.Shared
import spock.lang.Specification

/**
 * Created by zhangyu on 2019/11/4
 */
class HomePageCommonActionTest extends Specification {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    public final FongoRule fongoRule = new FongoRule(false);

    private Datastore datastore;

    private HomePageCommonActionImpl homePageCommonAction;

    private EmployeeConfigBaseServiceImpl employeeConfigBaseService;

    private EmployeeConfigDaoImpl employeeConfigDao;

    private EmployeeConfigCommonServiceImpl employeeConfigCommonService

    private ExpectedException exception = ExpectedException.none();

    public int tenantId = 71574

    public int employeeId = 1000

    public String layoutId = UUID.randomUUID().toString()

    @Rule
    TestRule rules = RuleChain.outerRule(exception).around(fongoRule);

    @Shared
    EmployeeConfigEntity employeeConfigEntity1
    @Shared
    EmployeeConfigEntity employeeConfigEntity2

    def setup() {

        Morphia morphia = new Morphia()
        datastore = morphia.createDatastore(fongoRule.getMongoClient(), "test")
        employeeConfigDao = new EmployeeConfigDaoImpl()
        employeeConfigDao.datastore = datastore
        employeeConfigBaseService = new EmployeeConfigBaseServiceImpl()
        employeeConfigBaseService.employeeConfigDao = employeeConfigDao
        homePageCommonAction = new HomePageCommonActionImpl()
        homePageCommonAction.employeeConfigBaseService = employeeConfigBaseService
        employeeConfigCommonService = new EmployeeConfigCommonServiceImpl()
        homePageCommonAction.employeeConfigCommonService = employeeConfigCommonService

        createMessageKey1()
        createMessageKey2()
    }

    def createMessageKey1() {
        def employeeConfig = new EmployeeConfig()
        employeeConfig.key = 1
        employeeConfig.value = "\"{\"dateId\":\"4\",\"empsAndDeps\":[],\"isAll\":true,\"startTime\":\"2019-11-01\",\"endTime\":\"2019-11-30\",\"dateType\":\"本月\"}\"";

        employeeConfigEntity1 = employeeConfigDao.upsertEmployeeConfig(tenantId, employeeId, layoutId, employeeConfig)
    }

    def createMessageKey2() {
        def employeeConfig = new EmployeeConfig()
        employeeConfig.key = 2
        employeeConfig.value = "\"{\"enableEmpFilterOfGlobalFilter\":1,\"enableDateFilterOfGlobalFilter\":1}\""

        employeeConfigEntity2 = employeeConfigDao.upsertEmployeeConfig(tenantId, employeeId, layoutId, employeeConfig)
    }

    def "getEmployeeConfigValueByKeys"() {
        given:
        def userInfo = new UserInfo()
        userInfo.employeeId = employeeId
        userInfo.enterpriseId = tenantId
        def arg = new GetEmployeeConfigValueByKeysArg()
        arg.layoutId = layoutId
        arg.keys = Lists.newArrayList(1, 2)
        arg.type = 0
        when:
        def result = homePageCommonAction.getEmployeeConfigValueByKeys(userInfo, arg)
        then:
        if (result.getConfigInfoList().get(0).key == 1) {
            employeeConfigEntity1.EValue == result.getConfigInfoList().get(0).value
            employeeConfigEntity2.EValue == result.getConfigInfoList().get(1).value
        } else {
            employeeConfigEntity1.EValue == result.getConfigInfoList().get(1).value
            employeeConfigEntity2.EValue == result.getConfigInfoList().get(0).value
        }
    }

    def "setEmployeeConfigValue" (){
        given:
        def userInfo = new UserInfo()
        userInfo.setEnterpriseId(tenantId)
        userInfo.setEmployeeId(employeeId)
        def arg = new SetEmployeeConfigValueArg()
        arg.layoutId = layoutId
        arg.key = 1
        arg.value = "\"{\"dateId\":\"4\",\"empsAndDeps\":[],\"isAll\":true,\"startTime\":\"2019-11-01\",\"endTime\":\"2019-11-30\",\"dateType\":\"本月\"}\""
        when:
        def employeeConfigValue = homePageCommonAction.setEmployeeConfigValue(userInfo, arg)
        def arg1 = new GetEmployeeConfigValueByKeysArg()
        arg1.layoutId = layoutId
        arg1.keys = Lists.newArrayList(1, 2)
        def result = homePageCommonAction.getEmployeeConfigValueByKeys(userInfo, arg1)
        then:
        employeeConfigValue.success == true
        if (result.getConfigInfoList().get(0).key == 1) {
            arg.value == result.getConfigInfoList().get(0).value
        } else {
            arg.value == result.getConfigInfoList().get(1).value
        }

    }

}
