package com.facishare.webpage.customer.test;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/6/14
 */
public class TestAddElement {


    @Test
    public void addElements() {

        /*Data data = new Data();
        data.setName("11");
        data.setNum(0);

        Data data1 = new Data();
        data1.setName("22");
        data1.setNum(1);

        Data data2 = new Data();
        data2.setName("33");
        data2.setNum(2);

        List<Data> dataList = Lists.newArrayList(data, data1, data2);

        String x = "mm";
        addElement(x, dataList);
        System.out.println(dataList);*/
    }

    private void addElement(String x, List<Data> list) {

        boolean flag = false;

        for (int i = 0; i < list.size(); i++) {

            if (flag) {
                Data data = list.get(i);
                int num = data.getNum();
                num++;
                data.setNum(num);
            }

            if (list.get(i).getName().equals("22")) {
                flag = true;
            }

        }

        /*Data data = new Data();
        data.setName(x);
        data.setNum(1);
        list.add(data);*/
    }

    @lombok.Data
    public static class Data implements Serializable {

        private String name;

        private int num;

        public Data(String name, int num) {
            this.name = name;
            this.num = num;
        }
    }

    @Test
    public void testSort() {
        Data data1 = new Data("张三", 1);
        Data data2 = new Data("李四", 2);
        Data data3 = new Data("王五", 3);

        List<Data> all = Lists.newArrayList(data3, data1, data2);

        List<Integer> nums = all.stream().map(x -> x.getNum()).collect(Collectors.toList());
        System.out.println(nums);

        Map<Integer, Data> dataMap = all.stream().collect(Collectors.toConcurrentMap(Data::getNum, x -> x));
        System.out.println(dataMap);
    }
}
