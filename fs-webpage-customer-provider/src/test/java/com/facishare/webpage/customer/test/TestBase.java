package com.facishare.webpage.customer.test;

import lombok.Data;

/**
 * Created by zhangyu on 2020/11/13
 */
public class TestBase {

    @Data
    public static class BaseArg {
        private String name;
    }

    @Data
    public static class StandBaseArg extends BaseArg {
        private String description;
    }

    public static void main(String[] args) {
        StandBaseArg arg = new StandBaseArg();
        arg.setName("aaa");
        arg.setDescription("bbb");

        BaseArg baseArg = arg;
        StandBaseArg standBaseArg = (StandBaseArg) baseArg;
        System.out.println(standBaseArg.getDescription());
    }

}
