package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.UserMenuAction;
import com.facishare.webpage.customer.controller.model.arg.menu.GetUserMenuByAppIdArg;
import com.facishare.webpage.customer.controller.model.arg.menu.GetUserMenuByIdArg;
import com.facishare.webpage.customer.controller.model.result.menu.GetUserMenuByAppIdResult;
import com.facishare.webpage.customer.controller.model.result.menu.GetUserMenuByIdResult;
import com.facishare.webpage.customer.util.GeneralUtil;
import com.fxiaoke.api.model.GetAppMenuDropListDTO;
import com.fxiaoke.api.model.GetPageData;
import com.fxiaoke.api.model.GetPageDataRestDTO;
import com.fxiaoke.api.service.ClientDataRestService;
import com.fxiaoke.api.service.DropListRestService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static org.junit.Assert.assertNotNull;

/**
 * Created by zhangyi on 2019/9/17.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class UserMenuActionTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private UserMenuAction userMenuAction;
    @Resource
    private DropListRestService dropListRestService;
    @Resource
    private ClientDataRestService clientDataRestService;

    @Test
    public void getUserMenuById() throws Exception {
        UserInfo userInfo = GeneralUtil.buildUserInfo(78810, "78810", 1000);
        //"employeeId":1000,"enterpriseAccount
        //":"83150","enterpriseId":83150,"locale":"zh-cn","menuId":"83150_fedfc30f88bb4801bcdaf4bd5f572b2d","pageTemplateType":2,"type":"Web"
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        GetUserMenuByIdArg arg = new GetUserMenuByIdArg();
        arg.setMenuId("71574_a89d1eefccbf4dadabb6c44b9f5c4f7a");
        arg.setPageTemplateType(2);
        GetUserMenuByIdResult result = userMenuAction.getUserMenuById(userInfo, clientInfo, arg);
        assertNotNull(result);
    }

    @Test
    public void testGetUserMenuByAppId() {
        UserInfo userInfo = GeneralUtil.buildUserInfo(89963, "89963", 1000);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        OuterUserInfo outerUserInfo = new OuterUserInfo();
        outerUserInfo.setOutTenantId((long) *********);
        outerUserInfo.setOutUserId((long) 300392433);
        outerUserInfo.setAppId("FSAID_114911f3");
        GetUserMenuByAppIdArg arg = new GetUserMenuByAppIdArg();
        arg.setId("89963_4ea442feabeb4621bde6ab3218ba9e4c");
        GetUserMenuByAppIdResult rest = userMenuAction.getUserMenuByAppId(userInfo, clientInfo, outerUserInfo, arg);
        System.out.println(978745645);
        System.out.println(123123);
    }

    @Test
    public void testUserExtensionRest() {
        GetAppMenuDropListDTO.Arg arg = new GetAppMenuDropListDTO.Arg();
        arg.setAppId("PaaS");
        arg.setCurrentEmployeeId(1000);
        arg.setLocale(Locale.CHINESE);
        arg.setEnterpriseId(85188);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", String.valueOf(arg.getEnterpriseId()));
        GetAppMenuDropListDTO.Result result = dropListRestService.getAppMenuDropList(arg, headers);
        List<String> appMenuApiNames = result.getApiNames();
        System.out.println(JSONObject.toJSONString(appMenuApiNames));
    }

    @Test
    public void testUserExtensionRest1() throws ExecutionException {
        GetPageDataRestDTO.Arg arg = new GetPageDataRestDTO.Arg();
        arg.setAppId("FSAID_9896e1");
        arg.setId("78612-8b290c85d1cf4adfa00b2fb84b104a41");
        arg.setTenantId(78612);
        arg.setEmployeeId(1000);
        arg.setLocale(Locale.CHINA);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", String.valueOf(78612));
        GetPageData.Result result = clientDataRestService.getPageData(arg, headers);
        System.out.println(JSONObject.toJSONString(result));
    }

}