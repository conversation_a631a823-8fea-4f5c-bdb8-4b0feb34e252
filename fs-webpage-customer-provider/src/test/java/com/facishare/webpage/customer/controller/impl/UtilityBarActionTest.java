package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.UtilityBarAction;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarForUserArg;
import com.facishare.webpage.customer.util.GeneralUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> Yu
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class UtilityBarActionTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private UtilityBarAction utilityBarAction;

    @Test
    public void getUtilityBarForUser() {
        GetUtilityBarForUserArg arg = new GetUtilityBarForUserArg();
        arg.setAppId("FSAID_11491009");
        arg.setAppType(2);
        arg.setPageTemplateId("71574_c641cd2955d348dda6e411ded2085929");

        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(300013128L, 300090620L, 1017, 1);
        UserInfo userInfo = GeneralUtil.buildUserInfo(71574, "fsceshi003", null);

        ClientInfo clientInfo = new ClientInfo();

        utilityBarAction.getUtilityBarForUser(userInfo, outerUserInfo, clientInfo, arg);
    }
}
