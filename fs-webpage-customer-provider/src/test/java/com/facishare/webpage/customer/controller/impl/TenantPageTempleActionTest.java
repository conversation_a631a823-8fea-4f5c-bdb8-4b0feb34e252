package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.PageTemplate;
import com.facishare.webpage.customer.controller.TenantPageTempleAction;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.SynPageTemplateArg;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.SynPageTemplateResult;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.UpdatePageTempleStatusResult;
import com.facishare.webpage.customer.util.GeneralUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

;

/**
 * Created by z<PERSON><PERSON> on 2019/9/17.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TenantPageTempleActionTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private TenantPageTempleAction tenantPageTempleAction;

    @Test
    public void deleteSynFromWebTemplate() throws Exception {
        UserInfo userInfo = GeneralUtil.buildUserInfo(84931, "84931", 1000);
        SynPageTemplateArg arg = new SynPageTemplateArg();
        arg.setFromWebPagetemplateId("71574_deda3301b12247569a76e62a11fd8613");
        SynPageTemplateResult result = tenantPageTempleAction.deleteSynFromWebTemplate(userInfo, arg);
        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void createSynFromWebTemplate() throws Exception {
        UserInfo userInfo = GeneralUtil.buildUserInfo(85188, "85188", 1000);
        ClientInfo clientInfo = new ClientInfo();
        SynPageTemplateArg arg = new SynPageTemplateArg();
        arg.setFromWebPagetemplateId("85188_0d54f9ea3f904f13906a878fa815c535");
        SynPageTemplateResult result = tenantPageTempleAction.createSynFromWebTemplate(userInfo, clientInfo, arg);
        System.out.println(JSONObject.toJSONString(result));
    }


}