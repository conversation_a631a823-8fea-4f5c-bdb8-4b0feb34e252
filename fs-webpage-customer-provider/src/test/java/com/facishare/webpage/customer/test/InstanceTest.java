package com.facishare.webpage.customer.test;

import lombok.Data;

public class InstanceTest {

    interface T {
        int getI();
        void setI(int i);
    }

    @Data
    static class T0 implements T {
        int i = 0 ;
    }

    @Data
    static class T1 implements T {
        int i = 0 ;
    }

    public static void main(String[] args) throws Throwable {
        int c = 100000000;
        long total = 0;
        long currentTime = System.currentTimeMillis();
        for (int i = 0; i < c; i++) {
//            T t = i % 2 == 0 ? T0.class.newInstance() : T1.class.newInstance();
            T t = i % 2 == 0 ? new T0() : new T1();
            t.setI(i);
//            total += t.getI();
        }
        System.out.println("new instance by normal cost " + (System.currentTimeMillis() - currentTime) + "ms, total is " + total);

        currentTime = System.currentTimeMillis();
        total = 0;
        for (int i = 0; i < c; i++) {
//            T t = i % 2 == 0 ? new T0() : new T1();
            T t = i % 2 == 0 ? T0.class.newInstance() : T1.class.newInstance();
            t.setI(i);
//            total += t.getI();
        }
        System.out.println("new instance by newInstance cost " + (System.currentTimeMillis() - currentTime) + "ms, total is " + total);
    }

}
