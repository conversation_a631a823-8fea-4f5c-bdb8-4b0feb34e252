package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.TempleType;
import com.facishare.webpage.customer.api.model.arg.QueryCustomerPageWidgetsArg;
import com.facishare.webpage.customer.api.model.arg.QueryHomePageWidgetsArg;
import com.facishare.webpage.customer.api.model.result.QueryCustomerPageWidgetsResult;
import com.facishare.webpage.customer.api.model.result.QueryHomePageWidgetsResult;
import com.facishare.webpage.customer.rest.WebPageRestAction;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by zhang<PERSON> on 2020/2/24
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class HomePageTest {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Autowired
    private WebPageRestAction webPageRestAction;

    @Test
    public void getHomePageById() {
//        Filter<Query> filter = query -> query.field("appId").equal("AccountObj");

//        List<HomePageLayoutEntity> list = (List<HomePageLayoutEntity>) systemDataService.queryDataList(71574, filter);
//        System.out.println(list.size());
    }

    @Test
    public void queryHomePageWidgets() {
        QueryHomePageWidgetsArg arg = new QueryHomePageWidgetsArg();
        arg.setEnterpriseId(85882);
        arg.setSource(TempleType.WEB);
        QueryHomePageWidgetsResult result = webPageRestAction.queryHomePageWidgets("85882", arg);
        System.out.println(JSONObject.toJSONString(result));
        arg.setSource(TempleType.APP);
        QueryHomePageWidgetsResult result1 = webPageRestAction.queryHomePageWidgets("85882", arg);
        System.out.println(JSONObject.toJSONString(result1));
    }

    @Test
    public void queryCustomerPageWidgets() {
        QueryCustomerPageWidgetsArg arg = new QueryCustomerPageWidgetsArg();
        arg.setEnterpriseId(85188);
        arg.setSource(TempleType.WEB);
        QueryCustomerPageWidgetsResult result = webPageRestAction.queryCustomerPageWidgets("85188", arg);
        System.out.println(JSONObject.toJSONString(result));
        arg.setSource(TempleType.APP);
        QueryCustomerPageWidgetsResult result1 = webPageRestAction.queryCustomerPageWidgets("85188", arg);
        System.out.println(JSONObject.toJSONString(result1));
    }

}
