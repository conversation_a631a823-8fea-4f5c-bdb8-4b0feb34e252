package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.organization.api.model.employee.BatchGetSimpleEmployeeDto;
import com.facishare.organization.api.model.employee.SimpleEmployeeDto;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.webpage.customer.controller.UserPageTempleAction;
import com.fxiaoke.appcenter.restapi.arg.CanAccessComponentArg;
import com.fxiaoke.appcenter.restapi.arg.QueryAppListByFsEnterpriseAccountArg;
import com.fxiaoke.appcenter.restapi.arg.QueryComponentsByFsUserArg;
import com.fxiaoke.appcenter.restapi.common.BaseResult;
import com.fxiaoke.appcenter.restapi.enums.AppAccessTypeEnum;
import com.fxiaoke.appcenter.restapi.model.OpenAppDO;
import com.fxiaoke.appcenter.restapi.model.vo.UserCanViewListVO;
import com.fxiaoke.appcenter.restapi.service.OpenFsUserAppViewService;
import com.fxiaoke.appcenter.restapi.service.OpenFsUserBindAppService;
import com.fxiaoke.enterpriserelation2.arg.GetDownstreamEmployeeInfoArg;
import com.fxiaoke.enterpriserelation2.arg.ListSourceEmployeesByFsArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.EmployeeCardResult;
import com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2019/9/25
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/applicationContext.xml")
public class TestEmployee {

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Resource
    private EmployeeProviderService employeeProviderService;
    @Resource
    private PublicEmployeeService publicEmployeeService;
    @Resource
    private OpenFsUserBindAppService openFsUserBindAppService;
    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Resource
    private UserPageTempleAction userPageTempleAction;


    @Test
    public void testGetEmployee() {
        BatchGetSimpleEmployeeDto.Arg arg = new BatchGetSimpleEmployeeDto.Arg();
        arg.setEmployeeIds(Lists.newArrayList(2571));
        arg.setEnterpriseId(2);
        BatchGetSimpleEmployeeDto.Result result = employeeProviderService.batchGetSimpleEmployeeDto(arg);
        List<SimpleEmployeeDto> employeeDtos = result.getEmployeeDtos();
        System.out.println(employeeDtos);

        /*GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEnterpriseId(2);
        employeeDtoArg.setEmployeeId(2571);
        GetEmployeeDtoResult employeeDto = employeeProviderService.getEmployeeDto(employeeDtoArg);
        System.out.println(employeeDto);*/
    }

    @Test
    public void testListSourceEmployeesByFs() {
        HeaderObj headerObj = HeaderObj.newInstance(71574);
        ListSourceEmployeesByFsArg arg = new ListSourceEmployeesByFsArg();
        arg.setDestTenantId(71574);
        arg.setSourceTenantId(71575);
        RestResult<List<EmployeeCardResult>> result = publicEmployeeService.listSourceEmployeesByFs(headerObj, arg);
        System.out.println(result);
    }

    @Test
    public void testOpenFsUserBindAppService() {
        com.fxiaoke.appcenter.restapi.common.HeaderObj headerObj = com.fxiaoke.appcenter.restapi.common.HeaderObj.newInstance("71574");
        headerObj.put("x-fs-locale", Locale.ENGLISH.toLanguageTag());
        QueryAppListByFsEnterpriseAccountArg arg = new QueryAppListByFsEnterpriseAccountArg();
        arg.setArg2("fsceshi003");
        arg.setFsUserVO("fsceshi003", 1001, "");
        BaseResult<List<OpenAppDO>> listBaseResult = openFsUserBindAppService.queryAppListByFsEnterpriseAccount(headerObj, arg);
        System.out.println(listBaseResult);
    }

    @Test
    public void testOpenFsUserAppViewService() {
        com.fxiaoke.appcenter.restapi.common.HeaderObj headerObj = com.fxiaoke.appcenter.restapi.common.HeaderObj.newInstance("71574");
        QueryComponentsByFsUserArg arg = new QueryComponentsByFsUserArg();
        arg.setFsUserVO("fsceshi003", 1017, "");
        arg.setArg2(AppAccessTypeEnum.ANDROID);
        BaseResult<List<UserCanViewListVO>> result = openFsUserAppViewService.queryComponentsByFsUser(headerObj, arg);
        System.out.println(result);
    }

    @Test
    public void testCanAccessComponent() {
        CanAccessComponentArg arg = new CanAccessComponentArg();
        arg.setFsUserVO("fsceshi003", 1017, "");
        arg.setComponentId("FSAID_5f5e24a");
        com.fxiaoke.appcenter.restapi.common.HeaderObj headerObj = com.fxiaoke.appcenter.restapi.common.HeaderObj.newInstance("71574");
        BaseResult<Boolean> result = openFsUserAppViewService.canAccessComponent(headerObj, arg);
        System.out.println(result);
    }

    @Test
    public void getDownstreamEmployeeInfo() {
        HeaderObj headerObj = HeaderObj.newInstance(71574);
        GetDownstreamEmployeeInfoArg arg = new GetDownstreamEmployeeInfoArg();
        arg.setUpstreamTenantId(71574);
        arg.setOuterUid(300090620L);
        RestResult<GetDownstreamEmployeeInfoResult> downstreamEmployeeInfo = publicEmployeeService.getDownstreamEmployeeInfo(headerObj, arg);
        System.out.println(downstreamEmployeeInfo.isSuccess());
    }

    @Test
    public void testGetDownstreamEmployeeInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(71574);
        userInfo.setEnterpriseAccount("fsceshi003");

        OuterUserInfo outerUserInfo = new OuterUserInfo();
        outerUserInfo.setUpstreamOwnerId(71574);
        outerUserInfo.setOutUserId(300090620L);
        GetDownstreamEmployeeInfoResult downstreamEmployeeInfo = userPageTempleAction.getDownstreamEmployeeInfo(userInfo, outerUserInfo);
        System.out.println(downstreamEmployeeInfo);
    }
}
