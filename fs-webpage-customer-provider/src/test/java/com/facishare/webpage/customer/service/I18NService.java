package com.facishare.webpage.customer.service;

import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.qixin.i18n.QixinI18nServiceImpl;
import com.facishare.qixin.i18n.model.Key;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.Map;

/**
 * Created by zhangyu on 2020/3/6
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-remote.xml")
public class I18NService {


    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }


    @Test
    public void testI18NService() throws InterruptedException {
        /*I18nClient.getInstance().initWithTags("server");
        Thread.sleep(20000L);
        Map<String, Localization> keyMaps = I18nClient.getInstance().get(Lists.newArrayList("group_group-7e5328da-4195-4bf3-88c6-00876e2fab91"), 71574);
        System.out.println(keyMaps.get("group_group-7e5328da-4195-4bf3-88c6-00876e2fab91").getEn());*/
        System.out.println(Locale.JAPAN.toString());
        System.out.println(Locale.JAPANESE.toString());
        System.out.println(Locale.JAPAN.toLanguageTag());
        System.out.println(Locale.JAPANESE.toLanguageTag());
        System.out.println(Locale.JAPAN.toLanguageTag());
        System.out.println(Locale.SIMPLIFIED_CHINESE.toLanguageTag());
        /*Key key = new Key("group_group-7e5328da-4195-4bf3-88c6-00876e2fab91");
        Map<String, String> fsceshi003 = qixinI18nService.getMultiI18nValueDefault("fsceshi003", Lists.newArrayList(key), Locale.ENGLISH);
        System.out.println(fsceshi003.get("group_group-7e5328da-4195-4bf3-88c6-00876e2fab91"));*/


    }

}
