package com.facishare.webpage.customer.test;

import com.facishare.webpage.customer.dao.entity.MenuDataEntity;
import com.facishare.webpage.customer.dao.entity.PageTempleEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.Key;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.UUID;

/**
 * Created by zhangyu on 2019/9/20
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:fs-webpage-customer-dao.xml")
public class TestAddPageTemplate {

    @Resource
    private Datastore datastore;

    static {
        System.getProperties().setProperty("process.profile", "fstest");
    }

    @Test
    public void testAdd() {
        PageTempleEntity pageTempleEntity = new PageTempleEntity();
        pageTempleEntity.setTempleId(UUID.randomUUID().toString());
        pageTempleEntity.setAppId("PRM");
        pageTempleEntity.setName("首页3");
        pageTempleEntity.setSourceType("system");
        pageTempleEntity.setScopes(Lists.newArrayList("OR-111", "OR-222"));
        pageTempleEntity.setSourceId(UUID.randomUUID().toString());
        pageTempleEntity.setAppPageId(UUID.randomUUID().toString());
        pageTempleEntity.setWebMenuId(UUID.randomUUID().toString());
        pageTempleEntity.setWebPageId(UUID.randomUUID().toString());
        pageTempleEntity.setCreatorId(1007);
        pageTempleEntity.setCreateTime(System.currentTimeMillis());
        pageTempleEntity.setUpdaterId(1007);
        pageTempleEntity.setUpdateTime(System.currentTimeMillis());
        pageTempleEntity.setTenantId(2);
        pageTempleEntity.setType("app");

        Key<PageTempleEntity> save = datastore.save(pageTempleEntity);

        System.out.println(save);

    }
    @Test
    public void testAddMenu() {
        TenantMenuEntity tenantMenuEntity = new TenantMenuEntity();
//        tenantMenuEntity.setId(UUID.randomUUID().toString());
        tenantMenuEntity.setId("4cab42cd-9079-45fc-99c8-90c123845155");
        tenantMenuEntity.setAppId("PRM");
        tenantMenuEntity.setTenantId(71568);
        tenantMenuEntity.setSourceType("system");
        tenantMenuEntity.setCreatorId(1007);
        tenantMenuEntity.setCreateTime(System.currentTimeMillis());
        tenantMenuEntity.setUpdaterId(1007);
        tenantMenuEntity.setStatus(0);
        tenantMenuEntity.setSourceId(UUID.randomUUID().toString());
        tenantMenuEntity.setUpdateTime(System.currentTimeMillis());
        MenuDataEntity menuDataEntity = new MenuDataEntity();
        menuDataEntity.setApiName("fgsjfs11");
        menuDataEntity.setGroupApiName("fgsjfs11");
        menuDataEntity.setIsHidden(false);
        menuDataEntity.setName("菜单11");
        menuDataEntity.setOrderNumber(11);
        menuDataEntity.setType("menu");
        tenantMenuEntity.setMenuDataEntities(Lists.newArrayList(menuDataEntity));
        Key<TenantMenuEntity> save = datastore.save(tenantMenuEntity);
        System.out.println(save);
    }

}
