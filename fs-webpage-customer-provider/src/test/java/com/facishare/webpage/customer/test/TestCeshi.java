package com.facishare.webpage.customer.test;

import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Splitter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * Created by zhangyu on 2019/9/2
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-test.xml")
public class TestCeshi {

    static {
        System.getProperties().setProperty("process.profile", "ceshi113");
    }

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Test
    public void testEnterpriseEditionService(){
        GetSimpleEnterpriseDataArg arg = new GetSimpleEnterpriseDataArg();
        arg.setEnterpriseAccount("55707");
        GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(arg);
        System.out.println(simpleEnterpriseData.getEnterpriseData().getEnterpriseName());
    }
    @Test
    public void testHashSet(){
        String a = "aaa";
        String b = "aaa";
        Set<String> hashSet = new HashSet<>();
        hashSet.add(a);
        if(hashSet.contains(b)){
            System.out.println(b);
        }
        System.out.println("test");
    }
}
