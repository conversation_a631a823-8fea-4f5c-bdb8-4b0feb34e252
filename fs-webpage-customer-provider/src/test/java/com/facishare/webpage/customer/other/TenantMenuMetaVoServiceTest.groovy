package com.facishare.webpage.customer.other

import com.alibaba.fastjson.JSON
import com.facishare.webpage.customer.controller.TenantMenuAction
import com.facishare.webpage.customer.controller.impl.TenantMenuActionImpl
import com.facishare.webpage.customer.controller.model.DefaultMenuVO
import com.facishare.webpage.customer.controller.model.TenantMenuItemVO
import com.facishare.webpage.customer.controller.model.TenantMenuSimpleDataVo
import com.facishare.webpage.customer.controller.model.arg.menu.GetTenantDefaultMenuDetailByAppIdArg
import com.facishare.webpage.customer.controller.model.arg.menu.GetTenantMenuDetailByIdArg
import com.facishare.webpage.customer.controller.model.arg.menu.UpdateTenantMenuArg
import org.apache.commons.collections.CollectionUtils


/**
 * Created by zhangyu on 2019/9/21
 */
class TenantMenuMetaVoServiceTest extends MenuMetaVoBaseTest {

    private TenantMenuAction tenantMenuAction
    public String exceptDefaultResult = "[\n" +
            "    {\n" +
            "        \"api_name\":\"HighSeasObj\",\n" +
            "        \"id\":\"HighSeasObj\",\n" +
            "        \"type\":\"menu\",\n" +
            "        \"displayName\":\"公海\",\n" +
            "        \"referenceApiname\":\"HighSeasObj\",\n" +
            "        \"is_active\":true\n" +
            "    },\n" +
            "    {\n" +
            "        \"api_name\":\"LeadsPoolObj\",\n" +
            "        \"id\":\"LeadsPoolObj\",\n" +
            "        \"type\":\"menu\",\n" +
            "        \"displayName\":\"线索池\",\n" +
            "        \"referenceApiname\":\"LeadsPoolObj\",\n" +
            "        \"is_active\":true\n" +
            "    },\n" +
            "    {\n" +
            "        \"api_name\":\"AccountObj\",\n" +
            "        \"id\":\"AccountObj\",\n" +
            "        \"type\":\"menu\",\n" +
            "        \"displayName\":\"客户\",\n" +
            "        \"referenceApiname\":\"AccountObj\",\n" +
            "        \"is_active\":true\n" +
            "    },\n" +
            "    {\n" +
            "        \"api_name\":\"ContactObj\",\n" +
            "        \"id\":\"ContactObj\",\n" +
            "        \"type\":\"menu\",\n" +
            "        \"displayName\":\"联系人\",\n" +
            "        \"referenceApiname\":\"ContactObj\",\n" +
            "        \"is_active\":true\n" +
            "    }\n" +
            "]"

    private String expectTenantMenu = "[{\n" +
            "                \"id\": \"AccountObj\",\n" +
            "                \"displayName\": \"客户\",\n" +
            "                \"number\": 10,\n" +
            "                \"referenceApiname\": \"AccountObj\",\n" +
            "                \"type\": \"menu\",\n" +
            "                \"isHidden\": false\n" +
            "            },\n" +
            "            {\n" +
            "                \"id\": \"ContactObj\",\n" +
            "                \"displayName\": \"联系人\",\n" +
            "                \"number\": 20,\n" +
            "                \"referenceApiname\": \"ContactObj\",\n" +
            "                \"type\": \"menu\",\n" +
            "                \"isHidden\": false\n" +
            "            }]"

    private String menuSimpleItems = "[\n" +
            "    {\n" +
            "        \"api_name\":\"AccountObj\",\n" +
            "        \"children\":[\n" +
            "\n" +
            "        ],\n" +
            "        \"is_hidden\":false,\n" +
            "        \"menu_item_id\":\"\",\n" +
            "        \"number\":10,\n" +
            "        \"type\":\"menu\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"api_name\":\"ContactObj\",\n" +
            "        \"children\":[\n" +
            "\n" +
            "        ],\n" +
            "        \"is_hidden\":false,\n" +
            "        \"menu_item_id\":\"\",\n" +
            "        \"number\":20,\n" +
            "        \"type\":\"menu\"\n" +
            "    }\n" +
            "]"

    def "getTenantDefaultMenuDetailByAppId"() {
        given:
        tenantMenuAction = new TenantMenuActionImpl()
        tenantMenuAction.tenantMenuService = tenantMenuService
        List<DefaultMenuVO> exceptMenuDataList = JSON.parseArray(exceptDefaultResult, DefaultMenuVO.class)
        when:
        def arg = new GetTenantDefaultMenuDetailByAppIdArg()
        arg.setAppId(appId)
        def result = tenantMenuAction.getTenantDefaultMenuDetailByAppId(userInfo, arg)
        then:
        CollectionUtils.isEqualCollection(exceptMenuDataList, result.defaultMenuVOS)
    }


    def "getTenantMenuDetailById"() {
        given:
        tenantMenuAction = new TenantMenuActionImpl()
        tenantMenuAction.tenantMenuService = tenantMenuService
        when:
        when:
        def arg = new GetTenantMenuDetailByIdArg()
        List<TenantMenuItemVO> expectTenantMenuItems = JSON.parseArray(expectTenantMenu, TenantMenuItemVO.class)
        arg.setMenuTempleId(menuId)
        def result = tenantMenuAction.getTenantMenuDetailById(userInfo, arg)
        then:
        CollectionUtils.isEqualCollection(expectTenantMenuItems, result.tenantMenuItemVOS)
    }

    def "insertOrUpdateTenantMenu"() {
        given:
        tenantMenuAction = new TenantMenuActionImpl()
        tenantMenuAction.tenantMenuService = tenantMenuService
        List<TenantMenuSimpleDataVo> tenantMenuSimpleItems = JSON.parseArray(menuSimpleItems, TenantMenuSimpleDataVo.class)
        List<TenantMenuItemVO> expectTenantMenuItems = JSON.parseArray(expectTenantMenu, TenantMenuItemVO.class)
        when:
        when:
        def arg1 = new UpdateTenantMenuArg()
        arg1.setAppId(appId)
        arg1.setTenantMenuSimpleItems(tenantMenuSimpleItems)
        arg1.setId(menuId)
        def result1 = tenantMenuAction.updateTenantMenu(userInfo, arg1)
        def arg2 = new GetTenantMenuDetailByIdArg()
        arg2.setMenuTempleId(result1.webMenuId)
        def result2 = tenantMenuAction.getTenantMenuDetailById(userInfo, arg2)
        then:
        CollectionUtils.isEqualCollection(expectTenantMenuItems, result2.tenantMenuItemVOS)
    }
}
