package com.facishare.webpage.customer.test;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.config.model.HomePageCount;
import com.facishare.webpage.customer.util.ChineseToPinyinUtil;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/10/17
 */
@Data
public class Tests implements Serializable {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private String name;

    private int age;

    public enum Preset implements Serializable {
        Chart(1),
        Report(2),
        Preset(3);
        private int index;

        Preset(int index) {
            this.index = index;
        }
    }

    public static void main(String[] args) {
        System.out.println(Preset.values());
    }

    @Test
    public void testListEquals() {
        List<String> list = Lists.newArrayList("empty");
        List<String> list1 = Lists.newArrayList("empty");
        if (list.equals(list1)) {
            System.out.println("成功！");
        } else {
            System.out.println("失败！");
        }
    }

    @Test
    public void testLong() {
        String appId = "";
        System.out.println(appId.toString());
    }

    @Test
    public void testList() {
        List<String> strings = null;
        strings.stream().forEach(s -> {
            System.out.println(s);
        });
    }

    @Test
    public void testListNull() {
        List<Integer> list = Lists.newArrayList();
        System.out.println(list.contains(1));
    }

    @Test
    public void testSort() {
        Tests t1 = new Tests();
        t1.setName(UUID.randomUUID().toString());
        t1.setAge(2);

        Tests t2 = new Tests();
        t2.setName(UUID.randomUUID().toString());
        t2.setAge(1);

        Tests t3 = new Tests();
        t3.setName(UUID.randomUUID().toString());
        t1.setAge(5);

        Tests t4 = new Tests();
        t4.setName(UUID.randomUUID().toString());
        t4.setAge(4);

        List<Tests> list = Lists.newArrayList(t1,t2,t3,t4);
        System.out.println(list);

        List<Tests> list1 = list.stream().sorted(Comparator.comparing(Tests::getAge)).collect(Collectors.toList());

        System.out.println(list1);

    }
    @Test
    public void testLow(){
        String url = "123456";
        System.out.println(url.toLowerCase());
    }

    @Test
    public void testStream(){
        List<Integer> array = Lists.newArrayList(1, 2, 3, 4, 5, 6);
        array.stream().forEach(x -> {
            if(x == 3){
                return;
            }
            System.out.println(x);
        });
    }
    @Test
    public void testCompDate(){
        Date date1 = new Date();

        Date date2 = new Date();


    }
    @Test
    public void testGetHour(){
        int hour = LocalTime.now().getHour();
        System.out.println(hour);
    }
    @Test
    public void testFore(){
        List<Integer> oldList = Lists.newArrayList(1);
        List<Integer> updateList = Lists.newArrayList(2);
        List<Integer> newList = Lists.newArrayList();

        for (int i : oldList) {
            boolean update = false;
            for (int j : updateList) {
                if(i == j){
                    update = true;
                    newList.add(j);
                    break;
                }
            }
            if(!update){
                newList.add(i);
            }
        }
        updateList.stream().filter(x -> !newList.contains(x)).forEach(newList::add);
        System.out.println(newList);
    }
    @Test
    public void test(){
        Map<String, HomePageCount> map = Maps.newHashMap();
        HomePageCount homePageCount = new HomePageCount();
        homePageCount.setMaxPersonCount(10);
        homePageCount.setMaxTenantCount(10);
        map.put("fsceshi003", homePageCount);
        JSONObject.toJSONString(map);
        System.out.println(map);
    }
    @Test
    public void testReplace(){
        String id = "79299-1a9b450a-dbf4-420f-88ef-90877c48d86e";
        String[] split = id.split("-");

        String replaceFirst = id.replaceFirst(split[0], String.valueOf(123456));
        System.out.println(replaceFirst);

    }
    @Test
    public void testRetainAll(){
        Set<Integer> set = Sets.newHashSet(1,2,3,4);
        List<Integer> list = Lists.newArrayList(3,5,6);
        List<Integer> subtract = (List<Integer>) CollectionUtils.subtract(list, set);
        System.out.println(subtract);
    }

    @Test
    public void testForeach(){
        Tests tests = new Tests();
        tests.setName("1111");
        tests.setAge(222);

        List<Tests> tests1 = Lists.newArrayList(tests);
        tests1.stream().forEach(tests2 -> {
            if (tests2.getAge() == 222){
                tests2.setName("4444");
            }
        });
        System.out.println(tests1);
    }
    @Test
    public void testSplit(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("111", "111");
        System.out.println(jsonObject);

        jsonObject.remove("111");
        System.out.println(jsonObject);
    }

    @Test
    public void testList1(){
        List<String> test1 = Lists.newArrayList();
        List<String> test2 = Lists.newArrayList("111");
        System.out.println(CollectionUtils.containsAny(test1, test2));
    }

    @Test
    public void testSystemCurrentTime() {

        for (int i = 0; i < 3; i++){
            long x = (long)(Math.random()*1000 * 1000 * 1000);
            System.out.println(x);
        }

    }

    @Test
    public void testEquars() {
        String b = "LeadsPoolObj,HighSeasObj";
        String a = "";
    }

    @Test
    public void replaceTest() {
        String s = TempleIdUtil.replaceId(111, "123456", "-");
        System.out.println(s);

    }

    @Test
    public void testChineseToPinYin() {

        String cn2FirstSpell = ChineseToPinyinUtil.getLowerCase("囖囖", true);

        System.out.println(cn2FirstSpell);
    }

    private void test11(AtomicInteger atomicInteger){
        int count = atomicInteger.get();
        count++;
        count++;
        count++;
        count++;
        atomicInteger.set(count);
    }


    @Test
    public void test22() {
        AtomicInteger atomicInteger = new AtomicInteger(0);
        test11(atomicInteger);
        System.out.println(atomicInteger.get());
    }

    @Test
    public void testRegex() {
        Pattern pattern =Pattern.compile("\\{(.*?)\\}");
        Matcher m = pattern.matcher("{0}");
        String group = m.group();
        System.out.println(group);
    }

    @Test
    public void streamNull() {
        List<String> list = Lists.newArrayList(null, null);
        list.stream().forEach(x -> {
            System.out.println(x);
        });
    }

    @Test
    public void generateId() {

        StringBuilder sb = new StringBuilder();
        int hashCode = sb.append(System.currentTimeMillis()).toString().hashCode();

        System.out.println(hashCode);
    }

    @Test
    public void testString() {
        String url = "crm/customer?apiname=%s&thirdapprecordtype=%s";
        String format = String.format(url, "AccountObj", "record_uW2uv__c");
        System.out.println(format);
    }


    @Test
    public void name() {

        com.facishare.webpage.customer.test.Test test = new com.facishare.webpage.customer.test.Test();
        Integer a = null;

        test.setCode(a);

    }

    @Test
    public void testFilter() {
        String tenantIds = "721313,720831,720830,720771,720731,720705,720613,719990,719984,710928,719596,719481,719480,719479,719478,719477,719476,719475,719474,719473,719472,719471,719398,719356,719314,719308,719239,719132,719124,719123,719060,719035,718961,712281,712314,712280,714678,714989,713198,718683,712798,717100,717036,712257,717595,713183,717218,717505,717294,717092,717008,714857,716725,716684,716464,716382,716347,715972,715628,715434,714858,714506,713548,713365,713132,713130,713049,713048,713047,712538,712458,712452,712450,712434,712373,712371,712359,712328,712326,712248,712187,712182,712170,712156,712134,712122,721745,721434,721444,721340,721076,721112,721107,721074,719943,719945,719951,719946,719947,719949,719955,719956,719958,719960,719966,719965,719962,719959,719957,719954,719952,719950,719948,719944,719942,719941,719879,719739";
        String[] split = tenantIds.split(",");
        List<Integer> array = Lists.newArrayList();
        for (String x : split){
            if (Integer.parseInt(x.trim()) < 713651){
                array.add(Integer.parseInt(x.trim()));
            }
        }
        System.out.println(array);
    }


    @Test
    public void testLanguage() {
        List<Integer> array = Lists.newArrayList(724760,724377,724376,723753,723648,723485,723419,723111,723110,719124,722844,722146,722143,722141,721801,721745,721313,720831,720830,720771,720731,720705,720613,719990,719984,710928,719596,719481,719480,719479,719478,719477,719476,719475,719474,719473,719472,719471,719398,719356,719314,719308,719239,719132,719123,719060,719035,718961,712281,712314,712280,714678,714989,713198,718683,712798,717100,717036,712257,717595,713183,717218,717505,717294,717092,717008,714857,716725,716684,716464,716382,716347,715972,715628,715434,714858,714506,713548,713365,713132,713130,713049,713048,713047,712538,712458,712452,712450,712434,712373,712371,712359,712328,712326,712248,712187,712182,712170,712156,712134,712122);
        List<Integer> list = array.stream().filter(x -> x < 713651).collect(Collectors.toList());
        System.out.println(list);
    }
}
