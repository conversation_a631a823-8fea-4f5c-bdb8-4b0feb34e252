package com.facishare.webpage.customer.config

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

/**
 * DefaultTenantConfig单元测试
 */
class DefaultTenantConfigTest extends Specification {

    def setupSpec() {
        // 标准I18n模拟设置
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    /**
     * 测试内容描述：测试获取Web主频道更多时间配置
     */
    def "getGetWebMainChannelMoreTimeByEi_空租户ID_返回默认值12"() {
        given: "准备测试数据"
        def tenantId = null

        when: "调用被测方法"
        def result = DefaultTenantConfig.getGetWebMainChannelMoreTimeByEi(tenantId)

        then: "验证结果"
        result == 12
    }

    /**
     * 测试内容描述：测试获取Web主频道更多时间配置
     */
    def "getGetWebMainChannelMoreTimeByEi_空字符串租户ID_返回默认值12"() {
        given: "准备测试数据"
        def tenantId = ""

        when: "调用被测方法"
        def result = DefaultTenantConfig.getGetWebMainChannelMoreTimeByEi(tenantId)

        then: "验证结果"
        result == 12
    }

    /**
     * 测试内容描述：测试获取Web主频道更多时间配置
     */
    def "getGetWebMainChannelMoreTimeByEi_租户ID不存在配置_返回默认值12"() {
        given: "准备测试数据"
        def tenantId = "123456"
        // 清空配置
        def originalMap = DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = [:]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getGetWebMainChannelMoreTimeByEi(tenantId)

        then: "验证结果"
        result == 12

        cleanup: "恢复原始配置"
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = originalMap
    }

    /**
     * 测试内容描述：测试获取Web主频道更多时间配置
     */
    def "getGetWebMainChannelMoreTimeByEi_租户ID存在配置_返回配置值"() {
        given: "准备测试数据"
        def tenantId = "123456"
        def expectedValue = 20
        // 保存原始配置
        def originalMap = DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI
        // 设置测试配置
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = [(tenantId): expectedValue]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getGetWebMainChannelMoreTimeByEi(tenantId)

        then: "验证结果"
        result == expectedValue

        cleanup: "恢复原始配置"
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = originalMap
    }

    /**
     * 测试内容描述：测试获取Web主频道更多时间配置
     */
    def "getGetWebMainChannelMoreTimeByEi_全局配置存在_返回全局配置值"() {
        given: "准备测试数据"
        def tenantId = "123456"
        def expectedValue = 30
        // 保存原始配置
        def originalMap = DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI
        // 设置测试配置，包括租户特定配置和全局配置
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = [
            (tenantId): 20,
            "*": expectedValue
        ]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getGetWebMainChannelMoreTimeByEi(tenantId)

        then: "验证结果 - 全局配置优先级高于租户特定配置"
        result == expectedValue

        cleanup: "恢复原始配置"
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = originalMap
    }

    /**
     * 测试内容描述：测试获取元数据菜单更多时间配置
     */
    def "getFindMetaMenuMoreTimeByEi_空租户ID_返回默认值10"() {
        given: "准备测试数据"
        def tenantId = null

        when: "调用被测方法"
        def result = DefaultTenantConfig.getFindMetaMenuMoreTimeByEi(tenantId)

        then: "验证结果"
        result == 10
    }

    /**
     * 测试内容描述：测试获取元数据菜单更多时间配置
     */
    def "getFindMetaMenuMoreTimeByEi_空字符串租户ID_返回默认值10"() {
        given: "准备测试数据"
        def tenantId = ""

        when: "调用被测方法"
        def result = DefaultTenantConfig.getFindMetaMenuMoreTimeByEi(tenantId)

        then: "验证结果"
        result == 10
    }

    /**
     * 测试内容描述：测试获取元数据菜单更多时间配置
     */
    def "getFindMetaMenuMoreTimeByEi_租户ID不存在配置_返回默认值10"() {
        given: "准备测试数据"
        def tenantId = "123456"
        // 清空配置
        def originalMap = DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = [:]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getFindMetaMenuMoreTimeByEi(tenantId)

        then: "验证结果"
        result == 10

        cleanup: "恢复原始配置"
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = originalMap
    }

    /**
     * 测试内容描述：测试获取元数据菜单更多时间配置
     */
    def "getFindMetaMenuMoreTimeByEi_租户ID存在配置_返回配置值"() {
        given: "准备测试数据"
        def tenantId = "123456"
        def expectedValue = 20
        // 保存原始配置
        def originalMap = DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI
        // 设置测试配置
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = [(tenantId): expectedValue]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getFindMetaMenuMoreTimeByEi(tenantId)

        then: "验证结果"
        result == expectedValue

        cleanup: "恢复原始配置"
        DefaultTenantConfig.FIND_META_MENU_MORE_TIME_BY_EI = originalMap
    }

    /**
     * 测试内容描述：测试是否忽略许可证检查
     */
    def "isIgnoreLicenseByAppId_应用ID在忽略列表中_返回true"() {
        given: "准备测试数据"
        def appId = "test_app_id"
        // 保存原始配置
        def originalSet = DefaultTenantConfig.IGNORE_LICENSE_APP_IDS
        // 设置测试配置
        DefaultTenantConfig.IGNORE_LICENSE_APP_IDS = [appId] as Set

        when: "调用被测方法"
        def result = DefaultTenantConfig.isIgnoreLicenseByAppId(appId)

        then: "验证结果"
        result == true

        cleanup: "恢复原始配置"
        DefaultTenantConfig.IGNORE_LICENSE_APP_IDS = originalSet
    }

    /**
     * 测试内容描述：测试是否忽略许可证检查
     */
    def "isIgnoreLicenseByAppId_应用ID不在忽略列表中_返回false"() {
        given: "准备测试数据"
        def appId = "test_app_id"
        // 保存原始配置
        def originalSet = DefaultTenantConfig.IGNORE_LICENSE_APP_IDS
        // 设置测试配置
        DefaultTenantConfig.IGNORE_LICENSE_APP_IDS = ["other_app_id"] as Set

        when: "调用被测方法"
        def result = DefaultTenantConfig.isIgnoreLicenseByAppId(appId)

        then: "验证结果"
        result == false

        cleanup: "恢复原始配置"
        DefaultTenantConfig.IGNORE_LICENSE_APP_IDS = originalSet
    }

    /**
     * 测试内容描述：测试是否为灰度企业
     */
    def "isGrayEnterprise_租户ID在灰度列表中_返回true"() {
        given: "准备测试数据"
        def tenantId = 123456

        // 设置测试配置
        DefaultTenantConfig.ENTERPRISE_EDITION_TENANT_IDS = [tenantId.toString()]

        when: "调用被测方法"
        def result = DefaultTenantConfig.isGrayEnterprise(tenantId)

        then: "验证结果"
        result == true
    }

    /**
     * 测试内容描述：测试是否为灰度企业
     */
    def "isGrayEnterprise_租户ID不在灰度列表中_返回false"() {
        given: "准备测试数据"
        def tenantId = 123456
        // 保存原始配置
        def originalSet = DefaultTenantConfig.ENTERPRISE_EDITION_TENANT_IDS
        // 设置测试配置
        DefaultTenantConfig.ENTERPRISE_EDITION_TENANT_IDS = ["654321"] as Set

        when: "调用被测方法"
        def result = DefaultTenantConfig.isGrayEnterprise(tenantId)

        then: "验证结果"
        result == false

        cleanup: "恢复原始配置"
        DefaultTenantConfig.ENTERPRISE_EDITION_TENANT_IDS = originalSet
    }

    /**
     * 测试内容描述：测试获取产品版本
     */
    def "getProductVersionByAppId_应用ID存在配置_返回对应版本"() {
        given: "准备测试数据"
        def appId = "test_app_id"
        def expectedVersion = "test_version"
        // 保存原始配置
        def originalMap = DefaultTenantConfig.APP_ID_AND_LICENSE_MAP
        // 设置测试配置
        DefaultTenantConfig.APP_ID_AND_LICENSE_MAP = [(appId): expectedVersion]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getProductVersionByAppId(appId)

        then: "验证结果"
        result == expectedVersion

        cleanup: "恢复原始配置"
        DefaultTenantConfig.APP_ID_AND_LICENSE_MAP = originalMap
    }

    /**
     * 测试内容描述：测试获取产品版本
     */
    def "getProductVersionByAppId_应用ID不存在配置_返回null"() {
        given: "准备测试数据"
        def appId = "test_app_id"
        // 保存原始配置
        def originalMap = DefaultTenantConfig.APP_ID_AND_LICENSE_MAP
        // 设置测试配置
        DefaultTenantConfig.APP_ID_AND_LICENSE_MAP = [:]

        when: "调用被测方法"
        def result = DefaultTenantConfig.getProductVersionByAppId(appId)

        then: "验证结果"
        result == null

        cleanup: "恢复原始配置"
        DefaultTenantConfig.APP_ID_AND_LICENSE_MAP = originalMap
    }
} 