package com.facishare.webpage.customer.controller.impl

import com.alibaba.fastjson.JSONObject
import com.facishare.webpage.customer.api.model.HomePageLayoutTO
import com.facishare.webpage.customer.constant.DataVersion
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.apache.commons.codec.digest.DigestUtils
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * UserHomePageActionImpl单元测试
 * 测试UserHomePageActionImpl类中的方法
 */
class UserHomePageActionImplTest extends Specification {

    // 测试对象
    UserHomePageActionImpl userHomePageAction

    def setupSpec() {
        // 标准I18n模拟设置
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        // 创建测试对象
        userHomePageAction = new UserHomePageActionImpl()
    }

    /**
     * 测试getMd5Version方法 - 各种输入情况
     * 这个测试验证getMd5Version方法在不同输入条件下的行为
     */
    @Unroll
    def "测试getMd5Version方法 - #scenario"() {
        given: "准备测试数据"
        def homePageLayout = layout
        def enterpriseId = 10086  // 添加默认企业ID参数

        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)

        then: "验证结果符合预期"
        result == expectedResult

        where:
        scenario                | layout                                | expectedResult
        "输入为null"            | null                                  | null
        "基本属性"              | createBasicHomePageLayout(1, 123456L) | DigestUtils.md5Hex("1123456")
        "包含customerLayout"    | createHomePageLayoutWithCustomerLayout(1, 123456L, createSimpleJsonObject()) | DigestUtils.md5Hex("1123456" + createSimpleJsonObject().toJSONString())
    }

    /**
     * 测试getMd5Version方法 - 异常情况下返回UUID
     */
    def "测试getMd5Version方法 - 异常情况下返回UUID"() {
        given: "准备会抛出异常的测试数据"
        def homePageLayout = Mock(HomePageLayoutTO)
        homePageLayout.getStatus() >> { throw new RuntimeException("测试异常") }
        def enterpriseId = 10086  // 添加企业ID参数

        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)

        then: "验证方法在异常情况下返回一个UUID格式的字符串"
        result != null
        // 验证返回的是UUID格式的字符串
        result.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")
    }

    /**
     * 测试getMd5Version方法 - 复杂的customerLayout
     */
    def "测试getMd5Version方法 - 复杂的customerLayout"() {
        given: "准备包含复杂customerLayout的测试数据"
        def complexJson = new JSONObject()
        complexJson.put("components", createComponentsArray())
        complexJson.put("layout", "complex")
        complexJson.put("version", "1.0")

        def homePageLayout = createHomePageLayoutWithCustomerLayout(2, 987654L, complexJson)
        def enterpriseId = 10086  // 添加企业ID参数

        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)

        then: "验证结果符合预期"
        result != null
        result == DigestUtils.md5Hex("2987654" + complexJson.toJSONString())
    }

    /**
     * 测试getMd5Version方法 - customerLayout为null
     */
    def "测试getMd5Version方法 - customerLayout为null"() {
        given: "准备customerLayout为null的测试数据"
        def homePageLayout = createBasicHomePageLayout(3, 555555L)
        homePageLayout.setCustomerLayout(null)
        def enterpriseId = 10086  // 添加企业ID参数

        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)

        then: "验证结果符合预期"
        result != null
        result == DigestUtils.md5Hex("3555555")
    }
    
    /**
     * 测试getMd5Version方法 - 包含customerLayoutList
     * 这个测试验证当前方法在处理包含customerLayoutList的情况下的行为
     * 注意：新实现优先考虑customerLayoutList
     */
    def "测试getMd5Version方法 - 包含customerLayoutList"() {
        given: "准备包含customerLayoutList的测试数据"
        def homePageLayout = createBasicHomePageLayout(4, 888888L)
        def customerLayout = createSimpleJsonObject()
        homePageLayout.setCustomerLayout(customerLayout)
        
        def customerLayoutList = [
            createLayoutJson("layout1"),
            createLayoutJson("layout2")
        ]
        homePageLayout.setCustomerLayoutList(customerLayoutList)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        
        then: "验证结果符合新实现的预期（优先考虑customerLayoutList）"
        result != null
        def expectedMd5 = DigestUtils.md5Hex("4888888" + customerLayoutList[0].toJSONString() + customerLayoutList[1].toJSONString())
        result == expectedMd5
    }
    
    /**
     * 测试getMd5Version方法 - customerLayoutList变更
     * 这个测试验证当customerLayoutList变更时，MD5值会相应变化
     */
    def "测试getMd5Version方法 - customerLayoutList变更"() {
        given: "准备两个只有customerLayoutList不同的HomePageLayout"
        def homePageLayout1 = createBasicHomePageLayout(5, 999999L)
        def customerLayout = createSimpleJsonObject()
        homePageLayout1.setCustomerLayout(customerLayout)
        
        def customerLayoutList1 = [
            createLayoutJson("layout1"),
            createLayoutJson("layout2")
        ]
        homePageLayout1.setCustomerLayoutList(customerLayoutList1)
        
        def homePageLayout2 = createBasicHomePageLayout(5, 999999L)
        homePageLayout2.setCustomerLayout(customerLayout)
        
        def customerLayoutList2 = [
            createLayoutJson("layout1"),
            createLayoutJson("layout2"),
            createLayoutJson("layout3") // 增加了一个布局
        ]
        homePageLayout2.setCustomerLayoutList(customerLayoutList2)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "分别计算两个HomePageLayout的MD5值"
        def result1 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout1, enterpriseId)
        def result2 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout2, enterpriseId)
        
        then: "验证新实现下两个MD5值不同（考虑customerLayoutList的变化）"
        result1 != result2
    }

    /**
     * 测试getMd5Version方法 - 只有customerLayoutList
     * 这个测试验证当只有customerLayoutList而没有customerLayout时的情况
     */
    def "测试getMd5Version方法 - 只有customerLayoutList"() {
        given: "准备只有customerLayoutList的测试数据"
        def homePageLayout = createBasicHomePageLayout(6, 777777L)
        homePageLayout.setCustomerLayout(null)
        
        def customerLayoutList = [
            createLayoutJson("layout1"),
            createLayoutJson("layout2")
        ]
        homePageLayout.setCustomerLayoutList(customerLayoutList)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        
        then: "验证结果符合新实现的预期（考虑customerLayoutList）"
        result != null
        def expectedMd5 = DigestUtils.md5Hex("6777777" + customerLayoutList[0].toJSONString() + customerLayoutList[1].toJSONString())
        result == expectedMd5
    }
    
    /**
     * 测试getMd5Version方法 - 当status和updateTime相同但customerLayout不同时
     */
    def "测试getMd5Version方法 - status和updateTime相同但customerLayout不同"() {
        given: "准备两个status和updateTime相同但customerLayout不同的HomePageLayout"
        def homePageLayout1 = createBasicHomePageLayout(7, 123123L)
        def customerLayout1 = createSimpleJsonObject()
        customerLayout1.put("title", "布局1")
        homePageLayout1.setCustomerLayout(customerLayout1)
        
        def homePageLayout2 = createBasicHomePageLayout(7, 123123L)
        def customerLayout2 = createSimpleJsonObject()
        customerLayout2.put("title", "布局2")
        homePageLayout2.setCustomerLayout(customerLayout2)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "分别计算两个HomePageLayout的MD5值"
        def result1 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout1, enterpriseId)
        def result2 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout2, enterpriseId)
        
        then: "验证两个MD5值不同（考虑customerLayout的变化）"
        result1 != result2
    }
    
    /**
     * 测试getMd5Version方法 - 当status不同但其他相同时
     */
    def "测试getMd5Version方法 - status不同但其他相同时"() {
        given: "准备两个只有status不同的HomePageLayout"
        def homePageLayout1 = createBasicHomePageLayout(8, 456456L)
        def customerLayout = createSimpleJsonObject()
        homePageLayout1.setCustomerLayout(customerLayout)
        
        def homePageLayout2 = createBasicHomePageLayout(9, 456456L)
        homePageLayout2.setCustomerLayout(customerLayout)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "分别计算两个HomePageLayout的MD5值"
        def result1 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout1, enterpriseId)
        def result2 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout2, enterpriseId)
        
        then: "验证两个MD5值不同（考虑status的变化）"
        result1 != result2
    }
    
    /**
     * 测试getMd5Version方法 - 当updateTime不同但其他相同时
     */
    def "测试getMd5Version方法 - updateTime不同但其他相同时"() {
        given: "准备两个只有updateTime不同的HomePageLayout"
        def homePageLayout1 = createBasicHomePageLayout(10, 111111L)
        def customerLayout = createSimpleJsonObject()
        homePageLayout1.setCustomerLayout(customerLayout)
        
        def homePageLayout2 = createBasicHomePageLayout(10, 222222L)
        homePageLayout2.setCustomerLayout(customerLayout)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "分别计算两个HomePageLayout的MD5值"
        def result1 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout1, enterpriseId)
        def result2 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout2, enterpriseId)
        
        then: "验证两个MD5值不同（考虑updateTime的变化）"
        result1 != result2
    }
    
    /**
     * 测试getMd5Version方法 - customerLayoutList为空列表时回退到customerLayout
     */
    def "测试getMd5Version方法 - customerLayoutList为空列表"() {
        given: "准备customerLayoutList为空列表的测试数据"
        def homePageLayout = createBasicHomePageLayout(11, 333333L)
        def customerLayout = createSimpleJsonObject()
        homePageLayout.setCustomerLayout(customerLayout)
        homePageLayout.setCustomerLayoutList([])
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        
        then: "验证结果符合预期（回退到使用customerLayout）"
        result == DigestUtils.md5Hex("11333333" + customerLayout.toJSONString())
    }
    
    /**
     * 测试getMd5Version方法 - customerLayoutList中包含null元素
     */
    def "测试getMd5Version方法 - customerLayoutList包含null元素"() {
        given: "准备customerLayoutList包含null元素的测试数据"
        def homePageLayout = createBasicHomePageLayout(12, 444444L)
        
        def customerLayoutList = [
            createLayoutJson("layout1"),
            null,
            createLayoutJson("layout3")
        ]
        homePageLayout.setCustomerLayoutList(customerLayoutList)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        
        then: "验证结果符合预期（忽略null元素）"
        result == DigestUtils.md5Hex("12444444" + customerLayoutList[0].toJSONString() + customerLayoutList[2].toJSONString())
    }
    
    /**
     * 测试getMd5Version方法 - 多种异常情况下返回UUID
     */
    def "测试getMd5Version方法 - 多种异常情况下返回UUID"() {
        given: "准备可能产生不同类型异常的测试数据"
        def homePageLayout = Mock(HomePageLayoutTO)
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "模拟不同类型的异常"
        homePageLayout.getStatus() >> { throw exceptionType.newInstance(exceptionMessage) }
        def result = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        
        then: "验证各种异常情况下都会返回UUID格式的字符串"
        result != null
        result.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")
        
        where:
        exceptionType         | exceptionMessage
        NullPointerException  | "空指针异常"
        IllegalStateException | "非法状态异常"
        RuntimeException      | "运行时异常"
    }
    
    /**
     * 测试getMd5Version方法 - 不同线程调用时异常的返回值是唯一的
     */
    def "测试getMd5Version方法 - 不同调用异常时生成的UUID是唯一的"() {
        given: "准备会抛出异常的两个不同的测试对象"
        def homePageLayout1 = Mock(HomePageLayoutTO)
        homePageLayout1.getStatus() >> { throw new RuntimeException("测试异常1") }
        
        def homePageLayout2 = Mock(HomePageLayoutTO)
        homePageLayout2.getStatus() >> { throw new RuntimeException("测试异常2") }
        def enterpriseId = 10086  // 添加企业ID参数
        
        when: "分别调用getMd5Version方法"
        def result1 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout1, enterpriseId)
        def result2 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout2, enterpriseId)
        
        then: "验证两次调用返回的UUID是不同的"
        result1 != null
        result2 != null
        result1 != result2
        result1.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")
        result2.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")
    }

    /**
     * 测试getMd5Version方法 - 验证真实数据的MD5计算一致性
     * 该测试使用实际生产中的数据结构
     */
    def "测试getMd5Version方法 - 复杂的实际数据结构"() {
        given: "准备复杂的真实数据结构"
        def homePageLayout = createBasicHomePageLayout(30, 123456789L)
        
        // 创建复杂的customerLayoutList，与用户提供的数据结构相同
        def customerLayoutList = [
            createRealLayoutJSON1(),
            createRealLayoutJSON2(),
            createRealLayoutJSON3()
        ]
        homePageLayout.setCustomerLayoutList(customerLayoutList)
        
        // 设置企业ID参数
        def enterpriseId = 10086
        
        when: "多次计算MD5值"
        def result1 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        def result2 = Whitebox.invokeMethod(userHomePageAction, "getMd5Version", homePageLayout, enterpriseId)
        
        then: "验证两次计算结果一致"
        result1 == result2
        
        and: "输出结果以供查看"
        println("真实数据的MD5计算结果: " + result1)
        
        // 验证stringBuilder的内容，用于与用户提供的数据比较
        def stringBuilderContent = captureStringBuilderContent(homePageLayout, enterpriseId)
        println("真实数据构建的stringBuilder内容长度: " + stringBuilderContent.length())
    }

    /**
     * 捕获getMd5Version方法中StringBuilder的内容
     * 这个方法模拟getMd5Version方法内部的逻辑，用于获取构建MD5之前的完整字符串
     */
    private String captureStringBuilderContent(HomePageLayoutTO homePageLayout, Integer enterpriseId) {
        try {
            if (homePageLayout == null) {
                return null
            }
            StringBuilder stringBuilder = new StringBuilder()
            stringBuilder.append(homePageLayout.getStatus()).append(homePageLayout.getUpdateTime())
            
            // 优先使用customerLayoutList计算MD5
            List<JSONObject> customerLayoutList = homePageLayout.getCustomerLayoutList()
            if (customerLayoutList != null && !customerLayoutList.isEmpty()) {
                // 将所有布局数据添加到MD5计算中
                for (JSONObject layoutItem : customerLayoutList) {
                    if (layoutItem != null) {
                        stringBuilder.append(layoutItem)
                    }
                }
            } else {
                // 如果customerLayoutList为空，则使用customerLayout（保持兼容性）
                JSONObject customerLayout = homePageLayout.getCustomerLayout()
                if (customerLayout != null) {
                    stringBuilder.append(customerLayout.toJSONString())
                }
            }
            return stringBuilder.toString()
        } catch (Exception e) {
            return "捕获内容时出错: " + e.getMessage()
        }
    }

    // 辅助方法 - 创建基本的HomePageLayoutTO对象
    private HomePageLayoutTO createBasicHomePageLayout(int status, long updateTime) {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        homePageLayout.setStatus(status)
        homePageLayout.setUpdateTime(updateTime)
        return homePageLayout
    }

    // 辅助方法 - 创建带customerLayout的HomePageLayoutTO对象
    private HomePageLayoutTO createHomePageLayoutWithCustomerLayout(int status, long updateTime, JSONObject customerLayout) {
        def homePageLayout = createBasicHomePageLayout(status, updateTime)
        homePageLayout.setCustomerLayout(customerLayout)
        return homePageLayout
    }

    // 辅助方法 - 创建简单的JSONObject
    private JSONObject createSimpleJsonObject() {
        def json = new JSONObject()
        json.put("id", "test-id")
        json.put("name", "测试布局")
        json.put("type", "layout")
        return json
    }
    
    // 辅助方法 - 创建布局JSON对象
    private JSONObject createLayoutJson(String name) {
        def json = new JSONObject()
        json.put("id", UUID.randomUUID().toString())
        json.put("name", name)
        json.put("type", "layout")
        json.put("components", createComponentsArray())
        return json
    }
    
    // 辅助方法 - 创建组件数组
    private List<JSONObject> createComponentsArray() {
        def components = []
        def component1 = new JSONObject()
        component1.put("id", "comp-1")
        component1.put("type", "text")
        component1.put("content", "测试内容1")
        
        def component2 = new JSONObject()
        component2.put("id", "comp-2")
        component2.put("type", "button")
        component2.put("label", "按钮")
        
        components.add(component1)
        components.add(component2)
        
        return components
    }
}