package com.facishare.webpage.customer.controller.impl

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.facishare.webpage.customer.api.constant.CustomerLayoutField
import com.facishare.webpage.customer.api.model.HomePageLayoutTO
import com.facishare.webpage.customer.constant.ComponentConstant
import com.facishare.webpage.customer.constant.DataVersion
import com.facishare.webpage.customer.model.component.CustomerLayoutHelper
import com.facishare.webpage.customer.remote.TempFileToFormalFile
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.apache.commons.codec.digest.DigestUtils
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * TenantHomePageActionImpl单元测试
 * 测试tempFileToFormalFile方法在不同场景下的行为
 */
class TenantHomePageActionImplTest extends Specification {
    
    // 测试对象
    TenantHomePageActionImpl tenantHomePageAction
    
    // 模拟依赖
    TempFileToFormalFile mockTempFileToFormalFile
    
    def setupSpec() {
        // 标准I18n模拟设置
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        // 创建模拟对象
        mockTempFileToFormalFile = Mock(TempFileToFormalFile)
        mockTempFileToFormalFile.tempFileToFormalFile(_, _, _, _) >> { args ->
            def paths = args[2]
            def result = [:]
            paths.each { path -> result[path] = "converted_" + path }
            return result
        }
        
        // 创建测试对象
        tenantHomePageAction = new TenantHomePageActionImpl()
        
        // 注入依赖
        Whitebox.setInternalState(tenantHomePageAction, "tempFileToFormalFile", mockTempFileToFormalFile)
    }
    
    /**
     * 测试边界条件：null输入
     */
    def "测试边界条件 - null输入"() {
        given: "准备测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, null)
        
        then: "方法应正常执行，无异常"
        noExceptionThrown()
    }
    
    /**
     * 测试边界条件：dataVersion100
     */
    def "测试边界条件 - dataVersion100"() {
        given: "准备测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithVersion(100)
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "方法应正常执行，无异常"
        noExceptionThrown()
    }
    
    /**
     * 测试导航组件处理
     */
    def "测试导航组件处理"() {
        given: "准备带导航组件的测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithComponents(ComponentConstant.NAVIGATE, 0, false)
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证导航组件中的图标路径被正确转换"
        def components = new CustomerLayoutHelper(homePageLayout.getCustomerLayout()).getComponents()
        components.size() > 0
        def menus = JSONObject.parseArray(components[0].getString(CustomerLayoutField.menus))
        menus.size() > 0
        menus[0].getString(CustomerLayoutField.newIcon) == "converted_TN_testNavigateIcon"
    }
    
    /**
     * 测试幻灯片组件处理
     */
    def "测试幻灯片组件处理"() {
        given: "准备带幻灯片组件的测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithComponents(ComponentConstant.slideImage, 0, false)
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证幻灯片组件中的图片路径被正确转换"
        def components = new CustomerLayoutHelper(homePageLayout.getCustomerLayout()).getComponents()
        components.size() > 0
        def imgs = components[0].getJSONArray(CustomerLayoutField.imgs)
        imgs.size() > 0
        imgs[0].getString(CustomerLayoutField.img) == "converted_TN_testSlideImage"
    }
    
    /**
     * 测试其他类型组件处理
     */
    def "测试其他类型组件处理"() {
        given: "准备带其他类型组件的测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithComponents("otherType", 0, false)
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证组件未被特殊处理"
        def components = new CustomerLayoutHelper(homePageLayout.getCustomerLayout()).getComponents()
        components.size() > 0
        components[0].getString(CustomerLayoutField.type) == "otherType"
    }
    
    /**
     * 测试多标签页处理 - 不包含customerLayoutList
     */
    def "测试多标签页处理 - 不包含customerLayoutList"() {
        given: "准备多标签页测试数据，但不包含customerLayoutList"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithMultiType(0, false)
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证主布局被处理，但没有处理customerLayoutList"
        def mainComponents = new CustomerLayoutHelper(homePageLayout.getCustomerLayout()).getComponents()
        mainComponents.size() > 0
        def menus = JSONObject.parseArray(mainComponents[0].getString(CustomerLayoutField.menus))
        menus.size() > 0
        menus[0].getString(CustomerLayoutField.newIcon) == "converted_TN_testNavigateIcon"
        
        homePageLayout.getCustomerLayoutList() == null || homePageLayout.getCustomerLayoutList().isEmpty()
    }
    
    /**
     * 测试多标签页处理 - 包含customerLayoutList
     */
    def "测试多标签页处理 - 包含customerLayoutList"() {
        given: "准备多标签页测试数据，包含customerLayoutList"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithMultiType(1, true)
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证主布局和customerLayoutList都被处理"
        // 验证主布局被处理
        def mainComponents = new CustomerLayoutHelper(homePageLayout.getCustomerLayout()).getComponents()
        mainComponents.size() > 0
        def menus = JSONObject.parseArray(mainComponents[0].getString(CustomerLayoutField.menus))
        menus.size() > 0
        menus[0].getString(CustomerLayoutField.newIcon) == "converted_TN_testNavigateIcon"
        
        // 验证customerLayoutList被处理
        homePageLayout.getCustomerLayoutList() != null
        homePageLayout.getCustomerLayoutList().size() > 0
        
        def customerLayoutList = homePageLayout.getCustomerLayoutList()
        def slideComponents = new CustomerLayoutHelper(customerLayoutList[0]).getComponents()
        slideComponents.size() > 0
        def imgs = slideComponents[0].getJSONArray(CustomerLayoutField.imgs)
        imgs.size() > 0
        imgs[0].getString(CustomerLayoutField.img) == "converted_TN_testSlideImage"
    }
    
    /**
     * 测试空组件列表处理
     */
    def "测试空组件列表处理"() {
        given: "准备带空组件列表的测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithEmptyComponents()
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证方法正确处理了空组件情况"
        homePageLayout.customerLayout != null
        def components = new CustomerLayoutHelper(homePageLayout.getCustomerLayout()).getComponents()
        components.size() == 0
    }
    
    /**
     * 测试customerLayout解析为null的情况
     */
    def "测试customerLayout解析为null的情况"() {
        given: "准备解析为null的测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        homePageLayout.setCustomerLayout(new JSONObject())
        
        and: "模拟JSONObject.parseObject返回null"
        GroovySpy(JSONObject, global: true)
        JSONObject.parseObject(_ as String) >> null
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证方法正确处理了解析失败情况"
        notThrown(Exception)
    }
    
    /**
     * 测试多顶导航时，组件为空的顶导航被保留
     * 这个测试验证当有多个顶导航时，即使某个顶导航的组件为空，它也应该被保留在结果中
     */
    def "测试多顶导航时，组件为空的顶导航被保留"() {
        given: "准备包含空组件顶导航的测试数据"
        def enterpriseAccount = "testAccount"
        def employeeId = 12345
        def homePageLayout = createHomePageLayoutWithEmptyComponentsTab()
        
        when: "调用tempFileToFormalFile方法"
        Whitebox.invokeMethod(tenantHomePageAction, "tempFileToFormalFile", enterpriseAccount, employeeId, homePageLayout)
        
        then: "验证组件为空的顶导航被保留"
        // 验证customerLayoutList不为空且包含预期数量的元素
        homePageLayout.getCustomerLayoutList() != null
        homePageLayout.getCustomerLayoutList().size() == 2
        
        // 验证第一个顶导航（空组件）被保留
        def emptyTabLayout = homePageLayout.getCustomerLayoutList()[0]
        def emptyComponents = new CustomerLayoutHelper(emptyTabLayout).getComponents()
        emptyComponents.size() == 0
        
        // 验证第二个顶导航（有组件）被正确处理
        def nonEmptyTabLayout = homePageLayout.getCustomerLayoutList()[1]
        def nonEmptyComponents = new CustomerLayoutHelper(nonEmptyTabLayout).getComponents()
        nonEmptyComponents.size() > 0
        def imgs = nonEmptyComponents[0].getJSONArray(CustomerLayoutField.imgs)
        imgs.size() > 0
        imgs[0].getString(CustomerLayoutField.img) == "converted_TN_testSlideImage"
    }
    
    /**
     * 测试getMd5Version方法 - 各种输入情况
     * 这个测试验证getMd5Version方法在不同输入条件下的行为
     */
    @Unroll
    def "测试getMd5Version方法 - #scenario"() {
        given: "准备测试数据"
        def homePageLayoutTOList = layoutList instanceof Closure ? layoutList.call() : layoutList
        def personPageConfig = personalConfig
        
        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(tenantHomePageAction, "getMd5Version", [homePageLayoutTOList, personPageConfig] as Object[])
        
        then: "验证结果符合预期"
        if (expectedResult instanceof Closure) {
            assert result == expectedResult.call()
        } else {
            assert result == expectedResult
        }
        
        where:
        scenario                | layoutList                                | personalConfig | expectedResult
        "两个参数都为null"       | null                                      | null           | null
        "只有布局列表为null"     | null                                      | true           | { DigestUtils.md5Hex("true") }
        "只有个人配置为null"     | [createSimpleHomePageLayout("test")]      | null           | { DigestUtils.md5Hex("[${createSimpleHomePageLayout("test")}]") }
        "两个参数都有值"         | [createSimpleHomePageLayout("test")]      | false          | { DigestUtils.md5Hex("[${createSimpleHomePageLayout("test")}]false") }
        "布局列表为空列表"       | []                                        | true           | { DigestUtils.md5Hex("true") }
    }
    
    /**
     * 测试getMd5Version方法 - 多个布局的情况
     */
    def "测试getMd5Version方法 - 多个布局"() {
        given: "准备包含多个布局的测试数据"
        def homePageLayout1 = createSimpleHomePageLayout("layout1")
        def homePageLayout2 = createSimpleHomePageLayout("layout2")
        def homePageLayoutTOList = [homePageLayout1, homePageLayout2]
        def personPageConfig = true
        
        when: "调用getMd5Version方法"
        def result = Whitebox.invokeMethod(tenantHomePageAction, "getMd5Version", [homePageLayoutTOList, personPageConfig] as Object[])
        
        then: "验证结果符合预期"
        result != null
        result == DigestUtils.md5Hex("[${homePageLayout1}, ${homePageLayout2}]true")
    }
    
    // 辅助方法 - 创建具有特定版本的HomePageLayoutTO对象
    private HomePageLayoutTO createHomePageLayoutWithVersion(int version) {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(version)
        return homePageLayout
    }
    
    // 辅助方法 - 创建具有指定组件类型的HomePageLayoutTO对象
    private HomePageLayoutTO createHomePageLayoutWithComponents(String componentType, int multiType, boolean includeCustomerLayoutList) {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        homePageLayout.setPageMultiType(multiType)
        
        // 创建customerLayout
        def customerLayout = new JSONObject()
        def components = new JSONArray()
        
        // 添加指定类型的组件
        def component = new JSONObject()
        component.put(CustomerLayoutField.type, componentType)
        
        if (componentType == ComponentConstant.NAVIGATE) {
            // 模拟导航组件
            def menus = new JSONArray()
            def menu = new JSONObject()
            menu.put(CustomerLayoutField.newIcon, "TN_testNavigateIcon")
            menus.add(menu)
            component.put(CustomerLayoutField.menus, menus.toString())
        } else if (componentType == ComponentConstant.slideImage) {
            // 模拟轮播图组件
            def imgs = new JSONArray()
            def img = new JSONObject()
            img.put(CustomerLayoutField.img, "TN_testSlideImage")
            imgs.add(img)
            component.put(CustomerLayoutField.imgs, imgs)
        }
        
        components.add(component)
        customerLayout.put(CustomerLayoutField.components, components)
        homePageLayout.setCustomerLayout(customerLayout)
        
        // 如果需要，添加customerLayoutList
        if (includeCustomerLayoutList) {
            def customerLayoutList = new ArrayList<JSONObject>()
            // 为了测试方便，使用与主布局相同的内容
            customerLayoutList.add(new JSONObject(customerLayout))
            homePageLayout.setCustomerLayoutList(customerLayoutList)
        }
        
        return homePageLayout
    }
    
    // 辅助方法 - 创建具有多标签页的HomePageLayoutTO对象
    private HomePageLayoutTO createHomePageLayoutWithMultiType(int multiType, boolean hasCustomerLayoutList) {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        homePageLayout.setPageMultiType(multiType)
        
        // 创建主customerLayout，带导航组件
        def mainCustomerLayout = new JSONObject()
        def mainComponents = new JSONArray()
        
        def navigateComponent = new JSONObject()
        navigateComponent.put(CustomerLayoutField.type, ComponentConstant.NAVIGATE)
        def menus = new JSONArray()
        def menu = new JSONObject()
        menu.put(CustomerLayoutField.newIcon, "TN_testNavigateIcon")
        menus.add(menu)
        navigateComponent.put(CustomerLayoutField.menus, menus.toString())
        
        mainComponents.add(navigateComponent)
        mainCustomerLayout.put(CustomerLayoutField.components, mainComponents)
        homePageLayout.setCustomerLayout(mainCustomerLayout)
        
        // 如果需要，添加customerLayoutList
        if (hasCustomerLayoutList) {
            def customerLayoutList = new ArrayList<JSONObject>()
            
            // 添加一个带幻灯片组件的布局
            def slideLayout = new JSONObject()
            def slideComponents = new JSONArray()
            
            def slideComponent = new JSONObject()
            slideComponent.put(CustomerLayoutField.type, ComponentConstant.slideImage)
            def imgs = new JSONArray()
            def img = new JSONObject()
            img.put(CustomerLayoutField.img, "TN_testSlideImage")
            imgs.add(img)
            slideComponent.put(CustomerLayoutField.imgs, imgs)
            
            slideComponents.add(slideComponent)
            slideLayout.put(CustomerLayoutField.components, slideComponents)
            customerLayoutList.add(slideLayout)
            
            homePageLayout.setCustomerLayoutList(customerLayoutList)
        }
        
        return homePageLayout
    }
    
    // 辅助方法 - 创建包含空组件列表的HomePageLayoutTO对象
    private HomePageLayoutTO createHomePageLayoutWithEmptyComponents() {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        
        // 创建customerLayout，组件列表为空
        def customerLayout = new JSONObject()
        customerLayout.put(CustomerLayoutField.components, new JSONArray())
        homePageLayout.setCustomerLayout(customerLayout)
        
        return homePageLayout
    }
    
    // 辅助方法 - 创建包含空组件顶导航的HomePageLayoutTO对象
    private HomePageLayoutTO createHomePageLayoutWithEmptyComponentsTab() {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        homePageLayout.setPageMultiType(1) // 设置为多标签页模式
        
        // 创建主customerLayout，带导航组件
        def mainCustomerLayout = new JSONObject()
        def mainComponents = new JSONArray()
        
        def navigateComponent = new JSONObject()
        navigateComponent.put(CustomerLayoutField.type, ComponentConstant.NAVIGATE)
        def menus = new JSONArray()
        def menu = new JSONObject()
        menu.put(CustomerLayoutField.newIcon, "TN_testNavigateIcon")
        menus.add(menu)
        navigateComponent.put(CustomerLayoutField.menus, menus.toString())
        
        mainComponents.add(navigateComponent)
        mainCustomerLayout.put(CustomerLayoutField.components, mainComponents)
        homePageLayout.setCustomerLayout(mainCustomerLayout)
        
        // 创建customerLayoutList，包含一个空组件的顶导航和一个有组件的顶导航
        def customerLayoutList = new ArrayList<JSONObject>()
        
        // 添加一个空组件的顶导航
        def emptyLayout = new JSONObject()
        emptyLayout.put(CustomerLayoutField.components, new JSONArray())
        customerLayoutList.add(emptyLayout)
        
        // 添加一个带幻灯片组件的顶导航
        def slideLayout = new JSONObject()
        def slideComponents = new JSONArray()
        
        def slideComponent = new JSONObject()
        slideComponent.put(CustomerLayoutField.type, ComponentConstant.slideImage)
        def imgs = new JSONArray()
        def img = new JSONObject()
        img.put(CustomerLayoutField.img, "TN_testSlideImage")
        imgs.add(img)
        slideComponent.put(CustomerLayoutField.imgs, imgs)
        
        slideComponents.add(slideComponent)
        slideLayout.put(CustomerLayoutField.components, slideComponents)
        customerLayoutList.add(slideLayout)
        
        homePageLayout.setCustomerLayoutList(customerLayoutList)
        
        return homePageLayout
    }
    
    // 辅助方法 - 创建简单的HomePageLayoutTO对象用于MD5测试
    private HomePageLayoutTO createSimpleHomePageLayout(String identifier) {
        def homePageLayout = new HomePageLayoutTO()
        homePageLayout.setDataVersion(DataVersion.dataVersion_200)
        homePageLayout.setLayoutId(identifier)
        return homePageLayout
    }
} 