package com.facishare.webpage.customer.metadata

import com.facishare.qixin.common.monitor.SlowLog
import com.facishare.qixin.permission.unified.common.CheckHasPermissionService
import com.facishare.webpage.customer.api.model.PaaSAppVO
import com.facishare.webpage.customer.common.CheckService
import com.facishare.webpage.customer.common.LanguageService
import com.facishare.webpage.customer.config.AppMenuConfig
import com.facishare.webpage.customer.config.AppTypeConfig
import com.facishare.webpage.customer.service.PaaSAppService
import com.fxiaoke.appcenter.restapi.arg.CanAccessComponentArg
import com.fxiaoke.appcenter.restapi.common.BaseResult
import com.fxiaoke.appcenter.restapi.common.HeaderObj
import com.fxiaoke.appcenter.restapi.service.OpenFsUserAppViewService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification

import java.util.Locale

/**
 * PaaSWebAppService单元测试类
 */
class PaaSWebAppServiceTest extends Specification {

    // 被测服务
    PaaSWebAppService service
    
    // 依赖项
    PaaSAppService paaSAppService
    CheckHasPermissionService checkHasPermissionService
    OpenFsUserAppViewService openFsUserAppViewService
    AppMenuConfig appMenuConfig
    CheckService checkService
    AppTypeConfig appTypeConfig
    LanguageService languageService
    
    def setupSpec() {
        // 标准I18n模拟设置
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        // 初始化Mock依赖
        paaSAppService = Mock(PaaSAppService)
        checkHasPermissionService = Mock(CheckHasPermissionService)
        openFsUserAppViewService = Mock(OpenFsUserAppViewService)
        appMenuConfig = Mock(AppMenuConfig)
        checkService = Mock(CheckService)
        appTypeConfig = Mock(AppTypeConfig)
        languageService = Mock(LanguageService)
        
        // 创建并配置被测对象
        service = new PaaSWebAppService()
        service.paaSAppService = paaSAppService
        service.checkHasPermissionService = checkHasPermissionService
        service.openFsUserAppViewService = openFsUserAppViewService
        service.appMenuConfig = appMenuConfig
        service.checkService = checkService
        service.appTypeConfig = appTypeConfig
        service.languageService = languageService
    }
    
    /**
     * 测试hasPermission方法-成功调用openFsUserAppViewService.canAccessComponent且结果为true
     */
    def "hasPermission_调用canAccessComponent成功_结果为true"() {
        given: "准备测试数据"
        def tenantId = 1
        def enterpriseAccount = "testEA"
        def employeeId = 2
        def componentId = "CRM"
        def header = Mock(HeaderObj)
        
        and: "配置Mock行为"
        appMenuConfig.getCrmPermissionAppId() >> componentId
        HeaderObj.newInstance(Integer.toString(tenantId)) >> header
        
        def baseResult = Mock(BaseResult)
        baseResult.isSuccess() >> true
        baseResult.getResult() >> true
        
        openFsUserAppViewService.canAccessComponent(_ as HeaderObj, _ as CanAccessComponentArg) >> { args ->
            // 验证参数
            HeaderObj headerArg = args[0]
            CanAccessComponentArg componentArg = args[1]
            assert componentArg.getComponentId() == componentId
            return baseResult
        }
        
        when: "调用被测方法"
        def result = Whitebox.invokeMethod(service, "hasPermission", tenantId, enterpriseAccount, employeeId)
        
        then: "验证结果"
        result == true
        1 * openFsUserAppViewService.canAccessComponent(_, _)
    }
    
    /**
     * 测试hasPermission方法-成功调用openFsUserAppViewService.canAccessComponent且结果为false
     */
    def "hasPermission_调用canAccessComponent成功_结果为false"() {
        given: "准备测试数据"
        def tenantId = 1
        def enterpriseAccount = "testEA"
        def employeeId = 2
        def componentId = "CRM"
        def header = Mock(HeaderObj)
        
        and: "配置Mock行为"
        appMenuConfig.getCrmPermissionAppId() >> componentId
        HeaderObj.newInstance(Integer.toString(tenantId)) >> header
        
        def baseResult = Mock(BaseResult)
        baseResult.isSuccess() >> true
        baseResult.getResult() >> false
        
        openFsUserAppViewService.canAccessComponent(_ as HeaderObj, _ as CanAccessComponentArg) >> baseResult
        
        when: "调用被测方法"
        def result = Whitebox.invokeMethod(service, "hasPermission", tenantId, enterpriseAccount, employeeId)
        
        then: "验证结果"
        result == false
        1 * openFsUserAppViewService.canAccessComponent(_, _)
    }
    
    /**
     * 测试hasPermission方法-调用openFsUserAppViewService.canAccessComponent失败，返回降级结果true
     */
    def "hasPermission_调用canAccessComponent失败_返回降级值true"() {
        given: "准备测试数据"
        def tenantId = 1
        def enterpriseAccount = "testEA"
        def employeeId = 2
        def componentId = "CRM"
        def header = Mock(HeaderObj)
        
        and: "配置Mock行为"
        appMenuConfig.getCrmPermissionAppId() >> componentId
        HeaderObj.newInstance(Integer.toString(tenantId)) >> header
        
        def baseResult = Mock(BaseResult)
        baseResult.isSuccess() >> false
        
        openFsUserAppViewService.canAccessComponent(_ as HeaderObj, _ as CanAccessComponentArg) >> baseResult
        
        when: "调用被测方法"
        def result = Whitebox.invokeMethod(service, "hasPermission", tenantId, enterpriseAccount, employeeId)
        
        then: "验证降级结果"
        result == true
        1 * openFsUserAppViewService.canAccessComponent(_, _)
    }
    
    /**
     * 测试hasPermission方法-调用openFsUserAppViewService.canAccessComponent抛出异常，返回降级结果true
     */
    def "hasPermission_调用canAccessComponent抛出异常_返回降级值true"() {
        given: "准备测试数据"
        def tenantId = 1
        def enterpriseAccount = "testEA"
        def employeeId = 2
        def componentId = "CRM"
        def header = Mock(HeaderObj)
        
        and: "配置Mock行为"
        appMenuConfig.getCrmPermissionAppId() >> componentId
        HeaderObj.newInstance(Integer.toString(tenantId)) >> header
        
        openFsUserAppViewService.canAccessComponent(_ as HeaderObj, _ as CanAccessComponentArg) >> { args ->
            throw new RuntimeException("模拟接口异常")
        }
        
        when: "调用被测方法"
        def result = Whitebox.invokeMethod(service, "hasPermission", tenantId, enterpriseAccount, employeeId)
        
        then: "验证降级结果"
        result == true
        1 * openFsUserAppViewService.canAccessComponent(_, _)
    }
    
    /**
     * 测试getUserMainChannelMetaDataList方法-调用hasPermission返回true的情况
     */
    def "getUserMainChannelMetaDataList_hasPermission返回true_返回应用列表"() {
        given: "准备测试数据"
        def tenantId = 1
        def enterpriseAccount = "testEA"
        def employeeId = 2
        def locale = Locale.CHINESE
        def stopWatch = Mock(SlowLog)
        def paasAppList = [new PaaSAppVO(appId: "app1", name: "应用1", description: "描述1")]
        def appTypeTransKeyMap = [innerApp: "innerApp.key", other: "other.key"]
        def menuLanguages = [:]
        
        and: "配置Mock行为"
        // 配置hasPermission方法返回true
        def baseResult = Mock(BaseResult)
        baseResult.isSuccess() >> true
        baseResult.getResult() >> true
        appMenuConfig.getCrmPermissionAppId() >> "CRM"
        HeaderObj.newInstance(Integer.toString(tenantId)) >> Mock(HeaderObj)
        openFsUserAppViewService.canAccessComponent(_ as HeaderObj, _ as CanAccessComponentArg) >> baseResult
        
        // 配置其他依赖方法
        paaSAppService.getUserPaaSAppList(tenantId, employeeId, locale) >> paasAppList
        checkService.checkGoNewCRM(tenantId) >> true
        appTypeConfig.getAppTypeTransKeyMap() >> appTypeTransKeyMap
        languageService.queryLanguageByNameI18nKeys(tenantId, _, locale) >> menuLanguages
        
        when: "调用被测方法"
        def result = service.getUserMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch)
        
        then: "验证结果"
        result.size() == 1
        result[0].appId == "app1"
        1 * openFsUserAppViewService.canAccessComponent(_, _)
    }

    /**
     * 测试getUserMainChannelMetaDataList方法-调用hasPermission返回false的情况
     */
    def "getUserMainChannelMetaDataList_hasPermission返回false_返回空列表"() {
        given: "准备测试数据"
        def tenantId = 1
        def enterpriseAccount = "testEA"
        def employeeId = 2
        def locale = Locale.CHINESE
        def stopWatch = Mock(SlowLog)
        
        and: "配置Mock行为"
        // 配置hasPermission方法返回false
        def baseResult = Mock(BaseResult)
        baseResult.isSuccess() >> true
        baseResult.getResult() >> false
        appMenuConfig.getCrmPermissionAppId() >> "CRM"
        HeaderObj.newInstance(Integer.toString(tenantId)) >> Mock(HeaderObj)
        openFsUserAppViewService.canAccessComponent(_ as HeaderObj, _ as CanAccessComponentArg) >> baseResult
        
        when: "调用被测方法"
        def result = service.getUserMainChannelMetaDataList(tenantId, enterpriseAccount, employeeId, locale, stopWatch)
        
        then: "验证返回空列表"
        result.isEmpty()
        1 * openFsUserAppViewService.canAccessComponent(_, _)
        0 * paaSAppService.getUserPaaSAppList(_, _, _) // 确认不会调用后续方法
    }
} 