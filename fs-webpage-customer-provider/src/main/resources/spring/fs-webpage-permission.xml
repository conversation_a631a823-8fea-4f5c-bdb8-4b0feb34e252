<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="objPermissionService" class="com.facishare.qixin.permission.unified.service.ObjPermissionServiceImpl"/>
    <bean id="rolePermissionService" class="com.facishare.qixin.permission.unified.service.RolePermissionServiceImpl"/>
    <bean id="devicePermissionService" class="com.facishare.qixin.permission.unified.service.DevicePermissionServiceImpl"/>
    <bean id="businessPermission" class="com.facishare.qixin.permission.unified.service.BusinessPermissionImpl"/>
    <bean id="enterpriseSourcePermissionService" class="com.facishare.qixin.permission.unified.service.EnterpriseSourcePermissionServiceImpl"/>
    <bean id="managerPermissionService" class="com.facishare.qixin.permission.unified.service.ManagerPermissionServiceImpl"/>
    <bean id="appOpenPermissionService" class="com.facishare.qixin.permission.unified.service.AppOpenPermissionServiceImpl"/>
    <bean id="licenseModulePermission" class="com.facishare.qixin.permission.unified.service.LicenseModulePermissionImpl"/>
    <bean id="licenseVersionService" class="com.facishare.qixin.permission.unified.service.LicenseVersionServiceImpl"/>
    <bean id="permissionUnifiedService" class="com.facishare.qixin.permission.unified.service.PermissionUnifiedServiceImpl" init-method="init">
        <property name="combinePermissionServiceList">
            <list>
                <ref bean="appOpenPermissionService"/>
                <ref bean="managerPermissionService"/>
                <ref bean="enterpriseSourcePermissionService"/>
                <ref bean="objPermissionService"/>
                <ref bean="rolePermissionService"/>
                <ref bean="devicePermissionService"/>
                <ref bean="businessPermission"/>
                <ref bean="licenseVersionService"/>
                <ref bean="licenseModulePermission"/>
            </list>

        </property>
    </bean>
    <import resource="classpath:fs-permission-service.xml"/>

</beans>