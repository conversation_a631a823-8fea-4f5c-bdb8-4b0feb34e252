package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.controller.model.UserMenuTempleVo;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON>yi on 2019/9/9.
 */
@Data
public class GetUserMenuByAppIdResult extends BaseResult {
    @SerializedName("menus")
    private List<UserMenuTempleVo> userMenuTempleVos;

    @SerializedName("incomplete")
    private boolean incomplete = false;
}
