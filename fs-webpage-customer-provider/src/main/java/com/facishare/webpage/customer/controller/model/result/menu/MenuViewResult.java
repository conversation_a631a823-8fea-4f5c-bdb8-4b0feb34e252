package com.facishare.webpage.customer.controller.model.result.menu;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.controller.model.MenuTempleVO;
import com.facishare.webpage.customer.controller.model.TenantMenuItemVO;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/24.
 */
@Data
public class MenuViewResult extends BaseResult {
   private MenuTempleVO objectData;

   private List<TenantMenuItemVO> detailMenuItems;

   private List<String> roleIdList;

   private List<Scope> scopeList;
}
