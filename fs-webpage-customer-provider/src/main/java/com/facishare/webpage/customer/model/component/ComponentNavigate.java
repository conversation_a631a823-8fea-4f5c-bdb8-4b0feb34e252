package com.facishare.webpage.customer.model.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.core.component.Component;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.ComponentTypConst;
import com.facishare.webpage.customer.core.util.DropListUtil;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Map;

/**
 * Created by zhangyu on 2020/6/30
 */
@Data
public class ComponentNavigate extends Component {

    private ComponentDto componentDto;

    private Map<String, String> componentLanguage;

    @Override
    public String getId() {
        return componentDto.getId();
    }

    @Override
    public String getPId() {
        return componentDto.getParentId();
    }

    @Override
    public String getName() {
        return DropListUtil.getDropListName(componentDto, componentLanguage);
    }

    @Override
    public String getDropItemType() {
        return componentDto.getComponentType() == ComponentTypConst.WIDGET_TYPE ? ComponentConstant.dropListCompType : ComponentConstant.dropListGroupType;
    }

    @Override
    public String getApiName() {
        return componentDto.getWidget().getId();
    }

    @Override
    public String getType() {
        return "navigate";
    }

    @Override
    public int getLimit() {
        return componentDto.getWidget().getLimit();
    }

    @Override
    public JSONObject getProps() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("api_name", getApiName());
        jsonObject.put("menus", Lists.newArrayList());
        jsonObject.put("header", getName());
        if (componentDto.getWidget().getExtProp() != null){
            jsonObject.putAll(componentDto.getWidget().getExtProp());
        }

        return jsonObject;
    }

    @Override
    public int getGrayLimit() {
        return 1;
    }
}
