package com.facishare.webpage.customer.controller.model.arg.menu;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.TenantMenuSimpleDataVo;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/9.
 */
@Data
public class UpdateTenantMenuArg extends BaseArg {

    private String id;

    private String appId;

    private List<TenantMenuSimpleDataVo> tenantMenuSimpleItems;

    private Boolean isShowMenuIcon = true;

    private Boolean hiddenQuickCreate = false;


    @Override
    public void valid() throws WebPageException {
        if (CollectionUtils.isEmpty(tenantMenuSimpleItems)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
