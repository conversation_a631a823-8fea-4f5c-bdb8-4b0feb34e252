package com.facishare.webpage.customer.service.impl;

import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.qixin.i18n.model.Key;
import com.facishare.webpage.customer.api.model.MainChannelMenu;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.google.common.base.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Component("mainChannelMenuConvert")
public class MainChannelMenuConvert {

    @Resource
    private QixinI18nService qixinI18nService;

    private int defaultCrmEnterpriseId = 649688;

    public List<MainChannelMenu> convert2MainChannelMenu(int tenantId, Locale locale, List<Menu> menuList) {
        List<Key> keys = menuList.stream().map(menu -> {
            Key key = new Key();
            key.setKey(menu.getNameI18nKey());
            key.setDefaultVal(menu.getName());
            return key;
        }).collect(Collectors.toList());
        Map<String, String> i118ns = qixinI18nService.getMultiI18nValueDefault(tenantId, keys, locale);

        AtomicBoolean crmSelected = new AtomicBoolean(false);
        List<MainChannelMenu> mainChannelMenus= menuList.stream().map(menu -> {
            MainChannelMenu mainChannelMenu = new MainChannelMenu();
            String name = i118ns.get(menu.getNameI18nKey());
            mainChannelMenu.setName(Strings.isNullOrEmpty(name)?menu.getName():name);
            mainChannelMenu.setGroupIndex(menu.getGroupIndex());
            mainChannelMenu.setHighlightUrl(menu.getUrl().getHighlightUrl());
            mainChannelMenu.setUrl(menu.getUrl().getWebUrl());
            mainChannelMenu.setIcon(menu.getIcon().getIcon_1());
            mainChannelMenu.setId(menu.getId());
            if (menu.getId().equals("CRM")&&tenantId>defaultCrmEnterpriseId) {
                crmSelected.set(true);
                mainChannelMenu.setSelected(true);
            }
            return mainChannelMenu;
        }).collect(Collectors.toList());
        if (!crmSelected.get()) {
            mainChannelMenus.stream().forEach(mainChannelMenu -> {
                if (mainChannelMenu.getId().equals("WORK_CIRCLE")) {
                    mainChannelMenu.setSelected(true);
                }
            });
        }
        Optional<MainChannelMenu> optional = mainChannelMenus.stream().filter(mainChannelMenu -> mainChannelMenu.isSelected()).findFirst();
        if (!optional.isPresent() && mainChannelMenus.size() > 0) {
            mainChannelMenus.get(0).setSelected(true);
        }
        return mainChannelMenus;

    }
}
