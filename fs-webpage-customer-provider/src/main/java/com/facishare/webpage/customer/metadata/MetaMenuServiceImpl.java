package com.facishare.webpage.customer.metadata;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.CustomMenuType;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.constant.ApplyEnum;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.constant.Constant;
import com.facishare.webpage.customer.core.model.MenuCollectionType;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.helper.menuitem.CustomerMenuFactory;
import com.facishare.webpage.customer.helper.menuitem.CustomerMenuHelperService;
import com.facishare.webpage.customer.metadata.model.ConfigMenuData;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataArg;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataResult;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.facishare.webpage.customer.service.CustomerMenuService;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.utils.UiPaasParallelUtils.createParallelTask;

/**
 * Created by zhangyu on 2020/11/4
 */
@Component
@Slf4j
public class MetaMenuServiceImpl implements MetaMenuService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetaMenuServiceImpl.class);

    @Resource
    private MetaMenuFilterService metaMenuFilterService;
    @Resource
    private AppMenuConfig appMenuConfig;
    @Resource
    private LanguageService languageService;
    @Resource
    private CustomerMenuService customerMenuService;
    @Resource
    private ConfigMetaDataService configMetaDataService;

    private List<MetaDataService> metaDataServiceList;
    @Resource
    private CheckService checkService;

    @Autowired
    public void setMetaDataServiceList(List<MetaDataService> metaDataServiceList) {
        this.metaDataServiceList = metaDataServiceList;
    }

    @Override
    public List<MetaMenuData> getMetaMenuList(DataSourceEnv env, int tenantId, String appId, Locale locale, boolean previewNewCrmFlag) {
        String oldAppId = appId;
        if (WebPageUtils.checkPaaSApp(appId)) {
            appId = "Inner";
        }
        SlowLog slowLog = GlobalStopWatch.create("getMetaMenuList", 100L);
        String finalAppId = appId;
        List<MetaMenuData> menuDataList = Lists.newCopyOnWriteArrayList();
        ParallelUtils.ParallelTask parallelTask = createParallelTask(String.valueOf(tenantId));
        for (MetaDataService metaDataService : metaDataServiceList) {
            List<MetaMenuData> finalMenuDataList = menuDataList;
            parallelTask.submit(MonitorTaskWrapper.wrap(() -> {
                try {
                    QueryMetaDataArg arg = QueryMetaDataArg.builder().
                            tenantId(tenantId).
                            dataSourceEnv(env).
                            appId(finalAppId).
                            menuType(MenuCollectionType.ALL_TYPE).
                            oldAppId(oldAppId).
                            previewNewCrmFlag(previewNewCrmFlag).
                            locale(locale).build();
                    QueryMetaDataResult result = metaDataService.queryMetaData(arg);
                    slowLog.lap(metaDataService.getClass().getSimpleName() + "-" + "queryMetaData");
                    if (result != null && CollectionUtils.isNotEmpty(result.getMetaMenuDataList())) {
                        finalMenuDataList.addAll(result.getMetaMenuDataList());
                    }
                } catch (Exception e) {
                    LOGGER.error(metaDataService.getClass().getSimpleName() + "-tenantId:{},appId:{},locale:{}", tenantId, finalAppId, locale, e);
                }
            }));
        }

        try {
            parallelTask.await(DefaultTenantConfig.getFindMetaMenuMoreTimeByEi(String.valueOf(tenantId)), TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            RequestContextManager.initContextForIsIncomplete(true);
            log.error("getMetaMenuList error by env:{}, tenantId:{}, appId:{}", env, tenantId, appId, e);
        }
        slowLog.lap("queryAllMetaData");
        //根据灰度进行过滤
        menuDataList = filterMetaMenuByGray(tenantId, appId, menuDataList, previewNewCrmFlag);
        slowLog.lap("filterMetaMenuByGray");
        List<MetaMenuData> metaMenuDataList = sortMenuDataList(menuDataList, appId);
        slowLog.stop("over getMetaMenuList");
        return metaMenuDataList;
    }

    private List<MetaMenuData> sortMenuDataList(List<MetaMenuData> menuDataList, String appId) {
        List<String> orderApiNames = appMenuConfig.getSortedApiNamesByAppId(appId);
        if (CollectionUtils.isEmpty(orderApiNames)) {
            return menuDataList;
        } else {
            Map<String, MetaMenuData> menuItemMap = menuDataList.stream().collect(Collectors.toMap(MetaMenuData::getApiName, metaMenuData -> metaMenuData, (key1, key2) -> key2));
            List<MetaMenuData> notNeedSortMenus = Lists.newArrayList(menuDataList);
            notNeedSortMenus = notNeedSortMenus.stream().filter(x -> !orderApiNames.contains(x.getApiName())).collect(Collectors.toList());
            List<MetaMenuData> orderedApiNameList = Lists.newArrayList();
            orderApiNames.stream().forEach(x -> {
                if (menuItemMap.get(x) != null) {
                    orderedApiNameList.add(menuItemMap.get(x));
                }
            });
            orderedApiNameList.addAll(notNeedSortMenus);
            return orderedApiNameList;
        }
    }

    @Override
    public List<MetaMenuData> queryMetaMenuDataListByAppIds(DataSourceEnv env,
                                                            List<String> appIds,
                                                            int tenantId,
                                                            Locale locale, boolean previewNewCrmFlag) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Lists.newArrayList();
        }
        SlowLog slowLog = GlobalStopWatch.create("queryMetaMenuDataListByAppIds", 100L);
        List<MetaMenuData> metaMenuDataList = Lists.newCopyOnWriteArrayList();
        ParallelUtils.ParallelTask parallelTask = createParallelTask(String.valueOf(tenantId));
        for (String appId : appIds) {
            parallelTask.submit((MonitorTaskWrapper.wrap(() -> {
                List<MetaMenuData> metaMenuList = getMetaMenuList(env, tenantId, appId, locale, previewNewCrmFlag);
                metaMenuDataList.addAll(metaMenuList);
                slowLog.lap(appId + "_" + "getMetaMenuList");
            })));
        }
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            RequestContextManager.initContextForIsIncomplete(true);
            log.error("getMetaMenuList error by env:{}, appIds:{}, tenantId:{}, locale:{}", env, appIds, tenantId, locale, e);
        } finally {
            slowLog.stop("over queryMetaMenuDataListByAppIds");
        }
        return metaMenuDataList;
    }

    @Override
    public List<MetaMenuData> filterConfigMenus(DataSourceEnv env, int tenantId, String appId, List<Menu> menuList, Locale locale, boolean previewNewCrmFlag) {
        if (CollectionUtils.isEmpty(menuList)) {
            return Lists.newArrayList();
        }
        //TODO 如果是新版CRM应用，配置的菜单全部支持web端，此项会增加较多终端类型，耦合逻辑后续要考虑去掉
        if (WebPageConstants.APP_CRM.equals(appId)) {
            if (checkService.checkGoNewCRM(tenantId) || previewNewCrmFlag) {
                menuList.stream().forEach(x -> {
                    x.getDeviceTypes().add("WEB");
                    x.getDeviceTypes().add("Desktop_Mac");
                    x.getDeviceTypes().add("Desktop_Windows");
                    x.setDeviceTypes(x.getDeviceTypes());
                });
            }
        }
        if (!appMenuConfig.isTempleTenant(tenantId, appId)) {
            menuList = metaMenuFilterService.filterNoSupportMenu(env, tenantId, appId, menuList);
        }
        Map<String, String> noObjMenusLanguageMap = languageService.queryMenusLanguage(tenantId, menuList, locale);
        List<MetaMenuData> configMetaMenuDataList = buildConfigMenuData(appId, menuList, noObjMenusLanguageMap);
        return filterMetaMenuByGray(tenantId, appId, configMetaMenuDataList, previewNewCrmFlag);
    }

    @Override
    public List<MetaMenuData> getCustomerMenuDataList(DataSourceEnv env, int tenantId, String appId, String singleApp, Locale locale) {
        List<CustomerMenu> customerMenus = customerMenuService.queryCustomerByType(
                tenantId,
                Constant.WEB,
                env.isType() ? ApplyEnum.InnerApply.getApplyType() : ApplyEnum.CrossApply.getApplyType(),
                appId,
                singleApp, locale, false);
        if (CollectionUtils.isEmpty(customerMenus)) {
            return Lists.newArrayList();
        }
        if (!env.isType() && StringUtils.isNotBlank(appId)) {
            //crm对象和 自定义页面  需要根据appId过滤
            customerMenus = customerMenus.stream()
                    .filter(x -> x.getSupportClients().contains("web"))
                    .filter(

                            x -> (
                                    (
                                            (x.getMenuType() == CustomMenuType.CRM_MENU_TYPE || x.getMenuType() == CustomMenuType.PAGE_MENU_TYPE || x.getMenuType() == CustomMenuType.WECHATAVA || x.getMenuType() == CustomMenuType.DASHBOARD)
                                                    && appId.equals(x.getAppId())
                                    )
                                            || x.getMenuType() == CustomMenuType.H5PAGE
                            )
                    )
                    .collect(Collectors.toList());
        }
        return getCustomerMenuDataList(tenantId, appId, locale, customerMenus);
    }

    private List<MetaMenuData> getCustomerMenuDataList(int tenantId, String appId, Locale locale, List<CustomerMenu> customerMenus) {
        if (CollectionUtils.isEmpty(customerMenus)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<CustomerMenu>> customerMenuMap = customerMenus.stream().
                filter(Objects::nonNull).collect(Collectors.groupingBy(CustomerMenu::getMenuType));
        List<MetaMenuData> customerMenuMetaList = Lists.newCopyOnWriteArrayList();
        try {
            ParallelUtils.ParallelTask parallelTask = createParallelTask(String.valueOf(tenantId));
            customerMenuMap.forEach((x, y) -> {
                parallelTask.submit(MonitorTaskWrapper.wrap(() -> {
                    CustomerMenuHelperService customerMenuHelperService = CustomerMenuFactory.getCustomerMenuHelperService(x);
                    List<MetaMenuData> metaMenuDataList = customerMenuHelperService.covertMetaMenuDataList(tenantId, appId, locale, y);
                    customerMenuMetaList.addAll(metaMenuDataList);
                }));
            });
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("getCustomerMenuData error by tenantId : {}, appId : {}, customerMenus : {}", tenantId, appId, customerMenus, e);
        }
        return customerMenuMetaList;
    }

    private List<MetaMenuData> filterMetaMenuByGray(int tenantId, String appId, List<MetaMenuData> metaMenuDataList, boolean previewNewCrmFlag) {

        if (StringUtils.equals(appId, BizType.CRM.getValue()) && (checkService.checkGoNewCRM(tenantId) || previewNewCrmFlag)) {
            appId = com.facishare.webpage.customer.api.constant.Constant.paasCRM;
        }
        List<String> grayMenuApiNames = appMenuConfig.getGrayMenuApiNamesByAppId(appId);
        if (CollectionUtils.isNotEmpty(grayMenuApiNames)) {
            metaMenuDataList = metaMenuDataList.stream().filter(x ->
                            !grayMenuApiNames.contains(x.getApiName())
                                    || checkService.checkGrayBusinessSwitch(x.getApiName(), tenantId)
                                    || WebPageGraySwitch.isAllowForMenuWidget(x.getApiName(), tenantId, 0))
                    .collect(Collectors.toList());
        }
        return metaMenuDataList;
    }

    private List<MetaMenuData> buildConfigMenuData(String appId, List<Menu> menus, Map<String, String> noObjMenusLanguageMap) {
        if (CollectionUtils.isEmpty(menus)) {
            return Lists.newArrayList();
        }

        List<MetaMenuData> menuDataList = Lists.newArrayList();
        for (Menu menu : menus) {
            ConfigMenuData configMenuData = new ConfigMenuData();
            configMenuData.setMenu(menu);
            configMenuData.setName(StringUtils.isEmpty(noObjMenusLanguageMap.get(menu.getNameI18nKey())) ? menu.getName() : noObjMenusLanguageMap.get(menu.getNameI18nKey()));
            configMenuData.setAppId(appId);

            menuDataList.add(configMenuData);
        }
        return menuDataList;
    }

}
