package com.facishare.webpage.customer.controller.model.arg.menu;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.CommonlyUseMenuItem;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/19.
 */
@Data
public class CreateCommonlyUsedMenuArg extends BaseArg {

    @JSONField(name = "M1")
    @SerializedName("menuItems")
    private List<CommonlyUseMenuItem> commonlyUseMenuItems = Lists.newArrayList();

    @Override
    public void valid() throws Exception {

    }
}
