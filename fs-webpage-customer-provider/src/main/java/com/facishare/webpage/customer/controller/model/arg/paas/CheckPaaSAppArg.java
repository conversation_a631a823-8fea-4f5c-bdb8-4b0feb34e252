package com.facishare.webpage.customer.controller.model.arg.paas;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by zhangyu on 2020/12/21
 */
@Data
public class CheckPaaSAppArg extends BaseArg {

    private String appId;

    @Override
    public void valid() {
        if (StringUtils.isEmpty(appId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
