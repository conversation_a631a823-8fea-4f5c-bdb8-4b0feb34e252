package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetVendorPageTemplateListResult implements Serializable {

    private List<VendorWebPageTemplate> vendorWebPageTemplateList;

    private List<VendorAppPageTemplate> vendorAppPageTemplateList;


    @Data
    public static class VendorWebPageTemplate implements Serializable{
        private String templateId;
        private String layoutId;
        private int status;
    }

    @Data
    public static class VendorAppPageTemplate implements Serializable{
        private String templateId;
        private String appPageId;
        private int status;
    }


}
