package com.facishare.webpage.customer.service;


import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.controller.model.MenuTemplateDto;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.MenuItem;
import com.facishare.webpage.customer.model.MenuTemple;
import com.facishare.webpage.customer.model.MenuTempleAO;

import java.util.List;
import java.util.Locale;

/**
 * Created by z<PERSON>yi on 2019/9/16.
 */
public interface TenantMenuService {

    MenuTempleAO findTenantMenuById(DataSourceEnv env, String menuId, int enterpriseId, Locale locale, boolean previewNewCrmFlag);

    boolean checkIsSystem(TenantMenuEntity tenantMenuEntity);

    TenantMenuEntity changeSystemMenu(TenantMenuEntity tenantMenuEntity);

    MenuTemple insertOrUpdateTenantMenu(DataSourceEnv env, MenuTemplateDto menuTemplateDto, Locale locale);

    MenuTemple temp2Normal(DataSourceEnv env, String webMenuId, String templeId, int appType, Locale locale);

    List<MenuItem> filterNotSupportMenuData(List<MenuItem> menuItems, List<MetaMenuData> metaMenuDataList);

    List<MenuTemple> getMenuTempleList(int enterpriseId, String appId, List<MetaMenuData> metaMenuDataList, Locale locale);

    MenuTemple getMenuTempleListById(int enterpriseId, String appId, List<MetaMenuData> metaMenuDataList, Locale locale, String menuId);

    List<MenuTemple> getSimpleMenuTempleList(int enterpriseId, String appId, Locale locale);

    boolean disableMenu(String menuId);

    boolean enableMenu(String menuId);

    boolean deleteMenu(String menuId);

    boolean isHiddenSystemMenu(int enterpriseId);

    boolean setHiddenSystemMenu(int enterpriseId, boolean value);

    boolean validateMenuLimit(int enterpriseId, String appId);

    boolean validateMenuName(int enterpriseId, String appId, String name);

    MenuTemple findTenantSystemMenuById(String menuId, int enterpriseId, String appId, Locale locale);

    List<TenantMenuEntity> getTenantMenuList(int tenantId, String appId, boolean onlySystem);

    boolean updateTenantMenuEntity(TenantMenuEntity systemMenu);

    List<TenantMenuEntity> copyTenantMenu(int fromTenantId, int toTenantId, int appType);

    int destroyTenantMenu(int tenantId);

    void deleteTenantMenusByAppId(int tenantId, int appType, String appId);

    List<JSONObject> getTenantUiConfig(String client);
}
