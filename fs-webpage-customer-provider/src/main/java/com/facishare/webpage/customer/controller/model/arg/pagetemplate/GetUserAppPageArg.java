package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/19.
 */
@Data
public class GetUserAppPageArg extends BaseArg {

    private String appId;

    @Override
    public void valid() throws WebPageException {
        if (StringUtils.isEmpty(appId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
