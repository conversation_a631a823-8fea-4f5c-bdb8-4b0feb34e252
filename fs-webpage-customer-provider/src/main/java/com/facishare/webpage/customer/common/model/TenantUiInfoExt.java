package com.facishare.webpage.customer.common.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
public class TenantUiInfoExt {
    private JSONObject map;

    private static final String NAME_I18N_KEY = "nameI18nKey";
    private static final String TITLE = "title";
    private static final String CLIENT = "client";

    public static TenantUiInfoExt of(JSONObject jsonObject) {
        return new TenantUiInfoExt(jsonObject);
    }

    public JSONObject getMap() {
        return map;
    }

    public String getTitle() {
        return map.getString(TITLE);
    }

    public String getNameI18nKey() {
        return map.getString(NAME_I18N_KEY);
    }

    public List<String> getSupportClient() {
        return map.getJSONArray(CLIENT).toJavaList(String.class);
    }

    public void setTitle(String title) {
        map.put(TITLE, title);
    }
}
