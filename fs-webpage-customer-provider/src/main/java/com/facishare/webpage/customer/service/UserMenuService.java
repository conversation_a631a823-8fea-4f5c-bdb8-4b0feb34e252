package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.MenuItem;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/16.
 */
public interface UserMenuService {

    List<MenuItem> insertOrUpdateUserMenu(String menuId, List<MenuItem> menuItems, int enterpriseId, int employeeId);

    boolean delete(String menuId, int enterpriseId, int employeeId);

    boolean hasUserMenu(String menuId, int enterpriseId, int employeeId);

    List<String> queryCommonlyUserMenuItem(int enterpriseId, int employeeId);

    String getUserDefaultMenu(int enterpriseId, int employeeId);

    List<String> insertOrUpdateCommonlyUsedMenu(int enterpriseId, int employeeId, List<String> menuApiNames);

    boolean setUserDefaultMenu(String menuId, int enterpriseId, int employeeId);

    Map<String, List<MenuItem>> getUserMenus(int enterpriseId, int employeeId, List<MetaMenuData> metaMenuDataList);

    /**
     * query user menu by userMenuId
     *
     * @param env
     * @param enterpriseId
     * @param employeeId
     * @param menuId
     * @param metaMenuDataList
     * @param locale
     * @return
     */
    List<MenuItem> queryUserMenuById(DataSourceEnv env,
                                     int enterpriseId,
                                     int employeeId,
                                     String menuId,
                                     List<MetaMenuData> metaMenuDataList,
                                     Locale locale);

}
