package com.facishare.webpage.customer.helper;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2022/3/16 15:35
 */
public class MenuComponentHelper {

    private JSONObject menuComponent;

    public MenuComponentHelper(JSONObject menuComponent) {
        this.menuComponent = menuComponent;
    }

    public String getApiName() {
        return this.menuComponent.getString("api_name");
    }

    public String getMenuType() {
        return this.menuComponent.getString("type");
    }

    public String getAppId() {
        JSONObject props = this.menuComponent.getJSONObject("props");
        return props.getString("appId");
    }

    public String getHeader() {
        JSONObject props = this.menuComponent.getJSONObject("props");
        return props.getString("header");
    }

}
