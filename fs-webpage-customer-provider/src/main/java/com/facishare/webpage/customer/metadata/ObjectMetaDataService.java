package com.facishare.webpage.customer.metadata;

import com.facishare.converter.EIEAConverter;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.core.SimpObjectDescription;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.core.config.ObjectConfig;
import com.facishare.webpage.customer.core.model.MenuCollectionType;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.metadata.model.ObjectMenuData;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataArg;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataResult;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.fxiaoke.enterpriserelation2.arg.ListAppAssociationObjectsArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.result.AppAssociationObjectResult;
import com.fxiaoke.enterpriserelation2.service.AppDataRoleService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 */
@Slf4j
@Service("objectMetaDataService")
public class ObjectMetaDataService implements MetaDataService {

    @Resource
    private ObjectConfig objectConfig;
    @Resource
    private ObjectService objectService;
    @Resource
    private AppMenuConfig appMenuConfig;
    @Resource
    private MetaMenuFilterService metaMenuFilterService;
    @Resource
    private CheckService checkService;

    @Autowired
    private AppDataRoleService appDataRoleService;

    @Resource
    private EIEAConverter eieaConverter;

    @Override
    public QueryMetaDataResult queryMetaData(QueryMetaDataArg arg) {
        int tenantId = arg.getTenantId();
        String appId = arg.getAppId();
        DataSourceEnv dataSourceEnv = arg.getDataSourceEnv();
        SlowLog slowLog = GlobalStopWatch.create("queryObjectMetaDataService", 100L);
        List<SimpObjectDescription> simpObjectDescriptions = getObjectMenus(tenantId, appId, arg.getLocale());
        slowLog.lap("getObjectMenus");
        List<MetaMenuData> metaMenuDataList = getMetaMenuDataList(arg, tenantId, appId, dataSourceEnv, slowLog, simpObjectDescriptions, Constant.LINK_APP_TYPE_PRE, arg.isPreviewNewCrmFlag());
        return QueryMetaDataResult.builder().metaMenuDataList(metaMenuDataList).build();
    }

    private List<MetaMenuData> getMetaMenuDataList(QueryMetaDataArg arg, int tenantId, String appId, DataSourceEnv dataSourceEnv,
                                                   SlowLog slowLog, List<SimpObjectDescription> simpleObjectDescriptions, Integer linkType, boolean previewNewCrmFlag) {
        switch (arg.getMenuType()) {
            case MenuCollectionType.ALL_TYPE:
                break;
            case MenuCollectionType.PRE_OBJ_TYPE:
                simpleObjectDescriptions = simpleObjectDescriptions.stream().filter(simpObjectDescription -> !simpObjectDescription.getApiName().endsWith("__c")).collect(Collectors.toList());
                log.debug("find simpleObjectDescriptions:{}, arg:{}", simpleObjectDescriptions, arg);
                break;
            case MenuCollectionType.UD_OBJ_TYPE:
                simpleObjectDescriptions = simpleObjectDescriptions.stream().filter(simpObjectDescription -> simpObjectDescription.getApiName().endsWith("__c")).collect(Collectors.toList());
                break;
        }
        slowLog.lap("filterSimpleObjectDescriptionsByMenuType");
        if (!appMenuConfig.isTempleTenant(arg.getTenantId(), arg.getAppId())) {
            simpleObjectDescriptions = metaMenuFilterService.filterNoSupportCrmMenu(dataSourceEnv, tenantId, appId, simpleObjectDescriptions, linkType);
            slowLog.lap("filterSimpleObjectDescriptionsByIsTempleTenant");
        }
        slowLog.lap("filterNoSupportCrmMenu");
        List<MetaMenuData> metaMenuDataList = buildObjectMenuData(dataSourceEnv, tenantId, arg.getOldAppId(), simpleObjectDescriptions);
        slowLog.lap("buildObjectMenuData");
        metaMenuDataList = filterMetaMenuByGray(tenantId, appId, metaMenuDataList, previewNewCrmFlag);
        slowLog.lap("filterMetaMenuByGray");

        slowLog.stop("over queryObjectMetaDataService");
        return metaMenuDataList;
    }

    private List<SimpObjectDescription> getObjectMenus(int tenantId, String appId, Locale locale) {
        List<SimpObjectDescription> objectDescribeList = objectService.getAllDescribe(tenantId, locale);
        if (CollectionUtils.isEmpty(objectDescribeList)) {
            return Lists.newArrayList();
        }
        if (Constant.APP_CRM.equals(appId) || Constant.paasCRM.equals(appId)) {
            List<String> apiNames = objectConfig.showApiNames();
            objectDescribeList = objectDescribeList.stream()
                    .filter(objectDescribe -> apiNames.contains(objectDescribe.getApiName())
                            || objectDescribe.getObjectType().equals(MenuType.UD_OBJ)).collect(Collectors.toList());
        }
        return objectDescribeList;
    }

    private List<MetaMenuData> buildObjectMenuData(DataSourceEnv env, int tenantId, String appId, List<SimpObjectDescription> objectDescribeList) {
        if (CollectionUtils.isEmpty(objectDescribeList)) {
            return Lists.newArrayList();
        }

        List<MetaMenuData> menuDataList = Lists.newArrayList();
        for (SimpObjectDescription objectDescribe : objectDescribeList) {
            Url grayUrl = null;
            String apiName = objectDescribe.getApiName();
            if (isPreObject(objectDescribe)) {
                Url grayUrlByConfig = objectConfig.getGrayUrlByApiName(apiName);
                if (Objects.nonNull(grayUrlByConfig) && WebPageGraySwitch.isAllowUrlByBusiness(apiName, tenantId)) {
                    grayUrl = grayUrlByConfig;
                }
            }
            //TODO
            objectDescribe.setIconIndex(objectConfig.getIconIndexByApiName(apiName, objectDescribe.getIconIndex()));
            ObjectMenuData objectMenuData = new ObjectMenuData();
            objectMenuData.setSimpObjectDescription(objectDescribe);
            objectMenuData.setHidden(objectConfig.isHiddenByApiName(apiName));
            objectMenuData.setGrayUrl(grayUrl);
            objectMenuData.setShowDeviceTypes(objectConfig.getShowDeviceTypes(apiName));
            objectMenuData.setAppId(appId);

            menuDataList.add(objectMenuData);
        }
        return menuDataList;
    }

    private boolean isPreObject(SimpObjectDescription objectDescribe) {
        if (Objects.isNull(objectDescribe) || Strings.isNullOrEmpty(objectDescribe.getApiName())) {
            return false;
        }
        return !objectDescribe.getApiName().endsWith("__c");
    }


    private List<MetaMenuData> filterMetaMenuByGray(int tenantId, String appId, List<MetaMenuData> metaMenuDataList, boolean previewNewCrmFlag) {

        if (StringUtils.equals(appId, BizType.CRM.getValue()) && (checkService.checkGoNewCRM(tenantId) || previewNewCrmFlag)) {
            appId = Constant.paasCRM;
        }
        List<String> grayMenuApiNames = appMenuConfig.getGrayMenuApiNamesByAppId(appId);
        if (CollectionUtils.isNotEmpty(grayMenuApiNames)) {
            metaMenuDataList = metaMenuDataList.stream().filter(x ->
                            !grayMenuApiNames.contains(x.getApiName())
                                    || checkService.checkGrayBusinessSwitch(x.getApiName(), tenantId)
                                    || WebPageGraySwitch.isAllowForMenuWidget(x.getApiName(), tenantId, 0))
                    .collect(Collectors.toList());
        }
        return metaMenuDataList;
    }

    public List<MetaMenuData> getCustomerLinkAppObjects(DataSourceEnv env, int tenantId, String appId, String type, Locale locale, String customerLinkAppId) {
        //查询自定义互联应用绑定的对象列表
        ListAppAssociationObjectsArg arg = new ListAppAssociationObjectsArg();
        arg.setAppId(customerLinkAppId);
        //arg.setLinkType(Constant.LINK_APP_TYPE_CUSTOMER);
        arg.setLinkType(Constant.LINK_APP_TYPE_PRE);
        arg.setUpstreamEa(eieaConverter.enterpriseIdToAccount(tenantId));
        HeaderObj headerObj = HeaderObj.newInstance(tenantId);
        List<AppAssociationObjectResult> objectList = appDataRoleService.listAppAssociationObjects(headerObj, arg).getData();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(objectList)) {
            return Lists.newArrayList();
        }
        List<String> apiNammes = objectList.stream().map(x -> x.getObjectApiName()).collect(Collectors.toList());
        SlowLog slowLog = GlobalStopWatch.create("getCustomerLinkAppObjects", 100L);
        List<SimpObjectDescription> simpObjectDescriptions = getObjectMenus(tenantId, appId, locale);
        simpObjectDescriptions = simpObjectDescriptions.stream().filter(x -> apiNammes.contains(x.getApiName())).collect(Collectors.toList());
        QueryMetaDataArg queryMetaDataArg = QueryMetaDataArg.builder().
                tenantId(tenantId).
                dataSourceEnv(env).
                appId(customerLinkAppId).
                oldAppId(customerLinkAppId).
                menuType(MenuCollectionType.ALL_TYPE).
                locale(locale).build();
        List<MetaMenuData> metaMenuDataList = getMetaMenuDataList(queryMetaDataArg, tenantId, customerLinkAppId, env, slowLog, simpObjectDescriptions, Constant.LINK_APP_TYPE_PRE, false);
        return metaMenuDataList;
    }
}
