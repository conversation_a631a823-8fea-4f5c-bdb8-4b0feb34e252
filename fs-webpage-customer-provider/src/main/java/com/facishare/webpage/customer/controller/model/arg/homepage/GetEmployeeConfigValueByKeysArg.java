package com.facishare.webpage.customer.controller.model.arg.homepage;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
public class GetEmployeeConfigValueByKeysArg implements Serializable {
    @SerializedName("Type")
    private int type;
    @SerializedName("DataID")
    private String layoutId;
    @SerializedName("Keys")
    private List<Integer> keys;

}
