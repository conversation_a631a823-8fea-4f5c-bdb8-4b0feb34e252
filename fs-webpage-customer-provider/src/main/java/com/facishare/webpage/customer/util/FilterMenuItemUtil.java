package com.facishare.webpage.customer.util;

import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.config.ObjectConfig;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.MenuItem;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/6/3
 */
@Component
@Slf4j
public class FilterMenuItemUtil {

    @Resource
    private ObjectConfig objectConfig;


    public List<MenuItem> filterMenuItemsByTenantMenus(List<MenuItem> menuItems, boolean needFilterHidden) {

        if (CollectionUtils.isNotEmpty(menuItems)) {
            menuItems = menuItems.stream().filter(x -> !needFilterHidden || !isHidden(x)).collect(Collectors.toList());
        }
        return menuItems;
    }


    public List<MenuItem> filterMenuItemsByFunAccess(List<MenuItem> menuItems,
                                                     Map<String, Map<String, List<String>>> funcAccess,
                                                     List<MetaMenuData> metaMenuDataList) {
        if (CollectionUtils.isEmpty(menuItems)) {
            return menuItems;
        }

        Map<String, MetaMenuData> metaMenuDataMap = MenuUtil.getMetaMenuDataMap(metaMenuDataList);
        menuItems = menuItems.stream().map(x -> {
            if (x.getMetaMenuData() == null) {
                return x;
            }
            MetaMenuData metaMenuData = metaMenuDataMap.get(MenuUtil.getUniqueKey(x.getMetaMenuData().getAppId(), x.getApiName()));
            if (metaMenuData == null) {
                return x;
            }
            Map<String, List<String>> funcAccessByAppId = funcAccess.get(StringUtils.isEmpty(x.getMetaMenuData().getAppId())
                    ? WebPageConstants.APP_CRM
                    : x.getMetaMenuData().getAppId());
            if (metaMenuData.getFunctions() == null || CollectionUtils.isNotEmpty(metaMenuData.getFunctions())) {
                x.setPrivilegeAction((List<String>) MapUtils.getObject(funcAccessByAppId, metaMenuData.getApiName(), Lists.newArrayList()));
            } else {
                x.setPrivilegeAction(Lists.newArrayList(WebPageConstants.FUNC_CODE_LIST));
            }
            return x;
        }).collect(Collectors.toList());
        return menuItems.stream().filter(x ->
                        x.getMenuItemType().equals(MenuType.GROUP) || CollectionUtils.isNotEmpty(x.getPrivilegeAction()))
                .collect(Collectors.toList());
    }

    public static boolean hasPermissionByMenu(String apiName, Map<String, List<String>> funcAccess, Map<String, MetaMenuData> metaMenuDataMap) {
        MetaMenuData metaMenuData = metaMenuDataMap.get(apiName);

        if (metaMenuData == null) {
            return false;
        }
        if ((metaMenuData.getFunctions() == null || CollectionUtils.isNotEmpty(metaMenuData.getFunctions())) && funcAccess.get(apiName) == null) {
            return false;
        }
        return true;
    }

    public List<MenuItem> filterUserNotSupportMenuData(List<MenuItem> menuItems, List<MetaMenuData> metaMenuDataList) {

        Map<String, MetaMenuData> metaMenuDataMap = MenuUtil.getMetaMenuDataMap(metaMenuDataList);
        Set<String> allApiNames = metaMenuDataMap.keySet();

        List<MenuItem> menuItemList = Lists.newArrayList();
        for (MenuItem menuItem : menuItems) {
            if (!StringUtils.equals(MenuType.GROUP, menuItem.getMenuItemType()) &&
                    !allApiNames.contains(MenuUtil.getUniqueKey(menuItem.getMetaMenuData().getAppId(), menuItem.getApiName()))) {
                continue;
            }
            if (menuItem.getMetaMenuData() == null) {
                menuItemList.add(menuItem);
                continue;
            }
            MetaMenuData metaMenuData = metaMenuDataMap.get(MenuUtil.getUniqueKey(menuItem.getMetaMenuData().getAppId(), menuItem.getApiName()));

            if (isDisableObject(menuItem, metaMenuData)) {
                continue;
            }
            menuItemList.add(menuItem);
        }
        return menuItemList;
    }

    private boolean isDisableObject(MenuItem menuItem, MetaMenuData metaMenuData) {

        if (menuItem == null) {
            return true;
        }

        if (MenuType.GROUP.equals(menuItem.getMenuItemType())) {
            return false;
        }

        if (metaMenuData == null || !metaMenuData.isActive()) {
            return true;
        }

        return false;
    }

    public List<MenuItem> filterMenuItemsFunAccess(List<MenuItem> menuItems, ClientInfo clientInfo) {
        if (CollectionUtils.isNotEmpty(menuItems)) {
            menuItems = menuItems.stream().map(x -> changePrivilegeActionByDevice(x, clientInfo)).collect(Collectors.toList());
        }
        return menuItems;
    }

    public List<MenuItem> filterCustomerMenuItems(List<MenuItem> menuItems, List<Scope> scopeList) {
        if (CollectionUtils.isEmpty(scopeList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(menuItems)) {
            return Lists.newArrayList();
        }
        return menuItems.stream().filter(x -> {
            if (!MenuType.CUSTOMER_MENU.equals(x.getMenuItemType()) || x.getMetaMenuData() == null) {
                return true;
            }
            List<Scope> menuDataScopeList = x.getMetaMenuData().getScopeList();
            if (CollectionUtils.isEmpty(menuDataScopeList)) {
                log.warn("filterCustomerMenuItems warn! menuDataScopeList is null,menuInfo:{}", JSON.toJSONString(x));
                return false;
            }
            return CollectionUtils.containsAny(menuDataScopeList, scopeList);
        }).collect(Collectors.toList());
    }

    private MenuItem changePrivilegeActionByDevice(MenuItem menuItem, ClientInfo clientInfo) {
        if (CollectionUtils.isEmpty(menuItem.getPrivilegeAction()) || !menuItem.getPrivilegeAction().contains(WebPageConstants.FUNC_CODE_ADD)) {
            return menuItem;
        }

        List<String> privilegeAction = menuItem.getPrivilegeAction();
        if (objectConfig.hiddenAddWithDeviceType(menuItem.getApiName(), clientInfo.getType().getValue())) {
            privilegeAction.remove(WebPageConstants.FUNC_CODE_ADD);
        }
        MetaMenuData metaMenuData = menuItem.getMetaMenuData();
        if (metaMenuData != null && metaMenuData.checkDetailObjectButton()) {
            privilegeAction.remove(WebPageConstants.FUNC_CODE_ADD);
        }
        menuItem.setPrivilegeAction(privilegeAction);
        return menuItem;
    }

    private boolean isHidden(MenuItem menuItem) {
        if (menuItem.getMenuData() != null && menuItem.getMenuData().getIsHidden()) {
            return true;
        }
        return false;
    }

}
