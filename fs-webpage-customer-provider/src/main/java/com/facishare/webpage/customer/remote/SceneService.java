package com.facishare.webpage.customer.remote;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.common.resource.model.arg.BatchGetSceneArg;
import com.facishare.webpage.customer.common.resource.model.result.BatchGetSceneResult;

import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2020/9/11
 */
public interface SceneService {

    /**
     * 获取场景多语
     *
     * @param tenantId
     * @param sceneArgList
     * @param locale
     * @return
     */
    List<BatchGetSceneResult.SceneResult> batchGetSceneName(int tenantId,
                                                            List<BatchGetSceneArg.SceneArg> sceneArgList,
                                                            Locale locale);

    /**
     * 获取互联场景数据
     *
     * @param tenantId
     * @param objectApiName
     * @param locale
     * @return
     */
    JSONObject findOuterTenantSceneList(int tenantId,
                                              String objectApiName,
                                              Locale locale);

}
