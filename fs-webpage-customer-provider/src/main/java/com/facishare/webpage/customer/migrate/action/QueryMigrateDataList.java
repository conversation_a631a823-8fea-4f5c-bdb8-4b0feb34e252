package com.facishare.webpage.customer.migrate.action;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/22 5:33 PM
 */
public interface QueryMigrateDataList {

    @Data
    @Builder
    class Arg implements Serializable {

    }

    @Data
    @Builder
    class Result implements Serializable {
        private List<AppMigrateData> appMigrateDataList;
        private List<WebMigrateData> webMigrateDataList;
    }

    @Data
    @Builder
    class AppMigrateData implements Serializable {
        private String viewName;
        private String pageName;
        private String pageId;
        private String scopeNames;
        private int priorityLevel;
        private int status;
    }

    @Data
    @Builder
    class WebMigrateData implements Serializable {
        public String viewName;
        public String menuName;
        public String menuId;
        public String pageName;
        public String pageId;
        public boolean mergeToMenu;
        public String scopeNames;
        public int priorityLevel;
        public int status;
    }

}
