package com.facishare.webpage.customer.util.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.HomePageLayoutFilter;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.api.constant.CustomerLayoutField;
import com.facishare.webpage.customer.core.component.ComponentCovert;
import com.facishare.webpage.customer.util.ComponentUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/9/7
 */
@Setter
public class SceneComponentCovert extends ComponentCovert {

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    private JSONObject sceneJSONObject;

    private Map<String, String> filterMainIDMap;

    @Override
    public String getApiName() {
        return sceneJSONObject.getString(CustomerLayoutField.apiName);
    }

    @Override
    public String getType() {
        return sceneJSONObject.getString(CustomerLayoutField.type);
    }

    @Override
    public int getLimit() {
        return sceneJSONObject.getInteger(ComponentConstant.LIMIT) == null ? 1 : sceneJSONObject.getInteger(ComponentConstant.LIMIT);
    }

    @Override
    public JSONObject getProps() {

        JSONObject jsonObject = (JSONObject) sceneJSONObject.clone();

        jsonObject.remove(ComponentConstant.LIMIT);
        jsonObject.put(CustomerLayoutField.type, sceneJSONObject.getString(CustomerLayoutField.propsType));


        List<HomePageLayoutFilter> homePageLayoutFilterList = ComponentUtil.buildHomePageLayoutFilters(jsonObject);
        if (CollectionUtils.isEmpty(homePageLayoutFilterList)) {
            return jsonObject;
        }

        List<JSONObject> newSceneCards = homePageLayoutFilterList.stream().map(homePageLayoutFilter -> {
            homePageLayoutFilter.setFilterMainID(filterMainIDMap.getOrDefault(homePageLayoutFilter.getFilterKey(), homePageLayoutFilter.getFilterKey()));
            String toJson = gson.toJson(homePageLayoutFilter);
            JSONObject jsonObject1 = JSONObject.parseObject(toJson, JSONObject.class);
            return jsonObject1;
        }).collect(Collectors.toList());
        jsonObject.put("filters", newSceneCards);

        return jsonObject;
    }
}
