package com.facishare.webpage.customer.controller.model.result.homepage;

import com.facishare.webpage.customer.api.model.HomePageComponent;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/9/11
 */
@Data
public class GetHomePageComponentsResult implements Serializable {
    @SerializedName("ComponentGroups")
    private List<HomePageComponent> components;
    @SerializedName("chartMaxCount")
    private int chartMaxCount = 10;
    @SerializedName("otherMaxCount")
    private int otherMaxCount = 10;
}
