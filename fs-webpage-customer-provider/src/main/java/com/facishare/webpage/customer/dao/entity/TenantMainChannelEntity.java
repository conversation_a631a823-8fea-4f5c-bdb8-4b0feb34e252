package com.facishare.webpage.customer.dao.entity;

import com.facishare.webpage.customer.constant.WebPageConstants;
import com.google.common.collect.Lists;
import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
@Entity(value = "TenantMainChannelEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("tenantId"), @Field("apiName")}
                , options = @IndexOptions(unique = true, dropDups = true, background = true))})
public class TenantMainChannelEntity {

    @Id
    private ObjectId id;
    @Property("tenantId")
    private int tenantId;
    @Property("apiName")
    private String apiName;
    @Property("name")
    private String name;
    @Property("scopeList")
    private List<String> scopeList = Lists.newArrayList("D-999999");
    @Embedded("webMainChannelMenuEntityList")
    private List<WebMainChannelMenuEntity> webMainChannelMenuEntityList;
    @Property("priorityLevel")
    private Integer priorityLevel = WebPageConstants.MAIN_CHANNEL_DEFAULT_PRIORITYLEVEL;
    @Property("canCustom")
    private Boolean canCustom = true;
    @Property("showAppName")
    private Boolean showAppName = true;
    @Property("showMoreAppEntry")
    private Boolean showMoreAppEntry;
    @Property("sourceType")
    private String sourceType;
    @Property("version")
    private Integer version = WebPageConstants.MAIN_CHANNEL_DEFAULT_VERSION;
    @Property("creatorId")
    private int creatorId;
    @Property("createTime")
    private long createTime;
    @Property("updaterId")
    private int updaterId;
    @Property("updateTime")
    private long updateTime;

}
