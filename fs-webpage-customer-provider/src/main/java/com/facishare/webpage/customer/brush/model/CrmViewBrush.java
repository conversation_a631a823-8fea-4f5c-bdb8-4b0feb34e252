package com.facishare.webpage.customer.brush.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CrmViewBrush {

    @Data
    class Arg implements Serializable {
        private Integer status;
        private Boolean physicalDelete;
        private String appId;
        private String componentId;

    }

    @Data
    @Builder
    class Result implements Serializable {
        private List<Integer> tenantIds;
        private List<Integer> failedTenantIds;
        private Integer totalNum;
        private Integer successNum;
    }
}
