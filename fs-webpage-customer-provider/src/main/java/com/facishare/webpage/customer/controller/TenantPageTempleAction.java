package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.*;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.*;


/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/9.
 */
public interface TenantPageTempleAction {

    GetWebPageTemplesResult getWebPageTemples(UserInfo userInfo,
                                              GetWebPageTemplesArg arg);

    GetAppPageTemplesResult getAppPageTemples(UserInfo userInfo,
                                              GetAppPageTemplesArg arg);

    GetScopesResult getScopes(UserInfo userInfo,
                              GetScopesArg arg);

    GetPageTemplateByIdResult getPageTemplateById(UserInfo userInfo,
                                                  ClientInfo clientInfo,
                                                  GetPageTemplateByIdArg arg);

    SaveWebPageTempleResult saveWebPageTemple(UserInfo userInfo,
                                              ClientInfo clientInfo,
                                              SaveWebPageTempleArg arg);

    SaveAppPageResult saveAppPageTemple(UserInfo userInfo,
                                        ClientInfo clientInfo,
                                        SaveAppPageTempleArg arg);

    UpdatePageTempleStatusResult updatePageTempleStatus(UserInfo userInfo,
                                                        UpdatePageTempleStatusArg arg);

    SynPageTemplateResult deleteSynFromWebTemplate(UserInfo userInfo,
                                                   SynPageTemplateArg arg);

    SynPageTemplateResult createSynFromWebTemplate(UserInfo userInfo,
                                                          ClientInfo clientInfo,
                                                   SynPageTemplateArg arg);
    @Deprecated
    SaveVendorWebPageTempleResult saveVendorWebPageTemplate(UserInfo userInfo,
                                                            ClientInfo clientInfo,
                                                            SaveVendorWebPageTempleArg arg);

    @Deprecated
    SaveVendorAppPageTemplateResult saveVendorAppPageTemplate(UserInfo userInfo,
                                                              ClientInfo clientInfo,
                                                              SaveVendorAppPageTemplateArg arg);

    @Deprecated
    GetVendorPageTemplateByIdResult getVendorTemplateById(UserInfo userInfo,
                                                          GetVendorPageTemplateByIdArg arg);

    @Deprecated
    UpdateVendorTemplateStatusResult updateVendorTemplateStatus(UserInfo userInfo,
                                                                UpdateVendorTemplateStatusArg arg);

    @Deprecated
    GetVendorPageTemplateListResult getVendorPageTemplateList(UserInfo userInfo);

    GetAppDataResult getAppData(UserInfo userInfo,
                                ClientInfo clientInfo,
                                GetAppDataArg arg);

    CheckGoNewCrmResult checkGoNewCrm(UserInfo userInfo);


    GetAllPageTemplatesResult getAllPageTemplates(UserInfo userInfo, ClientInfo clientInfo, GetAllPageTemplatesArg arg);
}
