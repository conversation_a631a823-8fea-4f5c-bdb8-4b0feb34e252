package com.facishare.webpage.customer.metadata.model;

import com.facishare.qixin.permission.unified.util.MenuPermissionParam;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/25
 */
@Data
public class MetaDataCovertPermissionParam extends MenuPermissionParam {

    private MetaMenuData metaMenuData;

    @Override
    public String getApiName() {
        return metaMenuData.getApiName();
    }

    @Override
    public String getObjectApiName() {
        return metaMenuData.getObjectApiName();
    }

    @Override
    public String getObjectRecodeTypeApiName() {
        return metaMenuData.getObjectRecordTypeApiName();
    }

    @Override
    public List<String> getStandardFunctionCodes() {
        if (CollectionUtils.isNotEmpty(getFunctions()) && (getFunctions().contains("List") || getFunctions().contains("Add"))){
            return getFunctions();
        }
        return null;
    }

    @Override
    public List<String> getNonStandardFunctionCodes() {
        if (CollectionUtils.isNotEmpty(getFunctions()) && (!getFunctions().contains("List") && !getFunctions().contains("Add"))){
            return getFunctions();
        }
        return null;
    }

    @Override
    public List<String> getRoleCodes() {
        return metaMenuData.getRoleCodes();
    }

    @Override
    public List<String> getDeviceTypes() {
        return metaMenuData.getDeviceTypes();
    }

    private List<String> getFunctions(){
        return metaMenuData.getFunctions();
    }

    //TODO
    @Override
    public List<String> getPersonFunctionCodes() {
        return null;
    }
}
