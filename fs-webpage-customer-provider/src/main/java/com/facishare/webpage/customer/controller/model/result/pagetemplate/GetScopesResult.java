package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.api.model.RoleInfo;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/19.
 */
@Data
public class GetScopesResult extends BaseResult {
    private boolean hasOuterRole;

    private List<RoleInfo> outerRoleInfoList;
}
