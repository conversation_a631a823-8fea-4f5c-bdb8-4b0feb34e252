package com.facishare.webpage.customer.dao.entity;

import com.facishare.qixin.datastore.BaseEntity;
import com.facishare.webpage.customer.dao.property.UserPageTemplateEntityProperty;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.*;

/**
 * <AUTHOR>
 * @date 2022/3/3 2:36 PM
 */
@Entity(value = "UserPageTemplateEntity", noClassnameStored = true)
@Indexes({
        @Index(fields = {@Field("EId"), @Field("EPId"), @Field("AId"), @Field("T"), @Field("PTId")},
                options = @IndexOptions(name = "EId_1_EPId_1_AId_1_T_1_PTId_1", background = true))
})
@Data
@ToString(callSuper = true)
public class UserPageTemplateEntity extends BaseEntity {
    /**
     * 模板id
     */
    @Property(UserPageTemplateEntityProperty.PAGE_TEMPLATE_ID)
    private String pageTemplateId;
    /**
     * 员工id
     */
    @Property(UserPageTemplateEntityProperty.EMPLOYEE_ID)
    private int employeeId;
    /**
     * 应用id
     */
    @Property(UserPageTemplateEntityProperty.APP_ID)
    private String appId;
    /**
     * 模板所在端类型
     */
    @Property(UserPageTemplateEntityProperty.TYPE)
    private String type;
    /**
     * 模板名称
     */
    @Property(UserPageTemplateEntityProperty.NAME)
    private String name;
    /**
     * 模板描述
     */
    @Property(UserPageTemplateEntityProperty.DESCRIPTION)
    private String description;
    /**
     * 菜单id
     */
    @Property(UserPageTemplateEntityProperty.WEB_MENU_ID)
    private String webMenuId;
    /**
     * 首页id
     */
    @Property(UserPageTemplateEntityProperty.WEB_PAGE_ID)
    private String webPageId;
    /**
     * app自定义页面id
     */
    @Property(UserPageTemplateEntityProperty.APP_PAGE_ID)
    private String appPageId;
    /**
     * 模板优先级
     */
    @Property(UserPageTemplateEntityProperty.PRIORITY_LEVEL)
    private int priorityLevel;
    /**
     * 创建时间
     */
    @Property(UserPageTemplateEntityProperty.CREATE_TIME)
    private long createTime;
    /**
     * 更新时间
     */
    @Property(UserPageTemplateEntityProperty.UPDATE_TIME)
    private Long updateTime;

    /**
     * 模板状态
     */
    @Property(UserPageTemplateEntityProperty.STATUS)
    private Integer status;

    @Property(UserPageTemplateEntityProperty.CREATE_ID)
    private int creatorId;

    @Property(UserPageTemplateEntityProperty.UPDATE_ID)
    private int updatorId;

    @Property(UserPageTemplateEntityProperty.FROM_WEB_PAGE_TEMPLATE_ID)
    public String fromWebPageTemplateId;

    @Property(UserPageTemplateEntityProperty.HAS_BEEN_SYN_TO_APP)
    public Boolean hasBeenSynToApp = false;
}
