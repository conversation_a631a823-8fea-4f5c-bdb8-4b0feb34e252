package com.facishare.webpage.customer.common;

import com.facishare.webpage.customer.common.model.QueryEmployeeScopeArg;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeResult;
import com.facishare.webpage.customer.common.model.QueryScopeNameArg;
import com.facishare.webpage.customer.common.model.QueryScopeNameResult;

/**
 * <AUTHOR> Yu
 */
public interface WebPageOrganizationService {

    QueryScopeNameResult queryScopeName(QueryScopeNameArg arg);

    QueryEmployeeScopeResult queryUserScope(QueryEmployeeScopeArg arg);

}
