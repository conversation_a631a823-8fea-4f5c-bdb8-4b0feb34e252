package com.facishare.webpage.customer.helper;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by zhangyu on 2020/12/21
 */
@Component
public class AppHomePageHelper extends HomePageHelper {

    @Override
    public int getAppType() {
        return BizType.APP.getType();
    }

    @Override
    public void checkCrmManagerPermission(int tenantId, int employeeId, int layoutType, List<Scope> scopeList) {

    }

    @Override
    public void checkHomePageName(int tenantId, int employeeId, int layoutType, String layoutId, String appId, String name) {

    }

    @Override
    public void checkHomePageScope(List<Scope> scopeList) {

    }

    @Override
    public void checkHomePageLayout(int dataVersion, List<HomePageLayoutCard> homePageLayoutCardList, JSONObject customerLayout) {

    }

}
