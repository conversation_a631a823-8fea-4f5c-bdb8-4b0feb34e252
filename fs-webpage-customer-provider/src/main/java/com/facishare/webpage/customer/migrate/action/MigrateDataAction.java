package com.facishare.webpage.customer.migrate.action;

import com.facishare.cep.plugin.model.UserInfo;

/**
 * <AUTHOR>
 * @date 2022/2/22 3:52 PM
 */
public interface MigrateDataAction {

    /**
     * 获取迁移列表
     *
     * @param userInfo
     * @param arg
     * @return
     */
    QueryMigrateDataList.Result queryMigrateDataList(UserInfo userInfo, QueryMigrateDataList.Arg arg);

    /**
     * 保存迁移数据
     *
     * @param userInfo
     * @param arg
     * @return
     */
    SaveMigrateDataList.Result saveMigrateDataList(UserInfo userInfo, SaveMigrateDataList.Arg arg);

}
