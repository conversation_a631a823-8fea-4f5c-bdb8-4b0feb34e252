package com.facishare.webpage.customer.metadata.factory;

import com.facishare.webpage.customer.metadata.ApplicationService;
import com.facishare.webpage.customer.metadata.CrossApplicationServiceImpl;
import com.facishare.webpage.customer.metadata.InnerApplicationServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * Created by zhangyu on 2020/8/18
 */
@Component(value = "applicationService")
public class ApplicationFactoryBean extends AbstractMetaFactoryBean<ApplicationService> {

    @Resource
    private InnerApplicationServiceImpl innerApplicationService;
    @Resource
    private CrossApplicationServiceImpl crossApplicationService;

    @PostConstruct
    public void init() {
        super.init(innerApplicationService, crossApplicationService);
    }

    @Override
    public Class<ApplicationService> getObjectType() {
        return ApplicationService.class;
    }
}
