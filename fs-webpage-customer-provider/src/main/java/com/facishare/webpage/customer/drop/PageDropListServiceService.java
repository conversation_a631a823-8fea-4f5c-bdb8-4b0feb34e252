package com.facishare.webpage.customer.drop;

import com.facishare.qixin.objgroup.common.service.model.resource.QueryComponentsResult;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.TempleType;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.component.ComponentService;
import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.designer.DropListService;
import com.facishare.webpage.customer.designer.model.DesignerAuthInfo;
import com.facishare.webpage.customer.designer.model.DropListType;
import com.facishare.webpage.customer.drop.model.GetHomePageDropList;
import com.facishare.webpage.customer.service.RemoteService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Component
public class PageDropListServiceService extends DropListService<GetHomePageDropList.Arg, GetHomePageDropList.Result> {

    @Resource
    private RemoteService remoteService;
    @Resource
    private ComponentService componentService;

    @Override
    public DropListType getDropListType() {
        return DropListType.PAGE;
    }

    @Override
    protected GetHomePageDropList.Result doGetDropList(GetHomePageDropList.Arg arg, DesignerAuthInfo designerAuthInfo) {
        int bizType = arg.getBizType();
        String bizId = getBizId(arg);
        Integer enterpriseId = designerAuthInfo.getUserInfo().getEnterpriseId();
        Locale locale = designerAuthInfo.getLocale();
        //获取自定义组件
        List<QueryComponentsResult.ComponentData> componentDataList = remoteService.getCusComponentList(enterpriseId, bizType, bizId);
        //获取业务组件
        List<DropListItem> businessComponents = componentService.getBusinessComponents(enterpriseId, bizType, bizId, locale);

        List<DropListItem> webDropListItemList =
                componentService.getDropListItemList(enterpriseId,
                        bizType,
                        bizId,
                        businessComponents,
                        componentService.getCusComponents(enterpriseId, bizType, bizId, componentDataList, TempleType.WEB), // 自定义组件类型转化
                        locale,
                        TempleType.WEB, arg.getLinkAppId());
        List<DropListItem> appDropListItemList =
                componentService.getDropListItemList(enterpriseId,
                        bizType,
                        bizId,
                        businessComponents,
                        componentService.getCusComponents(enterpriseId, bizType, bizId, componentDataList, TempleType.APP),
                        locale,
                        TempleType.APP, arg.getLinkAppId());
        return GetHomePageDropList.Result.builder().
                webDropListItemList(webDropListItemList).
                appDropListItemList(appDropListItemList).build();
    }

    @Override
    protected void before(GetHomePageDropList.Arg arg) {
        super.before(arg);
        if (arg.getBizType() == null || StringUtils.isEmpty(arg.getBizId())) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

    @Override
    protected GetHomePageDropList.Result after(GetHomePageDropList.Arg arg, GetHomePageDropList.Result result) {
        return super.after(arg, result);
    }

    @Override
    protected String getBizId(GetHomePageDropList.Arg arg) {
        return arg.getBizId();
    }
}
