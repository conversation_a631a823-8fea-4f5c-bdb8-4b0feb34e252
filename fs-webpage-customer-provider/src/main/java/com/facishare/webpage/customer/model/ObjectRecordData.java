package com.facishare.webpage.customer.model;

import com.facishare.webpage.customer.api.model.core.Icon;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON>hangyu on 2020/11/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ObjectRecordData {

    private String apiName;
    private String recordTypeApiName;
    private String recordTypeName;
    private String objectType;
    private Integer iconIndex;
    private Icon icon;
    private boolean active;

}
