package com.facishare.webpage.customer.controller.model.result.customer;

import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/5 6:32 下午
 */
@Data
public class GetCustomerLayoutsResult implements Serializable {

    private List<CustomerLayout> customerLayoutList;

    @Data
    public static class CustomerLayout implements Serializable {
        private String layoutApiName;
        private String name;
    }

    public static List<CustomerLayout> covertCustomerLayouts(List<HomePageLayoutTO> homePageLayoutTOList) {
        if (CollectionUtils.isEmpty(homePageLayoutTOList)) {
            return Lists.newArrayList();
        }
        return homePageLayoutTOList.stream().map(x -> {
            CustomerLayout customerLayout = new CustomerLayout();
            customerLayout.setLayoutApiName(x.getLayoutApiName());
            customerLayout.setName(x.getName());
            return customerLayout;
        }).collect(Collectors.toList());
    }

}
