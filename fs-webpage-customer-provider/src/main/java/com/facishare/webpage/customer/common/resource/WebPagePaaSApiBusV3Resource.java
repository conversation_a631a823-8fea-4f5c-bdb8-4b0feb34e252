package com.facishare.webpage.customer.common.resource;

import com.alibaba.fastjson.JSONObject;
import com.facishare.rest.api.model.newmetadata.NewMetadataBaseResult;
import com.facishare.rest.core.annotation.*;
import com.facishare.webpage.customer.common.resource.model.result.FindDescribeByApiNameListResult;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangyu on 2021/4/6
 */
@RestResource(
        value = "WebPagePaaSApiBusV3Resource",
        desc = "元数据服务",//ignoreI18n
        codec = "com.facishare.rest.api.codec.NewPaasMetadataCodec",
        contentType = "application/json"
)
public interface WebPagePaaSApiBusV3Resource {

    /**
     * 查询简版描述
     *
     * @param headers
     * @param isOnlyIncludeCustomObj
     * @param checkDetailObjectButton
     * @return
     */
    @GET(
            value = "/v3/object_describe/find_list",
            desc = "查询简版描述"//ignoreI18n
    )
    NewMetadataBaseResult<List<Map>> findSimpleObjectDescribeList(@HeaderMap Map<String, String> headers,
                                                                  @QueryParam("isOnlyIncludeCustomObj") String isOnlyIncludeCustomObj,
                                                                  @QueryParam("includeBigObject") String includeBigObject,
                                                                  @QueryParam("includeChangeOrderObj") String includeChangeOrderObj,
                                                                  @QueryParam("checkDetailObjectButton") String checkDetailObjectButton);

    /**
     * 获取对接人的名称
     *
     * @param headers
     * @param ids
     * @return
     */
    @POST(
            value = "/API/v1/inner/rest/object_data/PublicEmployeeObj/object_names",
            desc = "获取对接人的名称"//ignoreI18n
    )
    NewMetadataBaseResult<JSONObject> findPublicEmployeeObjNames(@HeaderMap Map<String, String> headers, @Body List<String> ids);

    /**
     * 获取对接企业的名称
     *
     * @param headers
     * @param ids
     * @return
     */
    @POST(
            value = "/API/v1/inner/rest/object_data/EnterpriseRelationObj/object_names",
            desc = "获取对接企业的名称"//ignoreI18n
    )
    NewMetadataBaseResult<JSONObject> findEnterpriseRelationObjNames(@HeaderMap Map<String, String> headers, @Body List<String> ids);

    @POST(value = "/API/v1/inner/rest/object_describe/list", desc = "获取对接企业的名称")
    FindDescribeByApiNameListResult findDescribeByApiNameList(@HeaderMap Map<String, String> headers,
                                                              @QueryParam("includeFields") String includeFields,
                                                              @Body List<String> apiNames);
}
