package com.facishare.webpage.customer.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;

import java.util.List;
import java.util.Locale;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/7/1
 */
public interface CovertCustomerManage {

    /**
     * 用户侧页面的转换
     *
     * @param userInfo
     * @param outerUserInfo
     * @param apiName
     * @param appType
     * @param appId
     * @param customerLayout
     * @param homePageLayouts
     * @param locale
     * @return
     */
    JSONObject convertUserLayout(UserInfo userInfo,
                                 OuterUserInfo outerUserInfo,
                                 String apiName,
                                 int appType,
                                 String appId,
                                 JSONObject customerLayout,
                                 List<HomePageLayoutCard> homePageLayouts,
                                 Locale locale);

    /**
     * 管理侧页面的转换
     *
     * @param userInfo
     * @param layoutId
     * @param appId
     * @param locale
     * @param customerLayout
     * @param homePageLayouts
     * @return
     */
    JSONObject convertTenantLayout(UserInfo userInfo,
                                   String layoutId,
                                   String appId,
                                   Locale locale,
                                   JSONObject customerLayout,
                                   List<HomePageLayoutCard> homePageLayouts);

    /**
     * 工具栏租户侧数据转换
     *
     * @param userInfo
     * @param appId
     * @param locale
     * @param utilityBarLayout
     * @return
     */
    JSONObject covertUtilityBarTenantLayout(UserInfo userInfo,
                                            String appId,
                                            Locale locale,
                                            JSONObject utilityBarLayout);

    /**
     * 工具栏用户侧数据转换
     *
     * @param userInfo
     * @param outerUserInfo
     * @param appType
     * @param appId
     * @param utilityBarLayout
     * @param locale
     * @return
     */
    JSONObject covertUtilityBarUserLayout(UserInfo userInfo,
                                          OuterUserInfo outerUserInfo,
                                          int appType,
                                          String appId,
                                          JSONObject utilityBarLayout,
                                          Locale locale);

}
