package com.facishare.webpage.customer.system.impl;

import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.qixin.sysdb.model.Data;
import com.facishare.qixin.sysdb.serivce.SysDataByLicenseService;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity;
import com.facishare.webpage.customer.service.HomePageCommonService;
import com.facishare.webpage.customer.system.PaasAppSystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/25 2:42 下午
 */
@Component
@Slf4j
public class PaasAppSystemServiceImpl implements PaasAppSystemService {

    @Resource
    private SysDataByLicenseService paasAppSystemDataService;
    @Resource
    private HomePageCommonService homePageCommonService;

    @Override
    public PaaSAppEntity queryPaaSAppEntityByAppId(int tenantId, String appId) {
        Filter<Query> filter = new Filter<Query>() {
            @Override
            public void afterPreset(Data data) {
                PaaSAppEntity paaSAppEntity = (PaaSAppEntity) data;
                paaSAppEntity.setTenantId(tenantId);
                paaSAppEntity.setCreatorId(-10000);
                paaSAppEntity.setUpdaterId(-10000);
            }
        };
        PaaSAppEntity paaSAppEntity = (PaaSAppEntity) paasAppSystemDataService.getDataByApiName(tenantId, appId, filter);
        return paaSAppEntity;
    }

    @Override
    public PaaSAppEntity createOrUpdate(int tenantId, String appId, PaaSAppEntity paaSAppEntity) {
        validateAndLog(tenantId, appId, paaSAppEntity);
        return (PaaSAppEntity) paasAppSystemDataService.createOrUpdate(tenantId, paaSAppEntity.getAppId(), paaSAppEntity);
    }

    private void validateAndLog(int tenantId, String appId, PaaSAppEntity paaSAppEntity) {
        if (Objects.nonNull(appId) && paaSAppEntity.getAppType() == 0) {
            log.warn("paas app type is null! ei:{}, appId:{}", tenantId, appId);
        }
    }

    @Override
    public List<PaaSAppEntity> queryTenantPaaSAppEntityList(int tenantId) {
        return paasAppSystemDataService.queryDataList(tenantId, new Filter() {
        });
    }

    @Override
    public List<PaaSAppEntity> queryTenantPaaSAppEntityList(int tenantId, int appType, List<String> accessTypeList) {
        return paasAppSystemDataService.queryDataList(tenantId, new Filter<Query>() {
            @Override
            public void addFilter(Query query) {
                Query<PaaSAppEntity> paasAppQuery = (Query<PaaSAppEntity>) query;
                if (appType != AppTypeEnum.PAAS_APP.getAppType()) {
                    paasAppQuery.field("appType").equal(appType);
                } else {
                    paasAppQuery.or(
                            paasAppQuery.criteria("appType").equal(AppTypeEnum.PAAS_APP.getAppType()),
                            paasAppQuery.criteria("appType").doesNotExist()
                    );
                }
                if (CollectionUtils.isNotEmpty(accessTypeList)) {
                    paasAppQuery.field("accessType").in(accessTypeList);
                }
            }
        });
    }

    @Override
    public List<PaaSAppEntity> queryUserPaaSAppEntityList(int tenantId, int employeeId) {
        List<String> scopeList = homePageCommonService.getScopeList(tenantId, employeeId, null);
        List<PaaSAppEntity> userPaaSAppList = paasAppSystemDataService.getUserDataList(tenantId, employeeId, new Filter<Query>() {
            @Override
            public void addFilter(Query query) {
                query.field("scopeList").hasAnyOf(scopeList);
            }

            @Override
            public void afterPreset(Data data) {
                PaaSAppEntity paaSAppEntity = (PaaSAppEntity) data;
                paaSAppEntity.setTenantId(tenantId);
                paaSAppEntity.setCreatorId(-10000);
            }
        });
        return userPaaSAppList;
    }

    @Override
    public List<PaaSAppEntity> queryUserPaaSAppEntityList(int tenantId, int employeeId, List<String> accessTypeList, int appType) {
        List<String> scopeList = homePageCommonService.getScopeList(tenantId, employeeId, null);

        List<PaaSAppEntity> paaSAppEntityList = paasAppSystemDataService.getUserDataList(tenantId, employeeId, new Filter<Query>() {
            @Override
            public void addFilter(Query query) {
                Query<PaaSAppEntity> paasAppQuery = (Query<PaaSAppEntity>) query;
                if (appType != AppTypeEnum.PAAS_APP.getAppType()) {
                    paasAppQuery.field("appType").equal(appType);
                } else {
                    paasAppQuery.or(
                            paasAppQuery.criteria("appType").equal(AppTypeEnum.PAAS_APP.getAppType()),
                            paasAppQuery.criteria("appType").doesNotExist()
                    );
                }
                if (CollectionUtils.isNotEmpty(accessTypeList)) {
                    paasAppQuery.field("accessType").in(accessTypeList);
                }
                paasAppQuery.field("scopeList").hasAnyOf(scopeList);
            }
        });
        return paaSAppEntityList;
    }
}
