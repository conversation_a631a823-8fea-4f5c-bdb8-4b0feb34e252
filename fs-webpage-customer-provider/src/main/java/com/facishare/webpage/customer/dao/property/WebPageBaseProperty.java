package com.facishare.webpage.customer.dao.property;

import com.facishare.qixin.datastore.BaseProperty;

/**
 * <AUTHOR>
 * @date 2022/3/3 8:06 PM
 */
public interface WebPageBaseProperty extends BaseProperty {

    String EMPLOYEE_ID = "EPId";
    String APP_ID = "AId";
    String APP_TYPE = "AT";
    String PAGE_TEMPLATE_ID = "PTId";
    String SOURCE_TYPE = "ST";
    String SOURCE_ID = "SId";
    String CREATE_TIME = "CT";
    String CREATE_ID = "CI";
    String UPDATE_TIME = "UT";
    String UPDATE_ID = "UI";
    String STATUS = "status";
    String FROM_WEB_PAGE_TEMPLATE_ID = "fromWebPageTemplateId";
    String HAS_BEEN_SYN_TO_APP = "hasBeenSynToApp";

}
