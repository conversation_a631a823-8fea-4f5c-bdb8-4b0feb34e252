package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSONArray;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.config.model.AddMenuItemVo;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/6/10
 */
@Component
public class AddMenuItemConfig {

    private static Logger logger = LoggerFactory.getLogger(AddMenuItemConfig.class);

    private List<AddMenuItemVo> addMenuItemVoList;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-webpage-addMenuItem-config", this::loadConfig);
    }

    private void loadConfig(IConfig config) {

        List<AddMenuItemVo> addMenuItemVos = JSONArray.parseArray(config.getString(), AddMenuItemVo.class);

        addMenuItemVos = addMenuItemVos.stream().sorted((x1, x2) -> {
            if (x1.getApiName().equals(x2.getAfterApiName())) {
                return -1;
            } else {
                return 1;
            }
        }).collect(Collectors.toList());
        addMenuItemVoList = addMenuItemVos;
        logger.info("init AddMenuItemConfig config addMenuItemVoList:{};", addMenuItemVoList);
    }

    public List<AddMenuItemVo> getAddMenuItems(String appId) {
        String originAppId = appId;
        String queryAppId = appId;
        if (WebPageUtils.checkPaaSApp(appId)) {
            queryAppId = BizType.PAAS.getDefaultAppId();
        }
        List<AddMenuItemVo> returnAddMenuItemVos = Lists.newArrayList();
        for (AddMenuItemVo addMenuItemVo : addMenuItemVoList) {
            if (!queryAppId.equals(addMenuItemVo.getAppId())) {
                continue;
            }
            AddMenuItemVo cloneAddMenuItemVo = addMenuItemVo.clone();
            cloneAddMenuItemVo.setAppId(originAppId);
            cloneAddMenuItemVo.setHiddenManage(false);
            returnAddMenuItemVos.add(cloneAddMenuItemVo);
        }
        return Collections.unmodifiableList(returnAddMenuItemVos);
    }

    public List<String> getHiddenManageApiNames() {
        return addMenuItemVoList.stream()
                .filter(addMenuItemVo -> addMenuItemVo.isHiddenManage())
                .map(addMenuItemVo -> addMenuItemVo.getApiName())
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        AddMenuItemConfig addMenuItemConfig = new AddMenuItemConfig();
        addMenuItemConfig.init();

        System.out.println(addMenuItemConfig.getAddMenuItems("FSAID_PaaS_b52773469870"));
    }


}
