package com.facishare.webpage.customer.service.impl;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.constant.UIPaaSI18NKey;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.constant.ApplyEnum;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.config.ObjectConfig;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.service.UIPaasLicenseService;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.dao.MenuEntityDao;
import com.facishare.webpage.customer.dao.entity.MenuEntity;
import com.facishare.webpage.customer.helper.menuitem.CustomerMenuFactory;
import com.facishare.webpage.customer.helper.menuitem.CustomerMenuHelperService;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.facishare.webpage.customer.service.CustomerMenuService;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.facishare.webpage.customer.util.CustomerMenuUtil;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.utils.UiPaasParallelUtils.createParallelTask;

/**
 * Created by zhangyu on 2020/11/3
 */
@Component
@Slf4j
public class CustomerMenuServiceImpl implements CustomerMenuService {

    @Resource
    private MenuEntityDao menuEntityDao;
    @Resource
    private OrganizationCommonService organizationCommonService;
    @Resource
    private LanguageService languageService;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Resource
    private ObjectConfig objectConfig;
    @Resource
    private UIPaasLicenseService uiPaasLicenseService;
    @Resource
    private I18nService i18nService;

    @Override
    public CustomerMenu saveCustomerMenu(int tenantId, int employeeId, CustomerMenu customerMenu) {
        if (customerMenu == null) {
            return null;
        }
        MenuEntity menuEntity = CustomerMenuUtil.covertMenuEntityByCustomerMenu(tenantId, employeeId, customerMenu);
        menuEntityDao.save(Lists.newArrayList(menuEntity));
        return customerMenu;
    }

    @Override
    public CustomerMenu updateCustomerMenu(int tenantId, int employeeId, CustomerMenu customerMenu, Locale locale) {

        if (customerMenu == null) {
            return null;
        }
        MenuEntity menuEntity = CustomerMenuUtil.covertMenuEntityByCustomerMenu(tenantId, employeeId, customerMenu);
        MenuEntity entity = menuEntityDao.findAndModify(menuEntity);

        List<I18nTrans.TransArg> transArg = Lists.newArrayList();
        transArg.add(TranslateI18nUtils.convertToTransArg(WebPageConstants.CUS_MENU_I18N_PRE + menuEntity.getMenuId(),
                Lists.newArrayList(UIPaaSI18NKey.GROUP_PREFIX + menuEntity.getApiName(),
                        UIPaaSI18NKey.GROUP_PREFIX + menuEntity.getName()),
                menuEntity.getName()));
        i18nService.syncTransValueIncludePreKeyV2(tenantId, transArg, locale.toLanguageTag());
        return CustomerMenuUtil.covertCustomerMenuByMenuEntity(null, entity);
    }

    @Override
    public void deleteCustomerMenu(int tenantId, List<String> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return;
        }
        menuEntityDao.deleteMenusByMenuIds(tenantId, menuIds);
    }

    @Override
    public List<CustomerMenu> queryCustomerByType(int tenantId,
                                                  String type,
                                                  int applyType,
                                                  String appId,
                                                  String singleApp,
                                                  Locale locale,
                                                  Boolean needAddScope) {
        List<MenuEntity> menuEntities = menuEntityDao.queryMenuEntities(tenantId, applyType, singleApp, appId);
        List<CustomerMenu> customerMenuList = menuEntitiesCovert2CustomerMenus(tenantId, applyType, type, menuEntities, locale);
        customerMenuList = filterCustomerMenus(tenantId, locale, customerMenuList); // 补充 对象/自定义页面 的内容项, 过滤掉空的
        if (needAddScope) {
            Set<Scope> scopeList = Sets.newHashSet();
            for (CustomerMenu customerMenu : customerMenuList) {
                scopeList.addAll(customerMenu.getScopeList());
            }
            Map<Integer, Object> scopeNameMap = organizationCommonService.getScopeName(tenantId, "CRM", Lists.newArrayList(scopeList), applyType == ApplyEnum.CrossApply.getApplyType());
            setScopeNameList(customerMenuList, scopeNameMap);
        }
        if (uiPaasLicenseService.existMultiLanguageModule(tenantId)) {
            //设置自定义菜单项的多语
            languageService.setCustomerMenuLanguage(tenantId, customerMenuList, locale);
        }
        return customerMenuList;

    }

    @NotNull
    private List<CustomerMenu> filterCustomerMenus(int tenantId, Locale locale, List<CustomerMenu> customerMenuList) {
        Map<Integer, List<CustomerMenu>> customerMenuMap = customerMenuList.stream().
                filter(Objects::nonNull).
                collect(Collectors.groupingBy(CustomerMenu::getMenuType));
        customerMenuMap.forEach((x, y) -> {
            CustomerMenuHelperService customerMenuHelperService = CustomerMenuFactory.getCustomerMenuHelperService(x);
            if (customerMenuHelperService != null) {
                customerMenuHelperService.setCustomerMenuContent(tenantId, y, locale);
            }
        });
        //content为空表示对象为空或者自定义页面为空,需对其过滤掉
        customerMenuList = customerMenuList.stream().
                filter(x -> StringUtils.isNotEmpty(x.getContent())).
                peek(x -> {
                    Icon icon = objectConfig.getIconByApiName(x.getMenuApiName());
                    if (x.getIcon() == null) {
                        x.setIcon(icon);
                    }
                    if (StringUtils.isEmpty(x.getIcon().getIcon_1())) {
                        x.getIcon().setIcon_1(icon.getIcon_1());
                    }
                    if (StringUtils.isEmpty(x.getIcon().getIcon_2())) {
                        x.getIcon().setIcon_1(icon.getIcon_2());
                    }
                }).
                collect(Collectors.toList());
        return customerMenuList;
    }

    @NotNull
    private List<CustomerMenu> menuEntitiesCovert2CustomerMenus(int tenantId,
                                                                int applyType,
                                                                String type,
                                                                List<MenuEntity> menuEntities, Locale locale) {
        Map<String, String> appNameMap = Maps.newHashMap();
        if (applyType == ApplyEnum.CrossApply.getApplyType()) {
            appNameMap = remoteCrossService.getUpSimpleLinkApp(tenantId, locale).stream()
                    .filter(x -> Objects.nonNull(x.getAppName()))
                    .collect(Collectors.toMap(SimpleLinkAppResult::getAppId, SimpleLinkAppResult::getAppName, (x1, x2) -> x1));
        }
        Map<String, String> finalAppNameMap = appNameMap;
        if (StringUtils.isEmpty(type)) {
            return menuEntities.stream().
                    map(menuEntity ->
                            CustomerMenuUtil.covertCustomerMenuByMenuEntity(finalAppNameMap.getOrDefault(menuEntity.getAppId(), null), menuEntity)).
                    collect(Collectors.toList());
        }

        return menuEntities.stream().
                filter(x -> {
                    if (x == null) {
                        return false;
                    }
                    if (StringUtils.isEmpty(type)) {
                        return true;
                    }
                    return x.getSupportClients().contains(type);
                }).
                map(menuEntity ->
                        CustomerMenuUtil.covertCustomerMenuByMenuEntity(finalAppNameMap.getOrDefault(menuEntity.getAppId(), null), menuEntity)).
                collect(Collectors.toList());

    }

    private void setScopeNameList(List<CustomerMenu> customerMenuList, Map<Integer, Object> scopeNameMap) {
        if (CollectionUtils.isEmpty(customerMenuList)) {
            return;
        }

        for (CustomerMenu customerMenu : customerMenuList) {
            List<String> scopeNames = ScopesUtil.getScopeNames(scopeNameMap, customerMenu.getScopeList());
            customerMenu.setScopeNameList(scopeNames);

        }
    }

    @Override
    public CustomerMenu queryCustomerMenuById(int tenantId, String menuId, Locale locale) {
        if (StringUtils.isEmpty(menuId)) {
            return null;
        }
        List<MenuEntity> menuEntities = menuEntityDao.queryMenusByMenuIds(tenantId, Lists.newArrayList(menuId));
        if (CollectionUtils.isEmpty(menuEntities)) {
            return null;
        }
        String appName = null;
        MenuEntity menuEntity = menuEntities.get(0);
        if (menuEntity.getApplyType() == ApplyEnum.CrossApply.getApplyType()) {
            Map<String, String> appNameMap = remoteCrossService.getUpSimpleLinkApp(tenantId, locale).stream().
                    filter( m -> StringUtils.isNotEmpty(m.getAppId()) && StringUtils.isNotEmpty(m.getAppName())).
                    collect(Collectors.toMap(SimpleLinkAppResult::getAppId, SimpleLinkAppResult::getAppName, (x1, x2) -> x1));
            appName = appNameMap.getOrDefault(menuEntity.getAppId(), null);
        }
        CustomerMenu customerMenu = CustomerMenuUtil.covertCustomerMenuByMenuEntity(appName, menuEntity);
        CustomerMenuHelperService customerMenuHelperService = CustomerMenuFactory.getCustomerMenuHelperService(customerMenu.getMenuType());
        if (customerMenuHelperService != null) {
            customerMenuHelperService.setCustomerMenu(tenantId, customerMenu, locale);
        }
        //设置多语
        if (uiPaasLicenseService.existMultiLanguageModule(tenantId)) {
            languageService.setCustomerMenuLanguage(tenantId, Lists.newArrayList(customerMenu), locale);
        }

        return customerMenu;
    }

    @Override
    public CustomerMenu queryCustomerMenuByName(int tenantId, int applyType, String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        MenuEntity menuEntity = menuEntityDao.queryMenuEntityByName(tenantId, applyType, name);
        return CustomerMenuUtil.covertCustomerMenuByMenuEntity(null, menuEntity);
    }

    @Override
    public List<CustomerMenu> queryManCustomerMenuByApiNames(int enterpriseId, List<String> menuApiNames, Locale locale) {
        if (CollectionUtils.isEmpty(menuApiNames)) {
            return Lists.newArrayList();
        }
        List<MenuEntity> menuEntities = menuEntityDao.queryMenusByMenuIds(enterpriseId, menuApiNames);
        List<CustomerMenu> customerMenus = covertCustomerMenus(enterpriseId, locale, menuEntities);
        customerMenus = filterCustomerMenus(enterpriseId, locale, customerMenus);
        return customerMenus;
    }

    @Override
    public List<CustomerMenu> queryUserCustomerMenuByApiNames(int enterpriseId,
                                                              Integer employeeId,
                                                              Long outTenantId,
                                                              Long outUid,
                                                              Integer identityType,
                                                              String appId,
                                                              List<String> menuApiNames,
                                                              Locale locale) {
        List<Scope> scopeList = organizationCommonService.queryScopeList(enterpriseId, employeeId, outTenantId, outUid, appId);
        List<String> scopesToString = ScopesUtil.buildScopesToString(scopeList);
        List<MenuEntity> menuEntities = menuEntityDao.queryMenuEntities(enterpriseId, menuApiNames, scopesToString);
        List<CustomerMenu> customerMenus = covertCustomerMenus(enterpriseId, locale, menuEntities);
        Map<Integer, List<CustomerMenu>> customerMenuMap = customerMenus.stream().
                filter(Objects::nonNull).
                collect(Collectors.groupingBy(CustomerMenu::getMenuType));
        ParallelUtils.ParallelTask parallelTask = createParallelTask(String.valueOf(enterpriseId));
        List<CustomerMenu> resultCustomerMenus = Lists.newCopyOnWriteArrayList();
        customerMenuMap.forEach((x, y) -> {
            parallelTask.submit(MonitorTaskWrapper.wrap(() -> {
                CustomerMenuHelperService customerMenuHelperService = CustomerMenuFactory.getCustomerMenuHelperService(x);
                if (customerMenuHelperService != null) {
                    List<CustomerMenu> filterCustomerMenus =
                            customerMenuHelperService.filterCustomerMenuForPermission(
                                    enterpriseId, appId, employeeId, outTenantId, outUid, identityType, y, locale);
                    resultCustomerMenus.addAll(filterCustomerMenus);
                }
            }));
        });
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("queryUserCustomerMenuByApiNames error by enterpriseId:{}, employeeId:{}, outUid:{}, menuApiNames:{}",
                    enterpriseId, employeeId, outUid, menuApiNames, e);
        }
        return resultCustomerMenus;
    }

    @Override
    public List<CustomerMenu> queryCustomerMenuByMenuInfo(int enterpriseId, String pageTemplateId, int applyType, int menuType, Locale locale) {
        List<MenuEntity> menuEntities = menuEntityDao.queryMenuEntities(enterpriseId, pageTemplateId, applyType, menuType);
        if (CollectionUtils.isEmpty(menuEntities)) {
            return new ArrayList<>();
        }
        List<CustomerMenu> customerMenus = covertCustomerMenus(enterpriseId, locale, menuEntities);
        customerMenus = filterCustomerMenus(enterpriseId, locale, customerMenus);
        return customerMenus;
    }

    private List<CustomerMenu> covertCustomerMenus(int enterpriseId, Locale locale, List<MenuEntity> menuEntities) {
        List<CustomerMenu> customerMenus = CustomerMenuUtil.covert2CustomerMenus(menuEntities);
        //设置自定义菜单项的多语
        languageService.setCustomerMenuLanguage(enterpriseId, customerMenus, locale);
        return customerMenus;
    }
}
