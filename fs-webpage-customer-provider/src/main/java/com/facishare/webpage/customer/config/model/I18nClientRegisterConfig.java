package com.facishare.webpage.customer.config.model;

import com.fxiaoke.i18n.client.I18nClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Created by zhangyu on 2020/7/22
 */
@Component
public class I18nClientRegisterConfig {


    @PostConstruct
    private void init() {
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags("server", new String[]{"bi_view","bi_custom","eip"});
    }


}
