package com.facishare.webpage.customer.common;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.paas.I18N;
import com.facishare.qixin.common.utils.FastJsonSerializer;
import com.facishare.webpage.customer.api.model.core.customelayout.Filter;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.api.constant.EmployeeConstant;
import com.facishare.webpage.customer.core.model.DateFilter;
import com.facishare.webpage.customer.core.util.FilterUtils;
import com.facishare.webpage.customer.api.model.EmployeeConfig;
import com.facishare.webpage.customer.service.EmployeeConfigBaseService;
import com.facishare.webpage.customer.service.RemoteService;
import com.facishare.webpage.customer.util.EmployeeConfigUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2019/11/19
 */
public class EmployeeConfigCommonServiceImpl implements EmployeeConfigCommonService {

    private static String TIME = "0";
    private static String MONTH = "本月"; //ignoreI18n
    private static String QUARTER = "本季度"; //ignoreI18n
    private static int DepartmentType = 4;
    private static int employeeType = 0;

    @Autowired
    private RemoteService remoteService;
    @Resource
    private EmployeeConfigBaseService employeeConfigBaseService;
    @Resource
    private EmployeeConfigCommonService employeeConfigCommonService;
    @Resource
    private FilterUtils filterUtils;

    @Override
    public EmployeeConfig getDefaultEmployeeConfig(int tenantId, int employeeId, int type) {
        EmployeeConfig employeeConfig = new EmployeeConfig();

        EmployeeConstant.HomePageDefaultValue homePageDefaultValue = new EmployeeConstant.HomePageDefaultValue();
        homePageDefaultValue.setStartTime(TIME);
        homePageDefaultValue.setEndTime(TIME);
        switch (type) {
            case 13:
                homePageDefaultValue.setDateId(String.valueOf(type));
                homePageDefaultValue.setDateType(I18N.text(I18NKey.THIS_QUARTER));
                break;
            default:
                homePageDefaultValue.setDateId(String.valueOf(4));
                homePageDefaultValue.setDateType(I18N.text(I18NKey.THIS_MONTH));
                break;
        }

        //如果在灰度范围之内，筛选器选择的是全部；否则下发当前这个人负责的的部门
        if (WebPageGraySwitch.isAllowFilterAllByBusiness(tenantId)) {
            homePageDefaultValue.setIsAll(true);
        } else {
            List<EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem> homePageDefaultItemList = Lists.newArrayList();
            List<Integer> departmentIds = remoteService.getResponsibleDepartmentId(tenantId, Lists.newArrayList(employeeId));
            if (CollectionUtils.isEmpty(departmentIds)) {
                EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem homePageDefaultItem = new EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem();
                homePageDefaultItem.setId(employeeId);
                homePageDefaultItem.setType(employeeType);
                homePageDefaultItemList.add(homePageDefaultItem);
            } else {
                departmentIds.stream().forEach(departmentId -> {
                    EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem homePageDefaultItem = new EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem();
                    homePageDefaultItem.setId(departmentId);
                    homePageDefaultItem.setType(DepartmentType);
                    homePageDefaultItemList.add(homePageDefaultItem);
                });
            }
            homePageDefaultValue.setEmpsAndDeps(homePageDefaultItemList);
        }

        employeeConfig.setKey(EmployeeConstant.HomePageDefault);
        employeeConfig.setValue(FastJsonSerializer.entityToJson(homePageDefaultValue));
        return employeeConfig;
    }

    @Override
    public EmployeeConfig getDefaultTenantEmployeeConfig() {
        EmployeeConfig employeeConfig = new EmployeeConfig();
        employeeConfig.setKey(EmployeeConstant.UserDefindSelect);
        employeeConfig.setValue("{\"enableEmpFilterOfGlobalFilter\":1,\"enableDateFilterOfGlobalFilter\":1}");
        return employeeConfig;
    }

    @Override
    public List<com.facishare.webpage.customer.api.model.core.customelayout.Filter> buildManagerFilters(int tenantId, int employeeId, String layoutId, boolean cross) {
        List<com.facishare.webpage.customer.api.model.core.customelayout.Filter> filters = Lists.newArrayList();
        filters.addAll(buildHomePageDefault(tenantId, cross));
        filters.add(buildUserDefaultSelect(tenantId, employeeId, layoutId));

        return filters;
    }

    @Override
    public List<Filter> buildUserFilters(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId, List<JSONObject> filters, boolean checkHasFilter) {
        //如果不是bi的灰度企业，且没有筛选器组件，才会下发默认的筛选器
        if (!checkHasFilter && !WebPageGraySwitch.isAllownotQueryUserFilterGrayEis(userInfo.getEnterpriseId())) {
            List<JSONObject> jsonObjectList = filterUtils.covertFilters(userInfo.getEnterpriseId(),
                    userInfo.getEmployeeId(),
                    outerUserInfo == null ? null : outerUserInfo.getOutUserId(),
                    null);
            return jsonObjectList.stream().map(x -> {
                Filter filter = new Filter();
                filter.setFilterType(x.getString(ComponentConstant.filterType));
                filter.setFilterData(x.getString(ComponentConstant.filterData));
                return filter;
            }).collect(Collectors.toList());
        }

        if (EmployeeConfigUtil.checkOldFilters(filters)) {
            return buildOldUserFilters(userInfo, outerUserInfo, layoutId);
        } else {
            return buildNewUserFilters(userInfo, outerUserInfo, layoutId, filters);
        }

    }


    private List<Filter> buildNewUserFilters(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId, List<JSONObject> filters) {
        int employeeId;
        if (outerUserInfo == null) {
            employeeId = userInfo.getEmployeeId();
        } else {
            employeeId = outerUserInfo.getUpstreamOwnerId() == null ? 0 : outerUserInfo.getUpstreamOwnerId();
        }
        List<EmployeeConfig> employeeConfigValueByKeys = new ArrayList<>();
        //如果是bi的灰度企业，则不再下发用户自定义筛选器
        if (!WebPageGraySwitch.isAllownotQueryUserFilterGrayEis(userInfo.getEnterpriseId())) {
            employeeConfigValueByKeys = employeeConfigBaseService.getEmployeeConfigValueByKeys(layoutId, userInfo.getEnterpriseId(), employeeId, Lists.newArrayList(EmployeeConstant.HomePageDefault));
        }
        if (CollectionUtils.isEmpty(employeeConfigValueByKeys)) {
            List<JSONObject> jsonObjectList = filterUtils.covertFilters(userInfo.getEnterpriseId(), employeeId, outerUserInfo == null ? null : outerUserInfo.getOutUserId(), filters);
            return jsonObjectList.stream().map(x -> {
                Filter filter = new Filter();
                filter.setFilterType(x.getString(ComponentConstant.filterType));
                filter.setFilterData(x.getString(ComponentConstant.filterData));
                return filter;
            }).collect(Collectors.toList());
        }
        List<Filter> filterList = EmployeeConfigUtil.covertPageEmployeeConfig(employeeConfigValueByKeys.get(0));
        Map<String, Filter> filterMap = filterList.stream().collect(Collectors.toMap(Filter::getFilterType, x -> x, (v1, v2) -> v1));
        List<Filter> newFilters = Lists.newArrayList();
        filters.stream().forEach(x -> {
            switch (x.getString(ComponentConstant.filterType)) {
                case ComponentConstant.dataFilter:
                    JSONObject dateJson = x.getJSONObject(ComponentConstant.filterData);
                    newFilters.add(covertDateFilter(dateJson, filterMap.get(ComponentConstant.dataFilter)));
                    break;
                case ComponentConstant.selectFilter:
                    JSONObject selectJson = x.getJSONObject(ComponentConstant.filterData);
                    newFilters.add(
                            covertSelectFilter(userInfo.getEnterpriseId(), employeeId, selectJson,
                                    filterMap.get(ComponentConstant.selectFilter),
                                    outerUserInfo == null ? null : outerUserInfo.getOutUserId()));
                    break;
                case ComponentConstant.filterDefaultType:
                    Filter filter = new Filter();
                    filter.setFilterType(ComponentConstant.filterDefaultType);
                    filter.setFilterData(x.getString(ComponentConstant.filterData));
                    newFilters.add(filter);
                    break;
                default:
                    newFilters.add(JSONObject.toJavaObject(x, Filter.class));
                    break;
            }
        });
        return newFilters;
    }

    private Filter covertSelectFilter(int tenantId, int employeeId, JSONObject selectJson, Filter selectFilter, Long outerUId) {
        Filter filter = new Filter();
        filter.setFilterType(ComponentConstant.selectFilter);
        if (selectFilter == null || (selectJson.getBoolean("canEdit") != null && !selectJson.getBoolean("canEdit"))) {
            JSONObject covertSelector = filterUtils.covertSelector(tenantId, employeeId, outerUId, selectJson);
            filter.setFilterData(JSONObject.toJSONString(covertSelector));
            return filter;
        }
        filter.setFilterData(selectFilter.getFilterData());
        return filter;
    }

    private Filter covertDateFilter(JSONObject dateJson, Filter dateFilter) {
        Filter filter = new Filter();
        filter.setFilterType(ComponentConstant.dataFilter);
        if (dateFilter == null || (dateJson.getBoolean("canEdit") != null && !dateJson.getBoolean("canEdit"))) {
            filter.setFilterData(JSONObject.toJSONString(dateJson));
            return filter;
        }
        filter.setFilterData(dateFilter.getFilterData());
        return filter;
    }

    private List<Filter> buildOldUserFilters(UserInfo userInfo, OuterUserInfo outerUserInfo, String layoutId) {
        int employeeId;
        if (outerUserInfo == null) {
            employeeId = userInfo.getEmployeeId();
        } else {
            employeeId = outerUserInfo.getUpstreamOwnerId() == null ? 0 : outerUserInfo.getUpstreamOwnerId();
        }
        List<EmployeeConfig> employeeConfigValueByKeys = employeeConfigBaseService.getEmployeeConfigValueByKeys(layoutId, userInfo.getEnterpriseId(), employeeId, Lists.newArrayList(EmployeeConstant.HomePageDefault, EmployeeConstant.UserDefindSelect));
        employeeConfigValueByKeys = employeeConfigCommonService.buildEmployeeConfig(employeeConfigValueByKeys, userInfo.getEnterpriseId(), employeeId, outerUserInfo, 4);
        List<Filter> filters = Lists.newArrayList();
        employeeConfigValueByKeys.stream().forEach(x -> {
            Filter filter = new Filter();
            if (x.getKey() == EmployeeConstant.UserDefindSelect) {
                filter.setFilterType(ComponentConstant.filterDefaultType);
                filter.setFilterData(x.getValue());
                filters.add(filter);
            } else {
                List<Filter> covertFilters = EmployeeConfigUtil.covertPageEmployeeConfig(x);
                filters.addAll(covertFilters);
            }
        });
        return filters;
    }


    private com.facishare.webpage.customer.api.model.core.customelayout.Filter buildUserDefaultSelect(int tenantId, int employeeId, String layoutId) {
        List<EmployeeConfig> employeeConfigValueByKeys = employeeConfigBaseService.getEmployeeConfigValueByKeys(layoutId, tenantId, employeeId, Lists.newArrayList(EmployeeConstant.UserDefindSelect));
        com.facishare.webpage.customer.api.model.core.customelayout.Filter filter = new com.facishare.webpage.customer.api.model.core.customelayout.Filter();
        filter.setFilterType("pageDefault");
        if (CollectionUtils.isEmpty(employeeConfigValueByKeys)) {
            filter.setFilterData("{\"enableEmpFilterOfGlobalFilter\":1,\"enableDateFilterOfGlobalFilter\":1}");
        } else {
            filter.setFilterData(employeeConfigValueByKeys.get(0).getValue());
        }
        return filter;
    }

    private List<com.facishare.webpage.customer.api.model.core.customelayout.Filter> buildHomePageDefault(int tenantId, boolean cross) {
        DateFilter dateFilter = new DateFilter();
        dateFilter.setStartTime(String.valueOf(0));
        dateFilter.setEndTime(String.valueOf(0));
        dateFilter.setDateId(String.valueOf(4));
        dateFilter.setDateType(I18N.text(I18NKey.THIS_MONTH));

        com.facishare.webpage.customer.api.model.core.customelayout.Filter date = new com.facishare.webpage.customer.api.model.core.customelayout.Filter();
        date.setFilterType("date");
        date.setFilterData(JSONObject.toJSONString(dateFilter));

        JSONObject selector = new JSONObject();
        selector.put("canEdit", true);
        if (cross || WebPageGraySwitch.isAllowFilterAllByBusiness(tenantId)) {
            selector.put("type", 2);
        } else {
            selector.put("type", 3);
        }
        com.facishare.webpage.customer.api.model.core.customelayout.Filter select = new com.facishare.webpage.customer.api.model.core.customelayout.Filter();
        select.setFilterType("selector");
        select.setFilterData(selector.toJSONString());

        return Lists.newArrayList(select, date);
    }

    @Override
    public List<EmployeeConfig> buildEmployeeConfig(List<EmployeeConfig> employeeConfigList,
                                                    int enterpriseId, int employeeId, OuterUserInfo outerUserInfo, int type) {

        Map<Integer, EmployeeConfig> employeeConfigMap = employeeConfigList.stream().collect(Collectors.toMap(EmployeeConfig::getKey, x -> x, (x1, x2) -> x1));

        List<EmployeeConfig> employeeConfigs = Lists.newArrayList();

        EmployeeConfig homePageDefaultConfig = employeeConfigMap.get(EmployeeConstant.HomePageDefault);

        if (outerUserInfo == null) {
            employeeConfigs.add(buildInnerHomePageDefault(enterpriseId, employeeId, type, homePageDefaultConfig));
        } else {
            employeeConfigs.add(buildCrossHomePageDefault(outerUserInfo.getOutUserId(), homePageDefaultConfig));
        }

        EmployeeConfig userSelectConfig = employeeConfigMap.get(EmployeeConstant.UserDefindSelect);
        if (userSelectConfig == null) {
            employeeConfigs.add(getDefaultTenantEmployeeConfig());
        } else {
            employeeConfigs.add(userSelectConfig);
        }

        EmployeeConfig appLayoutListOrderConfig = employeeConfigMap.get(EmployeeConstant.APP_LAYOUT_LIST_ORDER);
        if (appLayoutListOrderConfig != null) {
            employeeConfigs.add(appLayoutListOrderConfig);
        }

        return employeeConfigs;
    }

    private EmployeeConfig getCrossDefaultEmployeeConfig(long outerUId) {

        EmployeeConstant.HomePageDefaultValue homePageDefaultValue = new EmployeeConstant.HomePageDefaultValue();
        homePageDefaultValue.setStartTime(TIME);
        homePageDefaultValue.setEndTime(TIME);
        homePageDefaultValue.setDateId(String.valueOf(4));
        homePageDefaultValue.setDateType(I18N.text(I18NKey.THIS_MONTH));
        homePageDefaultValue.setIsAll(true);

        homePageDefaultValue.setEmpsAndDeps(getDefaultCrossEmpsAndDeps(outerUId));

        EmployeeConfig employeeConfig = new EmployeeConfig();
        employeeConfig.setKey(EmployeeConstant.HomePageDefault);
        employeeConfig.setValue(FastJsonSerializer.entityToJson(homePageDefaultValue));

        return employeeConfig;
    }

    private EmployeeConfig buildInnerHomePageDefault(int enterpriseId, int employeeId, int type, EmployeeConfig homePageDefaultConfig) {

        if (homePageDefaultConfig == null) {
            return getDefaultEmployeeConfig(enterpriseId, employeeId, type);
        } else {
            return homePageDefaultConfig;
        }
    }

    private EmployeeConfig buildCrossHomePageDefault(long outUserId, EmployeeConfig homePageDefaultConfig) {

        if (homePageDefaultConfig == null) {
            return getCrossDefaultEmployeeConfig(outUserId);
        } else {
            return updateCrossHomePageDefault(outUserId, homePageDefaultConfig);
        }

    }

    private EmployeeConfig updateCrossHomePageDefault(long outUserId, EmployeeConfig homePageDefaultConfig) {

        EmployeeConstant.HomePageDefaultValue homePageDefaultValue = FastJsonSerializer.jsonToEntity(EmployeeConstant.HomePageDefaultValue.class, homePageDefaultConfig.getValue());

        homePageDefaultValue.setEmpsAndDeps(getDefaultCrossEmpsAndDeps(outUserId));

        homePageDefaultConfig.setValue(FastJsonSerializer.entityToJson(homePageDefaultValue));

        return homePageDefaultConfig;
    }

    private List<EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem> getDefaultCrossEmpsAndDeps(long outUserId) {
        EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem homePageDefaultItem = new EmployeeConstant.HomePageDefaultValue.HomePageDefaultItem();
        homePageDefaultItem.setId(outUserId);
        homePageDefaultItem.setType(employeeType);
        return Lists.newArrayList(homePageDefaultItem);
    }
}
