package com.facishare.webpage.customer.migrate.dao.entity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/23 11:54 AM
 */
@Data
@Entity(value = "TenantWebPageMigrateEntity", noClassnameStored = true)
public class TenantWebPageMigrateEntity {
    @Id
    private ObjectId id;
    /**
     * 企业id
     */
    @Property("TId")
    private int tenantId;
    /**
     * 迁移的菜单id
     */
    @Property("WMId")
    private String webMenuId;
    /**
     * 迁移的首页id
     */
    @Property("WPId")
    private String webPageId;
    /**
     * 迁移后生成的视图模板id
     */
    @Property("WVId")
    private String webViewId;
    /**
     * 是否整合到菜单中
     */
    private boolean mergeToMenu;
    /**
     * 自定义菜单项相关数据
     */
    @Embedded("MPMDL")
    private List<MigratePageMenuData> migratePageMenuDataList;
    /**
     * 迁移状态
     */
    @Property("S")
    private int status;
    /**
     * 迁移人
     */
    @Property("MEId")
    private String migrateEmployeeId;
    /**
     * 迁移时间
     */
    @Property("MT")
    private long migrateTime;


}
