package com.facishare.webpage.customer.model.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.core.component.Component;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.ComponentTypConst;
import com.facishare.webpage.customer.core.util.DropListUtil;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Map;

/**
 * Created by zhangyu on 2020/8/26
 */
@Data
public class ComponentGrid extends Component {

    private ComponentDto componentDto;

    private Map<String, String> componentLanguage;

    @Override
    public String getId() {
        return componentDto.getId();
    }

    @Override
    public String getPId() {
        return componentDto.getParentId();
    }

    @Override
    public String getName() {
        return DropListUtil.getDropListName(componentDto, componentLanguage);
    }

    @Override
    public String getDropItemType() {
        return componentDto.getComponentType() == ComponentTypConst.WIDGET_TYPE ? ComponentConstant.dropListCompType : ComponentConstant.dropListGroupType;
    }

    @Override
    public String getApiName() {
        return componentDto.getWidget().getId();
    }

    @Override
    public String getType() {
        return "grid_row".equals(getApiName()) ? "grid_row" : "grid";
    }

    @Override
    public int getLimit() {
        return componentDto.getWidget().getLimit();
    }

    @Override
    public JSONObject getProps() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("api_name", getApiName());
        jsonObject.put("header", getName());
        jsonObject.put("showHeader", true);
        jsonObject.put("nameI18nKey", componentDto.getWidget().getNameI18nKey());
        jsonObject.put("ratioType", 0);
        jsonObject.put("widthRatios", Lists.newArrayList(1,1));
        jsonObject.put("components", Lists.newArrayList(Lists.newArrayList(), Lists.newArrayList()));
        if (componentDto.getWidget().getExtProp() != null){
            jsonObject.putAll(componentDto.getWidget().getExtProp());
        }

        if (componentDto.getWidget() != null) {
            jsonObject.putAll(componentDto.getWidget().getWidgetScope());
        }

        return jsonObject;
    }

    @Override
    public int getGrayLimit() {
        return 1;
    }
}
