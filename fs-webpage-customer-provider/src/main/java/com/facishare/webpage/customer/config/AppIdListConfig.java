package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.sysdb.config.LicenseSystemData;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/7/22
 */
@Component
public class AppIdListConfig {

    private static Logger logger = LoggerFactory.getLogger(AppIdListConfig.class);

    private String vendorAppId;
    private static Splitter splitter = Splitter.on(",");

    private Set<String> notAddMenuAppIds = new HashSet<>();
    private Set<String> allPresetAppIds = new HashSet<>();

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-user-extension-cross-appIdList-config", this::loadConfig);
        ConfigFactory.getConfig("fs-webpage-app-pagetemplate", this::loadPresetAppConfig);
    }

    private void loadConfig(IConfig config) {

        String vendorAppId = config.get("vendorAppId");
        this.vendorAppId = vendorAppId;
        String notAddMenuAppIdList = config.get("notAddMenuAppIdList", "");
        if (!Strings.isNullOrEmpty(notAddMenuAppIdList)) {
            notAddMenuAppIdList = notAddMenuAppIdList.trim();
            List<String> blackList = splitter.splitToList(notAddMenuAppIdList);
            notAddMenuAppIds = blackList.stream().map(s -> s.trim()).collect(Collectors.toSet());
        }
        logger.info("init AppIdListConfig config vendorId:{};", vendorAppId);
    }

    private void loadPresetAppConfig(IConfig config) {
        Set<String> allPresetAppIds = new HashSet<>();
        String configStr = config.getString();
        Map<String, Map<String, Map<String, List<String>>>> sysTemplateId = new HashMap();
        sysTemplateId = (Map) JSON.parseObject(configStr, Map.class);
        sysTemplateId.entrySet().forEach(x->{
            x.getValue().entrySet().forEach(y->{
                allPresetAppIds.addAll(y.getValue().keySet());
            });
        });
        this.allPresetAppIds = allPresetAppIds;

        logger.info("init allPresetAppIds {};", allPresetAppIds);
    }

    public String getVendorAppId() {
        return vendorAppId;
    }

    public boolean isNotAddMenuAppId(String appId) {
        return notAddMenuAppIds.contains(appId);
    }
    public boolean isPresetAppId(String appId) {
        return allPresetAppIds.contains(appId);
    }



    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        AppIdListConfig appIdListConfig = new AppIdListConfig();
        appIdListConfig.init();

        System.out.println(appIdListConfig.isPresetAppId("CRM"));
    }

}
