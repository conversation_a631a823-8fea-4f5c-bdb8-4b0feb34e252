package com.facishare.webpage.customer.controller.model.result.menu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/12/23
 */
@Data
public class GetCrossMenuResult implements Serializable {

    private List<CrossMenu> crossMenuList;

    @Data
    public static class CrossMenu implements Serializable {
        private String id;
        private String name;
        private String icon;
        private String webUrl;
    }

}
