package com.facishare.webpage.customer.migrate.action;

import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @date 2022/2/23 11:43 AM
 */
@Controller
@Slf4j
@RequestMapping("/migrate")
public class MigrateDataActionImpl implements MigrateDataAction {

    @Override
    @RequestMapping(value = "queryMigrateDataList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public QueryMigrateDataList.Result queryMigrateDataList(@FSUserInfo UserInfo userInfo,
                                                            @RequestBody QueryMigrateDataList.Arg arg) {
        return null;
    }

    @Override
    @RequestMapping(value = "saveMigrateDataList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SaveMigrateDataList.Result saveMigrateDataList(@FSUserInfo UserInfo userInfo,
                                                          @RequestBody SaveMigrateDataList.Arg arg) {
        return null;
    }
}
