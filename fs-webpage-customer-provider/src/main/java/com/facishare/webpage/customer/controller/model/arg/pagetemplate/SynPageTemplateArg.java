package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/19.
 */
@Data
public class SynPageTemplateArg extends BaseResult {
    /**
     * app模板对应的web模板的id
     */
    private String fromWebPagetemplateId;

    /**
     * 模板类型
     * 1：个人
     * 2、租户
     * 3、系统级
     */
    private int pageTemplateType;

    private Map<String, String> appCustomerPageMap = new HashMap<>();

    private String appId = Constant.APP_CRM;

    //同步类型 1：crm迁移时自动web同步移动端 2：管理后台web同步移动端  3：编辑web视图时重新同步至移动端
    private int synType = 2;
}
