package com.facishare.webpage.customer.controller.model.arg.portal;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.dao.entity.SiteDraftEntity;
import lombok.Data;

@Data
public class SiteDraftInfoDTO {
    private String id;
    private String siteApiName;
    private JSONObject draftData;
    private String description;
    private String appId;
    private String clientType = ClientType.web.getValue();

    public SiteDraftEntity toEntity() {
        SiteDraftEntity entity = new SiteDraftEntity();
        entity.setId(id);
        entity.setSiteApiName(siteApiName);
        entity.setDraftData(JSONObject.toJSONString(draftData));
        entity.setDescription(description);
        entity.setAppId(appId);
        entity.setClientType(clientType);
        return entity;
    }
}
