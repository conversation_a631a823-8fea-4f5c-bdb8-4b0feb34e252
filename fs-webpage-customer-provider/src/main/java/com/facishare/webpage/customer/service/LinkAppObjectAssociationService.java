package com.facishare.webpage.customer.service;

import com.facishare.enterprise.common.model.Pair;
import com.facishare.er.api.model.vo.AddRoleLayoutVO;
import com.facishare.er.api.model.vo.AddRoleRecordTypeVO;
import com.facishare.er.api.model.vo.AllocatedLayoutVO;
import com.facishare.webpage.customer.api.model.LinkAppObjectAssociationVO;
import com.facishare.webpage.customer.api.model.arg.DeleteLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.arg.GetLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.arg.UpdateLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.model.AllocatedRecordTypePojo;

import java.util.List;


public interface LinkAppObjectAssociationService {
    /**
     * 获取应用关联的业务对象列表
     */
    List<LinkAppObjectAssociationVO> listByTypeAndUpstreamAndAppId(Integer type, String upstream, String appId, boolean allAppIdFlag);

    Pair<Boolean, String> checkLinkAppAssociateObjectApiEnable(String upstreamEa, int userId, String appId, List<String> objectApiNames, String action);

    <T> void fillObjectDescribe(int fsEi, List<T> appAssociationObjects);

    void fillObjectMetadataForAssociations(int enterpriseId, List<LinkAppObjectAssociationVO> appAssociationObjects);

    /**
     * 添加应用关联的业务对象
     *
     * @return 返回添加后所有关联对象列表
     */
    List<LinkAppObjectAssociationVO> batchAddAssociationObjects(String upstream, String appId, List<String> objectApiNames, Boolean allowRemove);

    AllocatedRecordTypePojo listAllocateRecordTypes(Integer fsEi, String fsEa, Integer userId, String appId, String objectApiName, Integer appType);

    void allocateRecordType(Integer fsEi, String fsEa, Integer employeeId, String appId, String objectApiName, List<AddRoleRecordTypeVO> allocateList, boolean isManual);


    AllocatedLayoutVO listAllocatedLayoutByObject(String ea, String appId, String objectApiName, String layoutType);

    void allocateLayout(Integer fsEi, String appId, String objectApiName, List<AddRoleLayoutVO> allocateList, String layoutType);

    /**
     * 移除应用关联的业务对象
     *
     * @return 返回移除后所有关联对象列表
     */
    List<LinkAppObjectAssociationVO> deleteAssociationObject(String upstream, String appId, String objectApiName);

    List<LinkAppObjectAssociationVO> deleteAssociationObject(String upstream, String appId,
                                                             String objectApiName, Integer type);

    void insertLinkAppObjectAssociationList(List<LinkAppObjectAssociationVO> linkAppObjectAssociationVOS);

    List<LinkAppObjectAssociationVO> getLinkAppObjectAssociationList(GetLinkAppAssociationObjectListArg arg);

    void deleteLinkAppObjectAssociationList(DeleteLinkAppAssociationObjectListArg arg);

    void batchUpdateStatusByTypeAndUpstreamAndApiNames(UpdateLinkAppAssociationObjectListArg arg);
}
