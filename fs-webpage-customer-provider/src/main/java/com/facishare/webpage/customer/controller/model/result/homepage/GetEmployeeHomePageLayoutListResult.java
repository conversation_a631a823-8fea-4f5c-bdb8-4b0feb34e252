package com.facishare.webpage.customer.controller.model.result.homepage;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyu on 2019/10/28
 */
@Data
public class GetEmployeeHomePageLayoutListResult implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("HomePageLayoutList")
    private List<HomePageLayoutTO> homePageLayoutTOList;
    @JSONField(name = "M2")
    @SerializedName("enablePersonPageConfig")
    private boolean enablePersonPageConfig;
    @JSONField(name = "M3")
    @SerializedName("md5Version")
    private String md5Version;

}
