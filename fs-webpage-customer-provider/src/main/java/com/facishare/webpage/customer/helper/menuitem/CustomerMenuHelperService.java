package com.facishare.webpage.customer.helper.menuitem;

import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.CustomerMenu;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2021/7/15 2:16 下午
 */
public interface CustomerMenuHelperService {

    /**
     * 设置自定义菜单项的部分属性
     *
     * @param tenantId
     * @param customerMenu
     * @param locale
     */
    void setCustomerMenu(int tenantId, CustomerMenu customerMenu, Locale locale);

    /**
     * 设置自定义菜单项 content
     *
     * @param tenantId
     * @param customerMenuList
     * @param locale
     */
    void setCustomerMenuContent(int tenantId, List<CustomerMenu> customerMenuList, Locale locale);

    /**
     * covert to MetaMenuData
     *
     * @param tenantId
     * @param appId
     * @param locale
     * @param customerMenus
     * @return
     */
    List<MetaMenuData> covertMetaMenuDataList(int tenantId, String appId, Locale locale, List<CustomerMenu> customerMenus);

    /**
     * 对自定义菜单项进行权限过滤
     *
     * @param tenantId
     * @param appId
     * @param employeeId
     * @param outTenantId
     * @param outUid
     * @param outLinkType
     * @param customerMenus
     * @param locale
     * @return
     */
    List<CustomerMenu> filterCustomerMenuForPermission(int tenantId,
                                                       String appId,
                                                       Integer employeeId,
                                                       Long outTenantId,
                                                       Long outUid,
                                                       Integer outLinkType,
                                                       List<CustomerMenu> customerMenus,
                                                       Locale locale);

    /**
     * 自定义菜单项的类型
     *
     * @return
     */
    Integer getMenuType();

}
