package com.facishare.webpage.customer.permission;

import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.model.FilterArg;
import com.facishare.webpage.customer.model.FilterResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service("enterpriseSourcePermissionService")
public class EnterpriseSourcePermissionService implements PermissionService {

    @Override
    public FilterResult filterMenusWithPermission(FilterArg arg) {
        List<Menu> allowedMenus = Lists.newArrayList(arg.getMenus());
        List<Menu> checkMenus = allowedMenus.stream().filter(menu -> menu.getTenantPrivilege() != null
                && !CollectionUtils.isEmpty(menu.getTenantPrivilege().getNotSupportSource())).collect(Collectors.toList());
        allowedMenus.removeAll(checkMenus);

        List<Menu> remainMenus = checkMenus.stream().filter(menu -> !menu.getTenantPrivilege().getNotSupportSource()
                .contains(arg.getEnterpriseRegisterSource())).collect(Collectors.toList());
        allowedMenus.addAll(remainMenus);
        FilterResult result = new FilterResult();
        result.setMenus(allowedMenus);
        return result;
    }
}
