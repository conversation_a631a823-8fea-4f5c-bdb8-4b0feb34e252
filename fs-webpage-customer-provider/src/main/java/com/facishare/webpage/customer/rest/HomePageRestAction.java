package com.facishare.webpage.customer.rest;

import com.facishare.webpage.customer.rest.arg.GetEmployeeConfigValuesArg;
import com.facishare.webpage.customer.rest.arg.GetEmployeeHomePageListArg;
import com.facishare.webpage.customer.rest.arg.GetHomePageByIdArg;
import com.facishare.webpage.customer.rest.arg.GetUserCustomerLayoutRestArg;
import com.facishare.webpage.customer.rest.result.GetEmployeeConfigValuesResult;
import com.facishare.webpage.customer.rest.result.GetEmployeeHomePageListResult;
import com.facishare.webpage.customer.rest.result.GetHomePageByIdResult;
import com.facishare.webpage.customer.rest.result.GetUserCustomerLayoutRestResult;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.HeaderParam;
import java.util.Locale;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/8/14
 */
public interface HomePageRestAction {

    GetHomePageByIdResult getHomePageById(@HeaderParam("x-fs-ei") int tenantId, @HeaderParam("x-fs-locale") Locale locale, GetHomePageByIdArg arg);

    GetEmployeeConfigValuesResult getEmployeeConfigValues(@HeaderParam("x-fs-ei") int tenantId, GetEmployeeConfigValuesArg arg);

    GetEmployeeHomePageListResult getEmployeeHomePageList(@HeaderParam("x-fs-ei") int tenantId, @HeaderParam("x-fs-locale") Locale locale, GetEmployeeHomePageListArg arg);

    GetUserCustomerLayoutRestResult queryUserCustomerLayout(@HeaderParam("x-fs-ei") int tenantId, @HeaderParam("x-fs-locale") Locale locale, GetUserCustomerLayoutRestArg arg);

    void homePageLayoutAfterAction(@RequestBody GetHomePageByIdArg arg);

    }
