package com.facishare.webpage.customer.model;

import com.facishare.webpage.customer.core.model.Wheres;
import com.facishare.webpage.customer.core.util.DocumentBaseEntity;
import com.google.common.collect.Lists;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@NoArgsConstructor
public class ComponentData extends DocumentBaseEntity {


    private ComponentData(Map componentDataMap) {
        super(componentDataMap);
    }

    public static ComponentData of(Map componentDataMap) {
        return new ComponentData(componentDataMap);
    }

    public String getNewTitle() {
        return String.valueOf(get("newTitle"));
    }

    public List<Wheres> getWheres() {
        List<Map> wheres = (List<Map>) get("wheres");
        if (Objects.isNull(wheres)) {
            return Lists.newArrayList();
        }
        return wheres.stream().map(Wheres::of).collect(Collectors.toList());
    }

    public boolean isNoWrapper() {
        return (boolean) get("noWrapper");
    }

    public String getConditionAction() {
        return (String) get("conditionAction");
    }
}
