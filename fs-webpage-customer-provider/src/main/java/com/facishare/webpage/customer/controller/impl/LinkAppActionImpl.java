package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.api.constant.PaaSStatus;
import com.facishare.webpage.customer.controller.LinkAppAction;
import com.facishare.webpage.customer.event.WebPageEventService;
import com.facishare.webpage.customer.service.LinkAppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;


@Controller
@Slf4j
@RequestMapping({"/linkAppManager"})
public class LinkAppActionImpl implements LinkAppAction {

    @Autowired
    private LinkAppService linkAppService;
    @Resource
    private WebPageEventService webPageEventService;

    @Autowired
    private EIEAConverter eieaConverter;


    @Override
    @RequestMapping(value = {"/createLinkApp"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public CreateLinkAppResult createLinkApp(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody CreateLinkAppArg arg) {
        arg.valid();
        checkName(userInfo.getEnterpriseId(), null, arg.getName(), AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        int employeeId = userInfo.getEmployeeId();
        //先调用互联接口保存互联应用，并返回appId
        String appId = linkAppService.createLinkApp(userInfo.getEnterpriseAccount(), userInfo.getEnterpriseId(), employeeId, arg);
        webPageEventService.sendCreateCustomerLinkApp(userInfo, clientInfo);
        CreateLinkAppResult result = new CreateLinkAppResult();
        result.setAppId(appId);
        return result;
    }

    @Override
    @RequestMapping(value = {"/updateLinkApp"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public UpdateLinkAppResult updateLinkApp(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody UpdateLinkAppArg arg) {
        try {
            RequestContextManager.initContextForIsFromManage(true);
            arg.valid();
            checkName(userInfo.getEnterpriseId(), arg.getAppId(), arg.getName(), AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
            int employeeId = userInfo.getEmployeeId();
            linkAppService.updateLinkApp(userInfo.getEnterpriseAccount(), userInfo.getEnterpriseId(), employeeId, arg, clientInfo.getLocale());

            UpdateLinkAppResult result = new UpdateLinkAppResult();
            result.setSuccess(true);
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }

    private void checkName(int tenantId, String appId, String name, int appType) {
        PaaSAppVO paaSAppVO = linkAppService.queryLinkAppVOByName(tenantId, name, appType);
        if (paaSAppVO != null && !paaSAppVO.getAppId().equals(appId)) {
            throw new WebPageException(InterErrorCode.SAME_NAME);
        }
    }

    @Override
    @RequestMapping(value = {"/getLinkAppById"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public GetLinkAppByAppIdResult getLinkAppByAppId(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetLinkAppByAppIdArg arg) {
        try {
            arg.valid();
            RequestContextManager.initContextForIsFromManage(true);
            Integer tenantId = userInfo.getEnterpriseId();
            Integer employeeId = userInfo.getEmployeeId();
            if (StringUtils.isNotBlank(arg.getUpstreamEa())) {
                tenantId = eieaConverter.enterpriseAccountToId(arg.getUpstreamEa());
                employeeId = 1001;
            }
            PaaSAppVO paaSAppVO = linkAppService.getLinkAppByAppId(tenantId, arg.getAppId(), employeeId, clientInfo.getLocale());
            GetLinkAppByAppIdResult result = new GetLinkAppByAppIdResult();
            result.setLinkAppVO(paaSAppVO);
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }

    @Override
    @RequestMapping(value = {"/enableLinkApp"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public EnableLinkAppResult enableLinkApp(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody EnableLinkAppArg arg) {
        arg.valid();
        String appId = arg.getAppId();
        CheckLinkAppArg checkLinkAppArg = new CheckLinkAppArg();
        checkLinkAppArg.setAppId(appId);
        checkLinkApp(userInfo, clientInfo, checkLinkAppArg);
        linkAppService.updateStatus(userInfo.getEnterpriseAccount(), userInfo.getEnterpriseId(), userInfo.getEmployeeId(), appId, PaaSStatus.enable);
        EnableLinkAppResult result = new EnableLinkAppResult();
        result.setSuccess(true);
        return result;

    }

    @Override
    @RequestMapping(value = {"/disableLinkApp"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    // @ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public DisableLinkAppResult disableLinkApp(@FSUserInfo UserInfo userInfo, @RequestBody EnableLinkAppArg arg) {
        arg.valid();
        linkAppService.updateStatus(userInfo.getEnterpriseAccount(), userInfo.getEnterpriseId(), userInfo.getEmployeeId(), arg.getAppId(), PaaSStatus.disable);
        DisableLinkAppResult result = new DisableLinkAppResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    @RequestMapping(value = {"/deleteLinkApp"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public DeleteLinkAppResult deleteLinkApp(@FSUserInfo UserInfo userInfo, @RequestBody DeleteLinkAppArg arg) {
        arg.valid();
        int enterpriseId = userInfo.getEnterpriseId();
        String appId = arg.getAppId();
        linkAppService.deleteLinkApp(userInfo.getEnterpriseAccount(), enterpriseId, userInfo.getEmployeeId(), appId, PaaSStatus.delete);
        DeleteLinkAppResult result = new DeleteLinkAppResult();
        result.setSuccess(true);
        return result;
    }


    @Override
    @RequestMapping(value = {"/getLinkAppList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public GetLinkAppListResult getLinkAppList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo) {
        try {
            int enterpriseId = userInfo.getEnterpriseId();
            int employeeId = userInfo.getEmployeeId();
            RequestContextManager.initContextForIsFromManage(true);
            //boolean hasPermission = roleService.checkHasPermissionByCode(enterpriseId, employeeId, Link_APP_FUNCTION);
            //查询互联应用
            GetLinkAppListResult result = linkAppService.getLinkAppList(enterpriseId, userInfo.getEnterpriseAccount(), employeeId, clientInfo.getLocale());
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }


    @Override
    @RequestMapping(value = {"/getUpLinkAppList"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public GetUpLinkAppListResult getUpLinkAppList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo) {
        int enterpriseId = userInfo.getEnterpriseId();
        int employeeId = userInfo.getEmployeeId();
        //boolean hasPermission = roleService.checkHasPermissionByCode(enterpriseId, employeeId, Link_APP_FUNCTION);
        //查询互联应用
        GetUpLinkAppListResult result = linkAppService.getUpLinkAppList(enterpriseId, userInfo.getEnterpriseAccount(), employeeId, clientInfo.getLocale());
        return result;

    }

    @Override
    @RequestMapping(value = {"/checkLinkApp"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    //@ValidateFunctionPermission(functionCode = {Link_APP_FUNCTION})
    public CheckLinkAppResult checkLinkApp(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody CheckLinkAppArg arg) {
        arg.valid();
        CheckLinkAppResult result = new CheckLinkAppResult();
        boolean needEnable = linkAppService.checkLinkApp(userInfo.getEnterpriseId(), arg.getAppId());
        result.setNeedEnable(needEnable);
        return result;

    }
}
