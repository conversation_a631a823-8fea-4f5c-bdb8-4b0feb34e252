package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.config.MainChannelMenuIconConfig;
import com.facishare.webpage.customer.config.model.IconData;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.UtilityBarAction;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarForManageArg;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarForUserArg;
import com.facishare.webpage.customer.controller.model.arg.bar.GetUtilityBarIconArg;
import com.facishare.webpage.customer.controller.model.arg.bar.SaveUtilityBarForManageArg;
import com.facishare.webpage.customer.controller.model.result.bar.GetUtilityBarForManageResult;
import com.facishare.webpage.customer.controller.model.result.bar.GetUtilityBarForUserResult;
import com.facishare.webpage.customer.controller.model.result.bar.GetUtilityBarIconResult;
import com.facishare.webpage.customer.controller.model.result.bar.SaveUtilityBarForManageResult;
import com.facishare.webpage.customer.model.UtilityBarVO;
import com.facishare.webpage.customer.service.UtilityBarService;
import com.facishare.webpage.customer.util.UtilityBarUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 */
@Controller
@Slf4j
@RequestMapping("/UtilityBar")
public class UtilityBarActionImpl implements UtilityBarAction {

    @Resource
    private UtilityBarService utilityBarService;
    @Resource
    private MainChannelMenuIconConfig mainChannelMenuIconConfig;

    @Override
    @RequestMapping(value = "getUtilityBarForManage", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUtilityBarForManageResult getUtilityBarForManage(@FSUserInfo UserInfo userInfo,
                                                               @FSClientInfo ClientInfo clientInfo,
                                                               @RequestBody GetUtilityBarForManageArg arg) {
        try {
            RequestContextManager.initContextForIsFromManage(true);
            UtilityBarVO utilityBarVO = utilityBarService.queryUtilityBarForManager(
                    userInfo,
                    arg.getAppId(),
                    arg.getAppType(),
                    arg.getPageTemplateId(),
                    clientInfo.getLocale());
            GetUtilityBarForManageResult result = new GetUtilityBarForManageResult();
            result.setUtilityBar(utilityBarVO == null ? null : utilityBarVO.getUtilityBarLayout());
            return result;
        } finally {
            RequestContextManager.removeContext();
        }
    }

    @Override
    @RequestMapping(value = "getUtilityBarForUser", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUtilityBarForUserResult getUtilityBarForUser(@FSUserInfo UserInfo userInfo,
                                                           @FSOuterUserInfo OuterUserInfo outerUserInfo,
                                                           @FSClientInfo ClientInfo clientInfo,
                                                           @RequestBody GetUtilityBarForUserArg arg) {
        UtilityBarVO utilityBarVO = utilityBarService.queryUtilityBarForUser(
                userInfo,
                outerUserInfo,
                arg.getAppId(),
                arg.getAppType(),
                arg.getPageTemplateId(),
                clientInfo.getLocale());
        GetUtilityBarForUserResult result = new GetUtilityBarForUserResult();
        result.setUtilityBar(utilityBarVO == null ? null : utilityBarVO.getUtilityBarLayout());
        return result;
    }

    @Override
    @RequestMapping(value = "getUtilityBarIcon", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUtilityBarIconResult getUtilityBarIcon(@FSUserInfo UserInfo userInfo, @RequestBody GetUtilityBarIconArg arg) {
        BizType bizType = BizType.getBizTypeValue(arg.getAppType());
        String appId = bizType.getDefaultAppId();
        if (StringUtils.isEmpty(appId)) {
            appId = arg.getAppId();
        }
        List<IconData> iconDataList = mainChannelMenuIconConfig.queryIconDataListByAppId(appId, WebPageConstants.UTILITY_BAR);
        List<GetUtilityBarIconResult.UtilityBarIcon> utilityBarIconList = iconDataList.stream().map(x -> {
            GetUtilityBarIconResult.UtilityBarIcon utilityBarIcon = new GetUtilityBarIconResult.UtilityBarIcon();
            utilityBarIcon.setIconIndex(x.getIconIndex());
            utilityBarIcon.setIconAddress(x.getIcon());
            return utilityBarIcon;
        }).collect(Collectors.toList());
        GetUtilityBarIconResult result = new GetUtilityBarIconResult();
        result.setUtilityBarIconList(utilityBarIconList);
        return result;
    }

    @Override
    @RequestMapping(value = "saveUtilityBarForManage", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SaveUtilityBarForManageResult saveUtilityBarForManage(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody SaveUtilityBarForManageArg arg) {
        UtilityBarVO utilityBarVO = UtilityBarUtil.covertUtilityBarVO(arg.getAppType(), arg.getAppId(), arg.getPageTemplateId(), arg.getUtilityBar());
        utilityBarService.insertOrUpdateUtilityBar(userInfo.getEnterpriseId(), userInfo.getEmployeeId(), utilityBarVO, clientInfo);
        SaveUtilityBarForManageResult result = new SaveUtilityBarForManageResult();
        result.setSuccess(true);
        return result;
    }
}
