package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.constant.PaaSStatus;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.constant.PublishStatus;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import com.google.common.collect.Lists;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2024/11/5.
 */
@Setter
public class SiteEntityDaoImpl implements SiteEntityDao {

    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(SiteEntity.class, true);
    }

    @Override
    public List<SiteEntity> findByAppIdIncludeDisable(int tenantId, String appId) {
        return findByAppId(tenantId, appId, Collections.emptyList());
    }

    @Override
    public List<SiteEntity> findByAppId(int tenantId, String appId) {
        return findByAppId(tenantId, appId, Lists.newArrayList(PaaSStatus.enable));
    }

    private List<SiteEntity> findByAppId(int tenantId, String appId, List<Integer> statusList) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        if (CollectionUtils.isNotEmpty(statusList)) {
            query.field("status").in(statusList);
        }
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(appId);
        query.order("-updateTime");
        return query.asList();
    }

    @Override
    public SiteEntity findByIdIncludeDisable(int tenantId, String id) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        query.field("tenantId").equal(tenantId);
        query.field("_id").equal(id);
        return query.get();
    }

    @Override
    public SiteEntity findByApiNameIncludeDisable(int tenantId, String apiName) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);
        return query.get();
    }

    @Override
    public SiteEntity findByApiName(int tenantId, String apiName) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("status").equal(PaaSStatus.enable);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").equal(apiName);
        return query.get();
    }

    @Override
    public SiteEntity findBySiteIdIncludeDisable(int tenantId, String siteId) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        query.field("tenantId").equal(tenantId);
        query.field("siteId").equal(siteId);
        return query.get();
    }

    @Override
    public SiteEntity findBySiteId(int tenantId, String siteId) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("status").equal(PaaSStatus.enable);
        query.field("tenantId").equal(tenantId);
        query.field("siteId").equal(siteId);
        return query.get();
    }

    @Override
    public SiteEntity save(User user, SiteEntity entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setId(ObjectId.get().toHexString());
        }
        entity.setTenantId(user.getTenantId());
        entity.setCreatorId(user.getUserId());
        entity.setUpdaterId(user.getUserId());
        long now = System.currentTimeMillis();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        if (Objects.isNull(entity.getNeedLogin())) {
            entity.setNeedLogin(false);
        }
        if (Objects.isNull(entity.getStatus())) {
            entity.setStatus(PaaSStatus.enable);
        }
        if (StringUtils.isEmpty(entity.getSourceType())) {
            entity.setSourceType(SourceType.CUSTOMER);
        }
        datastore.save(entity);
        return entity;
    }

    @Override
    public SiteEntity update(User user, SiteEntity entity) {
        if (StringUtils.isBlank(entity.getId()) && StringUtils.isBlank(entity.getApiName())) {
            return entity;
        }
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        if (StringUtils.isNotBlank(entity.getId())) {
            query.field("_id").equal(entity.getId());
        } else {
            query.field("apiName").equal(entity.getApiName());
        }
        UpdateOperations<SiteEntity> updateOperations = datastore.createUpdateOperations(SiteEntity.class);
        if (Objects.nonNull(entity.getName())) {
            updateOperations.set("name", entity.getName());
        }
        if (Objects.nonNull(entity.getDescription())) {
            updateOperations.set("description", entity.getDescription());
        }
        if (Objects.nonNull(entity.getNeedLogin())) {
            updateOperations.set("needLogin", entity.getNeedLogin());
        }
        if (Objects.nonNull(entity.getScopeListForCross())) {
            updateOperations.set("scopeListForCross", entity.getScopeListForCross());
        }
        if (Objects.nonNull(entity.getLangList())) {
            updateOperations.set("langList", entity.getLangList());
        }
        updateOperations.set("updateTime", System.currentTimeMillis());
        updateOperations.set("updaterId", user.getUserId());
        return datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public void updateStatusByApiName(User user, String apiName, int status) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);
        query.field("status").notEqual(status);
        UpdateOperations<SiteEntity> updateOperations = datastore.createUpdateOperations(SiteEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updateTime", System.currentTimeMillis());
        updateOperations.set("updaterId", user.getUserId());
        if (status == PaaSStatus.delete) {
            updateOperations.set("deleteId", ObjectId.get().toString());
        }
        datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public void publishSite(User user, String apiName, long version, String clientType) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);
        UpdateOperations<SiteEntity> updateOperations = datastore.createUpdateOperations(SiteEntity.class);
        clientType = StringUtils.firstNonBlank(clientType, ClientType.web.getValue());
        if(ClientType.web.same(clientType)){
            updateOperations.set("publishStatus", PublishStatus.PUBLISHED.getValue());
            updateOperations.set("publishVersion", version);
            updateOperations.set("publisherId", user.getUserId());
            updateOperations.set("publishTime", System.currentTimeMillis());
        } else {
            updateOperations.set("appPublishStatus", PublishStatus.PUBLISHED.getValue());
            updateOperations.set("appPublishVersion", version);
            updateOperations.set("appPublisherId", user.getUserId());
            updateOperations.set("appPublishTime", System.currentTimeMillis());
        }
        datastore.findAndModify(query, updateOperations, false);
    }

    @Override
    public void modifySiteAfterPublish(User user, String apiName, String clientType) {
        Query<SiteEntity> query = datastore.createQuery(SiteEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("apiName").equal(apiName);
        UpdateOperations<SiteEntity> updateOperations = datastore.createUpdateOperations(SiteEntity.class);
        clientType = StringUtils.firstNonBlank(clientType, ClientType.web.getValue());
        if(ClientType.web.same(clientType)){
            updateOperations.set("publishStatus", PublishStatus.MODIFIED_AFTER_PUBLISH.getValue());
        } else {
            updateOperations.set("appPublishStatus", PublishStatus.MODIFIED_AFTER_PUBLISH.getValue());
        }
        datastore.findAndModify(query, updateOperations, false);
    }
}
