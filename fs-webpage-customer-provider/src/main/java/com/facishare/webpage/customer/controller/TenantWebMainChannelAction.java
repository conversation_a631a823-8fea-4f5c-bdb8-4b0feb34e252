package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.result.GetTenantBrandColorResult;
import com.facishare.webpage.customer.controller.model.arg.paas.*;
import com.facishare.webpage.customer.controller.model.result.paas.*;

/**
 * Created by zhangyu on 2020/11/13
 */
public interface TenantWebMainChannelAction {
    /**
     * 获取主导航列表
     *
     * @param userInfo   身份信息
     * @param clientInfo 端的信息
     * @param arg        入参
     * @return
     */
    GetManMainChannelListResult getManWebMainChannelList(UserInfo userInfo,
                                                         ClientInfo clientInfo,
                                                         GetManMainChannelListArg arg);

    /**
     * 保存主导航数据
     *
     * @param userInfo 身份信息
     * @param arg      入参
     * @return
     */
    SaveTenantMainChannelResult saveManWebMainChannelMenu(UserInfo userInfo,
                                                          ClientInfo clientInfo,
                                                          SaveTenantMainChannelArg arg);

    /**
     * 根据apiName获取主导航数据
     *
     * @param userInfo   身份信息
     * @param clientInfo 端的信息
     * @param arg        入参
     * @return
     */
    GetManMainChannelByApiNameResult getManMainChannelByApiName(UserInfo userInfo,
                                                                ClientInfo clientInfo,
                                                                GetManMainChannelByApiNameArg arg);

    /**
     * 删除主导航
     *
     * @param userInfo 身份信息
     * @param arg      入参
     * @return
     */
    DeleteWebMainChannelResult deleteWebMainChannel(UserInfo userInfo,
                                                    DeleteWebMainChannelArg arg);

    /**
     * 恢复出厂设置
     *
     * @param userInfo   身份信息
     * @param clientInfo 端的信息
     * @return
     */
    ResetWebMainChannelResult resetWebMainChannel(UserInfo userInfo,
                                                  ClientInfo clientInfo);

    /**
     * 获取企业内所有应用菜单
     *
     * @param userInfo   身份信息
     * @param clientInfo 端的信息
     * @return
     */
    GetManMainChannelMenusResult getManMainChannelMenus(UserInfo userInfo,
                                                        ClientInfo clientInfo);

    /**
     * 设置主题色配置
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    SetTenantBrandColorResult setTenantBrandColor(UserInfo userInfo,
                                                  ClientInfo clientInfo, SetTenantBrandColorArg arg);

    /**
     * 获取主题色配置
     * @param userInfo
     * @param outerUserInfo
     * @param clientInfo
     * @return
     */
    GetTenantBrandColorResult getTenantBrandColor(UserInfo userInfo,
                                                  OuterUserInfo outerUserInfo,
                                                  ClientInfo clientInfo);

    /**
     * 编辑保存企业全局设置
     * @param userInfo
     * @param arg
     * @return
     */
    SaveGlobalSettingResult createOrUpdateGlobalSetting(UserInfo userInfo,
                                                        SaveGlobalSettingArg arg);

    /**
     * 获取企业全局设置
     * @param userInfo
     * @param clientInfo
     * @return
     */
    GetGlobalSettingResult getGlobalSetting(UserInfo userInfo,
                                            ClientInfo clientInfo,
                                            GetGlobalSettingArg arg);


}
