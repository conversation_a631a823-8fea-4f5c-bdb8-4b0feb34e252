package com.facishare.webpage.customer.metadata;

import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppListArg;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.constant.AppGeneralField;
import com.facishare.webpage.customer.metadata.model.AppComponentData;
import com.facishare.webpage.customer.metadata.model.WebMainChannelMetaData;
import com.facishare.webpage.customer.service.PaaSAppService;
import com.facishare.webpage.customer.util.PaaSAppUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/11/16
 */
@Service("crossWebAppService")
public class CrossWebAppService implements WebAppService {
    @Resource
    private ApplicationService applicationService;
    @Resource
    private LanguageService languageService;
    @Autowired
    private PaaSAppService paaSAppService;

    @Override
    public List<WebMainChannelMetaData> getTenantMainChannelMetaDataList(int tenantId, String enterpriseAccount, int employeeId, Locale locale) {
        List<AppComponentData> componentDataForManager = applicationService.getAppComponentDataForManager(DataSourceEnv.CROSS, tenantId, locale);
        if (CollectionUtils.isEmpty(componentDataForManager)) {
            return Lists.newArrayList();
        }


        return PaaSAppUtil.buildMainChannelMenuVOList(componentDataForManager, getCrossName(tenantId, locale));
    }

    @Override
    public List<WebMainChannelMetaData> getUserMainChannelMetaDataList(int tenantId, String enterpriseAccount, int employeeId, Locale locale, SlowLog stopWatch) {

        List<AppComponentData> appComponentDataForUser = applicationService.getAppComponentDataForUser(DataSourceEnv.CROSS, tenantId, employeeId, locale);
        stopWatch.lap("over cross getAppComponentDataForUser");
        if (CollectionUtils.isEmpty(appComponentDataForUser)) {
            return Lists.newArrayList();
        }
        return PaaSAppUtil.buildMainChannelMenuVOList(appComponentDataForUser, getCrossName(tenantId, locale));
    }

    public String getCrossName(int tenantId, Locale locale) {
        String crossNameI18nKey = AppGeneralField.CROSS_NAME.getI18nKey();
        Map<String, String> languageMap = languageService.queryLanguageByNameI18nKeys(tenantId, Lists.newArrayList(crossNameI18nKey), locale);
        return languageMap.getOrDefault(crossNameI18nKey, AppGeneralField.CROSS_NAME.getName());
    }
}
