package com.facishare.webpage.customer.service.impl;

import com.facishare.qixin.enterpriserelation.model.SyncEmployeeIdsEvent;
import com.facishare.qixin.enterpriserelation.service.SyncEmployeeIdsService;
import com.facishare.webpage.customer.service.MainChannelInnerService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("syncEmployeeIdsService")
public class SyncEmployeeIdsServiceImpl implements SyncEmployeeIdsService {

    @Resource
    private MainChannelInnerService mainChannelInnerService;

    @Override
    public void syncEmployeeIds(List<SyncEmployeeIdsEvent> list) {
        list.stream().forEach(syncEmployeeIdsEvent -> {
            mainChannelInnerService.createOrUpdateMainChannelEntity(syncEmployeeIdsEvent);
        });
    }


    @Override
    public void deleteEmployeeIds(List<SyncEmployeeIdsEvent> list) {
        list.stream().forEach(syncEmployeeIdsEvent -> {
            mainChannelInnerService.removeEmployeeMainChannelEntity(syncEmployeeIdsEvent);
        });
    }
}
