package com.facishare.webpage.customer.util;

import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.google.common.base.Strings;
import org.apache.commons.lang.StringUtils;

import java.util.UUID;

/**
 * Created by zhangyu on 2019/9/14
 */
public class TempleIdUtil {

    public static final String SEPARATOR = "_";
    public static final String SEPARATOR_OTHER = "-";

    public static final String buildId(int tenantId) {
        return String.join(SEPARATOR, String.valueOf(tenantId), getGuid());
    }

    public static final String generateId(int tenantId, String id, String contactCharacter) {

        String replaceId = replaceId(tenantId, id, contactCharacter);

        if (id.equals(replaceId)) {
            return String.join(contact<PERSON>haracter, String.valueOf(tenantId), id);
        }
        return replaceId;
    }

    public static final String replaceId(int tenantId, String id, String contactCharacter) {
        if (Strings.isNullOrEmpty(id)) {
            return null;
        }
        String[] split = id.split(contactCharacter);
        if (split.length <= 1) {
            return id;
        }
        return id.replaceFirst(split[0], String.valueOf(tenantId));
    }

    public static String getGuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String getObjectApiName(int appType, String appId) {
        if (BizType.CUSTOMER.getType() != appType) {
            return null;
        }

        String[] split = appId.split(ComponentConstant.SEPARATOR);
        String apiName = appId.replaceFirst(split[0] + ComponentConstant.SEPARATOR, "");
        return apiName;
    }

    public static String getPrefixByAppId(int appType, String appId) {
        if (BizType.CUSTOMER.getType() != appType) {
            return null;
        }
        String[] split = appId.split(ComponentConstant.SEPARATOR);
        return split[0];
    }

    public static String getCustomerAppId(int appType, String appId) {
        if (BizType.CUSTOMER.getType() != appType) {
            return appId;
        }
        if (appType == BizType.CROSSCUSTOMER.getType() && StringUtils.isNotEmpty(appId)) {
            if (appId.equals(DefaultTenantConfig.getQudaomenhuAppId())) {
                return BizType.CROSSCUSTOMER.getDefaultAppId();
            }
        }
        String[] split = appId.split(ComponentConstant.SEPARATOR);
        return split[0];
    }

    public static String removeTenantIdById(int tenantId, int appType, String id) {
        if (BizType.CUSTOMER.getType() == appType) {
            return id;
        }
        return TranslateI18nUtils.removeTenantIdById(tenantId, id);
    }
}
