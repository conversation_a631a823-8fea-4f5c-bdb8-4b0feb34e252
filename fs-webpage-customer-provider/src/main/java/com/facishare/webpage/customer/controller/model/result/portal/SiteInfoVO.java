package com.facishare.webpage.customer.controller.model.result.portal;

import com.facishare.webpage.customer.api.model.ScopeForCross;
import com.facishare.webpage.customer.api.model.SiteLangDTO;
import com.facishare.webpage.customer.dao.entity.SiteEntity;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2024/11/4.
 */
@Data
public class SiteInfoVO {
    private String id;
    private String apiName;
    private String name;
    private String description;
    private String siteId;
    private Boolean needLogin;
    private String appId;
    private Integer status;
    private Integer creatorId;
    private Long createTime;
    private Integer updaterId;
    private Long updateTime;

    private Integer publishStatus;
    private Long publishTime;
    private Integer publisherId;
    private Long publishVersion;

    private Integer appPublishStatus;
    private Long appPublishTime;
    private Integer appPublisherId;
    private Long appPublishVersion;

    private List<ScopeForCross> scopeListForCross;

    /**
     * 站点语言配置列表
     */
    private List<SiteLangDTO> langList;

    public static SiteInfoVO of(SiteEntity entity) {
        SiteInfoVO vo = new SiteInfoVO();
        vo.setId(entity.getId());
        vo.setApiName(entity.getApiName());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setSiteId(entity.getSiteId());
        vo.setNeedLogin(entity.getNeedLogin());
        vo.setScopeListForCross(entity.getScopeListForCross());
        vo.setAppId(entity.getAppId());
        vo.setStatus(entity.getStatus());
        vo.setCreatorId(entity.getCreatorId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdaterId(entity.getUpdaterId());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setPublishStatus(entity.getPublishStatus());
        vo.setPublishTime(entity.getPublishTime());
        vo.setPublisherId(entity.getPublisherId());
        vo.setPublishVersion(entity.getPublishVersion());
        vo.setAppPublishStatus(entity.getAppPublishStatus());
        vo.setAppPublishTime(entity.getAppPublishTime());
        vo.setAppPublisherId(entity.getAppPublisherId());
        vo.setAppPublishVersion(entity.getAppPublishVersion());

        // 转换语言配置
        if (CollectionUtils.isNotEmpty(entity.getLangList())) {
            List<SiteLangDTO> langDTOList = entity.getLangList().stream()
                    .map(siteLang -> siteLang.toDTO())
                    .collect(Collectors.toList());
            vo.setLangList(langDTOList);
        }

        return vo;
    }
}
