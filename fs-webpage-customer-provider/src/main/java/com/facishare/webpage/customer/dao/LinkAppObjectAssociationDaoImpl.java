package com.facishare.webpage.customer.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.LinkAppObjectAssociationType;
import com.facishare.webpage.customer.api.model.arg.DeleteLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.arg.GetLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.utils.BizLogUtils;
import com.facishare.webpage.customer.dao.entity.LinkAppObjectAssociationEntity;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangyu on 2019/10/29
 */
@Slf4j
public class LinkAppObjectAssociationDaoImpl implements LinkAppObjectAssociationDao {

    @Resource
    private Datastore datastore;

    /**
     * @Description: 根据 Ea和 appId 查询绑定的业务对象, 没有delete标志, 删除就是物理删
     * @see: {@link com.facishare.webpage.customer.api.constant.LinkAppObjectAssociationType}
     *
     */
    @Override
    public List<LinkAppObjectAssociationEntity> findByEaAndAppId(String upstreamEa, String appId){
        if(StringUtils.isBlank(upstreamEa) || StringUtils.isBlank(appId)){
            return Lists.newArrayList();
        }
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("linkAppId").equal(appId);
        query.or(
                query.criteria("upstreamEa").equal(upstreamEa),
                query.criteria("upstreamEa").doesNotExist(),
                query.criteria("upstreamEa").equal(null)
        );
        return query.asList();
    }

    @Override
    public List<LinkAppObjectAssociationEntity> listByTypeAndUpstreamAndAppId(int type, String upstream, String appId, String objectApiName, boolean allAppIdFlag) {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("type").equal(type);
        if (type == LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE && StringUtils.isBlank(upstream)) {
            return new ArrayList<>();
        }
        if (type == LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE && StringUtils.isNotBlank(upstream)) {
            query.field("upstreamEa").equal(upstream);
        }
        if (!allAppIdFlag) {
            query.field("linkAppId").equal(appId);
            if (StringUtils.isNotBlank(objectApiName)) {
                query.field("objectApiName").equal(objectApiName);
            }
        }

        return query.asList();
    }

    @Override
    public void insert(LinkAppObjectAssociationEntity linkAppObjectAssociationEntity) {
        datastore.save(linkAppObjectAssociationEntity);
    }

    @Override
    public void findAndModify(LinkAppObjectAssociationEntity linkAppObjectAssociationEntity) {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("type").equal(linkAppObjectAssociationEntity.getType());
        query.field("linkAppId").equal(linkAppObjectAssociationEntity.getLinkAppId());
        query.field("objectApiName").equal(linkAppObjectAssociationEntity.getObjectApiName());
        if (StringUtils.isNotBlank(linkAppObjectAssociationEntity.getUpstreamEa())) {
            query.field("upstreamEa").equal(linkAppObjectAssociationEntity.getUpstreamEa());
        }
        UpdateOperations<LinkAppObjectAssociationEntity> updateOperations = datastore.createUpdateOperations(LinkAppObjectAssociationEntity.class);
        updateOperations.set("type", linkAppObjectAssociationEntity.getType());
        if (StringUtils.isNotBlank(linkAppObjectAssociationEntity.getUpstreamEa())) {
            updateOperations.set("upstreamEa", linkAppObjectAssociationEntity.getUpstreamEa());
        }
        updateOperations.set("linkAppId", linkAppObjectAssociationEntity.getLinkAppId());
        updateOperations.set("objectApiName", linkAppObjectAssociationEntity.getObjectApiName());
        updateOperations.set("objectStatus", linkAppObjectAssociationEntity.getObjectStatus());
        updateOperations.set("createTime", linkAppObjectAssociationEntity.getCreateTime());
        updateOperations.set("updateTime", linkAppObjectAssociationEntity.getUpdateTime());
        updateOperations.set("allowRemove", linkAppObjectAssociationEntity.getAllowRemove());
        updateOperations.set("notRecordTypeNames", linkAppObjectAssociationEntity.getNotRecordTypeNames());
        updateOperations.set("notLayoutNames", linkAppObjectAssociationEntity.getNotLayoutNames());
        updateOperations.set("needAllocate", null == linkAppObjectAssociationEntity.getNeedAllocate() ? true : linkAppObjectAssociationEntity.getNeedAllocate());
        datastore.findAndModify(query, updateOperations, false, true);
    }

    @Override
    public void deleteByUpstreamAndAppId(int type, String upstream, String appId, String objectApiName) {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("type").equal(type);
        query.field("upstreamEa").equal(upstream);
        query.field("linkAppId").equal(appId);
        query.field("objectApiName").equal(objectApiName);
        log.info("deleteByUpstreamAndAppId type={} ,upstream={} ,appId={} ,objectApiName={} , query={}", type, upstream, appId, objectApiName, query.toString());
        AuditLogDTO.AuditLogDTOBuilder builder = AuditLogDTO.builder().action("deleteByUpstreamAndAppId").extra(query.toString()).message(type + "-" + upstream + "-" + appId + "-" + objectApiName);
        BizLogUtils.sendBizLog(builder);
        datastore.delete(query);
    }

    @Override
    public void batchInsert(List<LinkAppObjectAssociationEntity> entities) {
        datastore.save(entities);
    }

    @Override
    public List<LinkAppObjectAssociationEntity> getLinkAppObjectAssociationList(GetLinkAppAssociationObjectListArg arg) {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("type").equal(arg.getType());
        if (StringUtils.isNotBlank(arg.getUpstreamEa())) {
            query.field("upstreamEa").equal(arg.getUpstreamEa());
        }
        if (StringUtils.isNotBlank(arg.getAppId())) {
            query.field("linkAppId").equal(arg.getAppId());
        }
        if (CollectionUtils.isNotEmpty(arg.getAppIds())) {
            query.field("linkAppId").in(arg.getAppIds());
        }
        if (CollectionUtils.isNotEmpty(arg.getObjectApiNames())) {
            query.field("objectApiName").in(arg.getObjectApiNames());
        }
        if (StringUtils.isNotBlank(arg.getObjectApiName())) {
            query.field("objectApiName").equal(arg.getObjectApiName());
        }
        return query.asList();
    }

    @Override
    public void deleteLinkAppObjectAssociationList(DeleteLinkAppAssociationObjectListArg arg) {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("type").equal(arg.getType());
        if (StringUtils.isNotBlank(arg.getUpstreamEa())) {
            query.field("upstreamEa").equal(arg.getUpstreamEa());
        }
        if (StringUtils.isNotBlank(arg.getAppId())) {
            query.field("linkAppId").equal(arg.getAppId());
        }
        if (CollectionUtils.isNotEmpty(arg.getAppIds())) {
            query.field("linkAppId").in(arg.getAppIds());
        }
        if (CollectionUtils.isNotEmpty(arg.getObjectApiNames())) {
            query.field("objectApiName").in(arg.getObjectApiNames());
        }
        if (StringUtils.isNotBlank(arg.getObjectApiName())) {
            query.field("objectApiName").equal(arg.getObjectApiName());
        }
        log.info("deleteLinkAppObjectAssociationList arg={} query={}", JSONObject.toJSONString(arg), query.toString());
        AuditLogDTO.AuditLogDTOBuilder builder = AuditLogDTO.builder().action("deleteLinkAppObjectAssociationList").extra(query.toString()).message(JSONObject.toJSONString(arg));
        BizLogUtils.sendBizLog(builder);
        datastore.delete(query);
    }

    @Override
    public void batchUpdateStatusByTypeAndUpstreamAndApiNames(int objectStatus, Integer type, String upstreamEa, List<String> objectApiNames) {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("type").equal(type);
        query.field("upstreamEa").equal(upstreamEa);
        query.field("objectApiName").in(objectApiNames);
        UpdateOperations<LinkAppObjectAssociationEntity> updateOperations = datastore.createUpdateOperations(LinkAppObjectAssociationEntity.class);
        updateOperations.set("objectStatus", objectStatus);
        datastore.update(query, updateOperations, false);
    }

    @Override
    public void removeAllObject() {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        datastore.delete(query);
    }

    @Override
    public void removeNullObject() {
        Query<LinkAppObjectAssociationEntity> query = datastore.createQuery(LinkAppObjectAssociationEntity.class);
        query.field("notLayoutNames").equal(null);
        datastore.delete(query);
    }
}
