package com.facishare.webpage.customer.config.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON>hangyu on 2020/6/10
 */
@Data
public class AddMenuItemVo implements Serializable, Cloneable{

    private String apiName;

    private String appId = "CRM";

    private String afterApiName;

    private boolean hiddenManage = false;

    /**
     * 顶部菜单的设置；这个是唯一的
     */
    private boolean topMenu = false;

    @Override
    public AddMenuItemVo clone() {
        AddMenuItemVo addMenuItemVo = new AddMenuItemVo();
        addMenuItemVo.setAppId(this.appId);
        addMenuItemVo.setTopMenu(this.topMenu);
        addMenuItemVo.setApiName(this.apiName);
        addMenuItemVo.setAfterApiName(this.afterApiName);
        addMenuItemVo.setHiddenManage(this.hiddenManage);
        return addMenuItemVo;
    }

}
