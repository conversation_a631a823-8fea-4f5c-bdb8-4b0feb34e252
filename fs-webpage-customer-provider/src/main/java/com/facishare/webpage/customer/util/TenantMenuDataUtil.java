package com.facishare.webpage.customer.util;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.model.MobileConfig;
import com.facishare.webpage.customer.api.model.UserMenuItem;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.model.AddMenuItemVo;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.controller.model.DefaultMenuVO;
import com.facishare.webpage.customer.controller.model.TenantMenuItemVO;
import com.facishare.webpage.customer.controller.model.TenantMenuSimpleDataVo;
import com.facishare.webpage.customer.dao.entity.MenuDataEntity;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.MenuData;
import com.facishare.webpage.customer.model.MenuItem;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhangyi on 2019/9/16.
 */
public class TenantMenuDataUtil {

    public static List<DefaultMenuVO> buildDefaultMenuVos(List<MetaMenuData> metaMenuDataList, Map<String, List<String>> searchWordsMap) {
        if (CollectionUtils.isEmpty(metaMenuDataList)) {
            return Lists.newArrayList();
        }
        return metaMenuDataList.stream().map(x -> buildDefaultMenuVo(x, searchWordsMap.getOrDefault(x.getName(), Lists.newArrayList()))).filter(x -> x != null).collect(Collectors.toList());
    }

    private static DefaultMenuVO buildDefaultMenuVo(MetaMenuData metaMenuData, List<String> searchWords) {
        if (metaMenuData == null) {
            return null;
        }

        DefaultMenuVO vo = new DefaultMenuVO();
        {
            vo.setApiName(metaMenuData.getApiName());
            vo.setDisplayName(metaMenuData.getName());
            vo.setIconIndex(metaMenuData.getIconIndex());
            vo.setIsActive(metaMenuData.isActive());
            vo.setSearchWords(searchWords);
        }
        return vo;
    }

    public static List<MenuItem> buildMenuItemFromVos(String appId, List<TenantMenuSimpleDataVo> voList, List<MetaMenuData> metaMenuDataList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Lists.newArrayList();
        }
        List<MenuItem> userMenuDataToList = Lists.newArrayList();
        voList.forEach(x -> {
            MenuItem menuItem = buildMenuItemFromVo(appId, x, metaMenuDataList);
            userMenuDataToList.add(menuItem);
            if (x.getType().equals(MenuType.GROUP) && CollectionUtils.isNotEmpty(x.getChildren())) {
                userMenuDataToList.addAll(buildChildMenuItemFromVos(appId, x.getChildren(), metaMenuDataList, menuItem.getApiName()));
            }
        });
        return userMenuDataToList.stream().filter(x -> x != null).collect(Collectors.toList());
    }

    private static List<MenuItem> buildChildMenuItemFromVos(String appId, List<TenantMenuSimpleDataVo> voList, List<MetaMenuData> metaMenuDataList, String apiName) {
        if (CollectionUtils.isEmpty(voList)) {
            return Lists.newArrayList();
        }
        List<MenuItem> tenantMenuDataToList = Lists.newArrayList();
        voList.forEach(x -> {
            MenuItem menuItem = buildChildMenuItemFromVo(appId, x, metaMenuDataList, apiName);
            tenantMenuDataToList.add(menuItem);
            if (x.getType().equals(MenuType.GROUP) && CollectionUtils.isNotEmpty(x.getChildren())) {
                tenantMenuDataToList.addAll(buildChildMenuItemFromVos(appId, x.getChildren(), metaMenuDataList, menuItem.getApiName()));
            }
        });
        return tenantMenuDataToList.stream().filter(x -> x != null).collect(Collectors.toList());
    }

    private static MenuItem buildChildMenuItemFromVo(String appId, TenantMenuSimpleDataVo vo, List<MetaMenuData> metaMenuDataList, String groupApiName) {
        if (Objects.isNull(vo)) {
            return null;
        }
        Map<String, MetaMenuData> menuItemMap = MenuUtil.getMetaMenuDataMap(metaMenuDataList);
        String apiName = vo.getApiName();

        MenuData menuData = new MenuData();
        if (StringUtils.equals(vo.getType(), MenuType.GROUP)) {
            if (StringUtils.isEmpty(apiName)) {
                apiName = vo.getType() + "-" + TempleIdUtil.getGuid();
            }
            menuData.setType(MenuType.GROUP);
        } else {
            if (StringUtils.isEmpty(apiName)) {
                apiName = vo.getMenuItemId();
            }
            if (StringUtils.isNotEmpty(vo.getAppId())) {
                appId = vo.getAppId();
            }

            MetaMenuData metaMenuData = menuItemMap.get(MenuUtil.getUniqueKey(appId, apiName));

            if (metaMenuData == null) {
                return null;
            }

            menuData.setType(metaMenuData.getMenuType());
        }

        menuData.setApiName(apiName);
        menuData.setOrderNumber(vo.getNumber());
        menuData.setIsHidden(vo.getIsHidden());
        menuData.setName(vo.getDisplayName());
        if (StringUtils.isNotEmpty(groupApiName)) {
            menuData.setGroupApiName(groupApiName);
        }
        return MenuUtil.buildMenuItem(menuItemMap.get(MenuUtil.getUniqueKey(appId, apiName)), menuData);
    }

    private static MenuItem buildMenuItemFromVo(String appId, TenantMenuSimpleDataVo vo, List<MetaMenuData> metaMenuDataList) {
        return buildChildMenuItemFromVo(appId, vo, metaMenuDataList, null);
    }


    public static List<MenuDataEntity> buildMenuDataEntityFromMenuItems(List<MenuItem> menuItems) {
        if (CollectionUtils.isEmpty(menuItems)) {
            return Lists.newArrayList();
        }
        return menuItems.stream().map(x -> buildMenuDataEntityFromMenuItem(x)).collect(Collectors.toList());
    }

    private static MenuDataEntity buildMenuDataEntityFromMenuItem(MenuItem menuItem) {
        MenuDataEntity entity = new MenuDataEntity();
        entity.setIsHidden(menuItem.getMenuData().getIsHidden());
        entity.setType(menuItem.getMenuItemType());
        entity.setOrderNumber(menuItem.getMenuData().getOrderNumber());
        entity.setName(menuItem.getMenuData().getName());
        entity.setApiName(menuItem.getApiName());
        if (!Strings.isNullOrEmpty(menuItem.getMenuData().getGroupApiName())) {
            entity.setGroupApiName(menuItem.getMenuData().getGroupApiName());
        }
        if (menuItem.getMetaMenuData() != null && StringUtils.isNotEmpty(menuItem.getMetaMenuData().getAppId())) {
            entity.setAppId(menuItem.getMetaMenuData().getAppId());
        }
        return entity;
    }

    public static UserMenuItem buildUserMenuItem(int ei, int employeeId, MenuItem menuItem,
                                                 Map<String, String> languageMap, List<String> searchWords,
                                                 ClientInfo clientInfo, String menuId) {
        UserMenuItem userMenuItem = new UserMenuItem();

        MenuData menuData = menuItem.getMenuData();

        MetaMenuData metaMenuData = menuItem.getMetaMenuData();
        userMenuItem.setId(menuItem.getApiName());
        userMenuItem.setType(StringUtils.equals(MenuType.GROUP, menuItem.getMenuItemType()) ? MenuType.GROUP : MenuType.MENU);
        if (null != metaMenuData && StringUtils.isNotBlank(metaMenuData.getObjectApiName())) {
            userMenuItem.setReferenceApiname(metaMenuData.getObjectApiName());
        } else {
            userMenuItem.setReferenceApiname(menuItem.getApiName());
        }
        userMenuItem.setPid(menuData.getGroupApiName());
        userMenuItem.setIsHidden(menuData.getIsHidden());
        userMenuItem.setNumber(menuData.getOrderNumber());
        userMenuItem.setMenuItemId(menuItem.getApiName());
        userMenuItem.setPrivilegeAction(menuItem.getPrivilegeAction());

        if (MenuType.GROUP.equals(menuItem.getMenuItemType())) {
            userMenuItem.setDisplayName(MenuUtil.getGroupLanguageName(menuData.getApiName(), menuData.getName(), languageMap, menuId));
            userMenuItem.setI18nKeyList(Lists.newArrayList(TranslateI18nUtils.getWebAppViewGroupName(menuId, menuData.getApiName())));
        } else {
            userMenuItem.setDisplayName(metaMenuData.getName());
            userMenuItem.setAppId(metaMenuData.getAppId());
            userMenuItem.setI18nKeyList(metaMenuData.getI18nKeyList());
        }
        if (metaMenuData != null) {
            // 880之后不用了，先留着
            userMenuItem.setIconPathHome(metaMenuData.getIcon() != null ? metaMenuData.getIcon().getIcon_1() : null);
            userMenuItem.setIconPathMenu(metaMenuData.getIcon() != null ? metaMenuData.getIcon().getIcon_1() : null);

            userMenuItem.setIconIndex(metaMenuData.getIconIndex());
            userMenuItem.setIconSlot(metaMenuData.getIconSlot());


            Url url = metaMenuData.getUrl();
            String menuUrl = MenuUtil.getMenuUrl(ei, employeeId, url, clientInfo);

            MobileConfig mobileConfig = MenuUtil.getMobileConfig(ei, employeeId, url, clientInfo);

            userMenuItem.setMobileConfig(mobileConfig);
            userMenuItem.setUrl(menuUrl);
            userMenuItem.setUseDefaultUrl(StringUtils.isEmpty(menuUrl) ? false : url.isUseServerUrl());
            if (url != null && url.isUseServerUrl()) {
                userMenuItem.setWebGoJumpUrl(url.getWebGoJumpUrl());
            }
            userMenuItem.setTarget(metaMenuData.getTarget());
            userMenuItem.setFxIcon(metaMenuData.getFxIcon());
            if (null != metaMenuData.getIconSlot()) {
                userMenuItem.setIconSlot(metaMenuData.getIconSlot());
                userMenuItem.setIconPathHome(DefaultTenantConfig.getObjectIcon1Url(metaMenuData.getIconSlot()));
                userMenuItem.setIconPathMenu(DefaultTenantConfig.getObjectIcon1Url(metaMenuData.getIconSlot()));
                userMenuItem.setFxIcon(DefaultTenantConfig.getObjectFxIconUrl(metaMenuData.getIconSlot()));
            }
        }
        userMenuItem.setSearchWords(searchWords);

        return userMenuItem;
    }

    public static List<UserMenuItem> buildUserMenuItems(int ei, int employeeId, List<MenuItem> menuItems,
                                                        Map<String, String> languageMap,
                                                        Map<String, List<String>> searchWordsMap,
                                                        ClientInfo clientInfo) {
        return buildUserMenuItems(ei, employeeId, menuItems, languageMap, searchWordsMap, clientInfo, null);
    }

    public static TenantMenuItemVO getDefaultRecent() {
        TenantMenuItemVO tenantMenuItemVO = new TenantMenuItemVO();
        tenantMenuItemVO.setId("recent");
        tenantMenuItemVO.setCanDelete(false);
        tenantMenuItemVO.setDisplayName(I18N.text(I18NKey.RECENT));
        tenantMenuItemVO.setAppId("CRM");
        tenantMenuItemVO.setReferenceApiname("recent");
        tenantMenuItemVO.setType("menu");
        tenantMenuItemVO.setNumber(1);
        tenantMenuItemVO.setSearchWords(Lists.newArrayList("zuijinshiyon", I18N.text(I18NKey.RECENT), "zjsy"));
        return tenantMenuItemVO;
    }

    public static TenantMenuItemVO getDefaultCrmRemind() {
        TenantMenuItemVO tenantMenuItemVO = new TenantMenuItemVO();
        tenantMenuItemVO.setId("CrmRemind");
        tenantMenuItemVO.setCanDelete(false);
        tenantMenuItemVO.setDisplayName(I18N.text(I18NKey.CRM_REMIND));
        tenantMenuItemVO.setAppId("CRM");
        tenantMenuItemVO.setReferenceApiname("CrmRemind");
        tenantMenuItemVO.setType("menu");
        tenantMenuItemVO.setNumber(2);
        tenantMenuItemVO.setSearchWords(Lists.newArrayList("crmtixing", I18N.text("qixin.OSS1.CrmReminder.name"), "tx"));
        return tenantMenuItemVO;
    }

    public static TenantMenuItemVO getDefaultCrmToDo() {
        TenantMenuItemVO tenantMenuItemVO = new TenantMenuItemVO();
        tenantMenuItemVO.setId("CrmToDo");
        tenantMenuItemVO.setCanDelete(false);
        tenantMenuItemVO.setDisplayName(I18N.text(I18NKey.CRM_TODO));
        tenantMenuItemVO.setAppId("CRM");
        tenantMenuItemVO.setReferenceApiname("CrmToDo");
        tenantMenuItemVO.setType("menu");
        tenantMenuItemVO.setNumber(3);
        tenantMenuItemVO.setSearchWords(Lists.newArrayList("crmdaibanz", I18N.text("CRM待办"), "db")); // ignoreI18n
        return tenantMenuItemVO;
    }

    public static List<UserMenuItem> buildUserMenuItems(int ei, int employeeId, List<MenuItem> menuItems,
                                                        Map<String, String> languageMap,
                                                        Map<String, List<String>> searchWordsMap,
                                                        ClientInfo clientInfo, String menuId) {
        if (CollectionUtils.isEmpty(menuItems)) {
            return Lists.newArrayList();
        }

        return menuItems.stream().map(x -> {
            List<String> searchWords;
            if (MenuType.GROUP.equals(x.getMenuItemType())) {
                searchWords = searchWordsMap.getOrDefault(x.getMenuData().getName(), Lists.newArrayList());
            } else {
                searchWords = searchWordsMap.getOrDefault(x.getMetaMenuData().getName(), Lists.newArrayList());
            }
            return buildUserMenuItem(ei, employeeId, x, languageMap, searchWords, clientInfo, menuId);
        }).filter(userMenuItem -> Objects.nonNull(userMenuItem)).collect(Collectors.toList());
    }

    public static List<MenuItem> buildUserMenuItems(List<MenuDataEntity> menuDataEntities, List<MetaMenuData> metaMenuDataList) {
        Map<String, MetaMenuData> metaMenuDataMap = metaMenuDataList.stream().collect(Collectors.toMap(MetaMenuData::getApiName, x -> x, (key1, key2) -> key2));
        List<MenuItem> menuItems = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(menuDataEntities)) {
            for (MenuDataEntity menuDataEntity : menuDataEntities) {
                MetaMenuData metaMenuData = metaMenuDataMap.get(menuDataEntity.getApiName());
                MenuItem menuItem = buildMenuItem(menuDataEntity, metaMenuData);
                menuItems.add(menuItem);
            }
            menuItems = menuItems.stream().filter(menuItem -> menuItem != null).collect(Collectors.toList());
        }
        return menuItems;
    }

    private static MenuItem buildMenuItem(MenuDataEntity entity, MetaMenuData metaMenuData) {

        if (!MenuType.GROUP.equals(entity.getType()) && metaMenuData == null) {
            return null;
        }

        MenuData menuData = new MenuData();
        menuData.setApiName(entity.getApiName());
        menuData.setIsHidden(entity.getIsHidden());
        menuData.setGroupApiName(entity.getGroupApiName());
        menuData.setType(entity.getType());
        menuData.setOrderNumber(entity.getOrderNumber());

        if (MenuType.GROUP.equals(entity.getType())) {
            menuData.setName(entity.getName());
        } else {
            menuData.setName(metaMenuData.getName());
        }
        MenuItem menuItem = MenuUtil.buildMenuItem(metaMenuData, menuData);
        return menuItem;
    }

    public static List<MenuItem> buildMenuDataFromDos(String appId,
                                                      List<MenuDataEntity> entities,
                                                      List<MetaMenuData> metaMenuDataList,
                                                      List<AddMenuItemVo> addMenuItemVoList,
                                                      Boolean needAddMenus, boolean needFillSystemMenuList) {
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        Map<String, MetaMenuData> metaMenuDataMap = MenuUtil.getMetaMenuDataMap(metaMenuDataList);

        //增加配置中心的菜单
        entities = buildAddConfigMenuData(appId, entities, metaMenuDataMap, addMenuItemVoList);

        List<MenuItem> menuItemList = entities.stream().map((MenuDataEntity x) -> {
            MetaMenuData metaMenuData = metaMenuDataMap.get(MenuUtil.getUniqueKey(StringUtils.isNotEmpty(x.getAppId()) ? x.getAppId() : appId, x.getApiName()));
            if (!MenuType.GROUP.equals(x.getType()) && metaMenuData == null) {
                return null;
            }
            MenuData menuData = buildMenuDataFromDo(x);
            MenuItem menuItem = new MenuItem();
            menuItem.setApiName(x.getApiName());
            menuItem.setMenuItemType(x.getType());
            menuItem.setMenuData(menuData);
            menuItem.setMetaMenuData(metaMenuData);
            return menuItem;

        }).filter(Objects::nonNull).collect(Collectors.toList());
        //needAddMenus 判断是否需要追加菜单    判断依据预制菜单均追加
        if (!needAddMenus) {
            return menuItemList;
        }
        Set<String> entityApiNames = entities.stream().
                map(menuDataEntity -> MenuUtil.getUniqueKey(StringUtils.isEmpty(menuDataEntity.getAppId()) ? appId : menuDataEntity.getAppId(), menuDataEntity.getApiName())).
                collect(Collectors.toSet());
        Set<String> defaultApiNames = metaMenuDataMap.keySet();
        //菜单模板  追加菜单
        List<String> addApiNames = (List<String>) CollectionUtils.subtract(defaultApiNames, entityApiNames);
        List<MenuItem> addMenuItems = MenuUtil.buildDefaultMenuItems(addApiNames, metaMenuDataMap, entities.size());
        fillAddMenuItemsToTenantMenu(needFillSystemMenuList, menuItemList, addMenuItems);
        return menuItemList;
    }

    /**
     * 将四个预设的菜单，排在最前面（首页、待办、最近使用、提醒）
     * 如果租户级菜单中没有则默认排在最前面，否则按租户级菜单中的顺序处理
     *
     * @param menuItemList
     * @param addMenuItems
     */
    private static void fillAddMenuItemsToTenantMenu(boolean needFillSystemMenuList, List<MenuItem> menuItemList, List<MenuItem> addMenuItems) {
        if (needFillSystemMenuList) {
            Map<String, MenuItem> menuItemMap = addMenuItems.stream().collect(Collectors.toMap(MenuItem::getApiName, it -> it, (x1, x2) -> x1));
            List<MenuItem> systemMenuList = AppMenuConfig.getSystemMenuIds().stream().map(menuItemMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            MenuUtil.addNewMenu(menuItemList, systemMenuList);
            addMenuItems = addMenuItems.stream().filter(x -> !AppMenuConfig.getSystemMenuIds().contains(x.getApiName())).collect(Collectors.toList());
        }
        menuItemList.addAll(addMenuItems);
    }


    //构建配置中心追加的菜单
    public static List<MenuDataEntity> buildAddConfigMenuData(String appId, List<MenuDataEntity> entities, Map<String, MetaMenuData> metaMenuDataMap, List<AddMenuItemVo> addMenuItemVoList) {

        if (CollectionUtils.isEmpty(addMenuItemVoList)) {
            return entities;
        }

        //先增加顶部菜单项
        List<AddMenuItemVo> topMenus = addMenuItemVoList.stream().filter(addMenuItemVo -> addMenuItemVo.isTopMenu()).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(topMenus)
                && checkHasApiNameByMetaMenu(MenuUtil.getUniqueKey(topMenus.get(0).getAppId(), topMenus.get(0).getApiName()), metaMenuDataMap)
                && !checkHasApiNameByEntities(MenuUtil.getUniqueKey(topMenus.get(0).getAppId(), topMenus.get(0).getApiName()), entities)) {
            String uniqueKey = MenuUtil.getUniqueKey(topMenus.get(0).getAppId(), topMenus.get(0).getApiName());
            entities = addTopMenuDataEntity(entities, metaMenuDataMap.get(uniqueKey));

        }
        //再增加根据afterApiName追加的菜单项
        List<AddMenuItemVo> addMenuItemVosByAfterApiName = addMenuItemVoList.stream().
                filter(addMenuItemVo -> StringUtils.isNotEmpty(addMenuItemVo.getAfterApiName())).
                collect(Collectors.toList());

        List<String> entityApiNames = entities.stream().
                map(menuDataEntity -> MenuUtil.getUniqueKey(StringUtils.isEmpty(menuDataEntity.getAppId()) ? appId : menuDataEntity.getAppId(), menuDataEntity.getApiName())).
                collect(Collectors.toList());

        Map<String, AddMenuItemVo> addMenuItemVoMap = MenuUtil.getAddMenuItemVoMap(addMenuItemVosByAfterApiName);
        List<String> addMenuItemApiNames = addMenuItemVosByAfterApiName.stream().
                map(addMenuItemVo -> MenuUtil.getUniqueKey(addMenuItemVo.getAppId(), addMenuItemVo.getApiName())).
                collect(Collectors.toList());

        List<String> addApiNames = (List<String>) CollectionUtils.subtract(addMenuItemApiNames, entityApiNames);

        for (String apiName : addApiNames) {
            entities = addMenuDataEntity(appId, entities, metaMenuDataMap, addMenuItemVoMap.get(apiName));
        }

        return entities;

    }

    public static boolean checkHasApiNameByMetaMenu(String uniqueApiName, Map<String, MetaMenuData> metaMenuDataMap) {

        Set<String> metaMenuDataApiNames = metaMenuDataMap.keySet();
        return metaMenuDataApiNames.contains(uniqueApiName);

    }

    private static boolean checkHasApiNameByEntities(String uniqueApiName, List<MenuDataEntity> entities) {

        List<String> entityApiNames = entities.stream().
                map(menuDataEntity -> MenuUtil.getUniqueKey(menuDataEntity.getAppId(), menuDataEntity.getApiName())).
                collect(Collectors.toList());

        return entityApiNames.contains(uniqueApiName);
    }

    private static List<MenuDataEntity> addTopMenuDataEntity(List<MenuDataEntity> entities, MetaMenuData metaMenuData) {

        MenuDataEntity menuDataEntity = new MenuDataEntity();
        menuDataEntity.setApiName(metaMenuData.getApiName());
        menuDataEntity.setType(metaMenuData.getMenuType());
        menuDataEntity.setIsHidden(false);
        menuDataEntity.setName(metaMenuData.getName());
        menuDataEntity.setOrderNumber(0);
        menuDataEntity.setGroupApiName(null);
        menuDataEntity.setAppId(metaMenuData.getAppId());

        for (MenuDataEntity entity : entities) {
            int orderNumber = entity.getOrderNumber();
            orderNumber++;
            entity.setOrderNumber(orderNumber);
        }
        entities.add(menuDataEntity);
        return entities;
    }

    private static List<MenuDataEntity> addMenuDataEntity(String appId, List<MenuDataEntity> entities, Map<String, MetaMenuData> metaMenuDataMap, AddMenuItemVo addMenuItemVo) {
        String uniqueKey = MenuUtil.getUniqueKey(addMenuItemVo.getAppId(), addMenuItemVo.getApiName());
        if (!checkHasApiNameByMetaMenu(uniqueKey, metaMenuDataMap) || checkHasApiNameByEntities(uniqueKey, entities)) {
            return entities;
        }

        Map<String, MenuDataEntity> menuDataEntityMap = MenuUtil.getMenuDataEntityMap(appId, entities);

        MenuDataEntity targetMenuDataEntity = menuDataEntityMap.get(MenuUtil.getUniqueKey(addMenuItemVo.getAppId(), addMenuItemVo.getAfterApiName()));
        if (targetMenuDataEntity == null || targetMenuDataEntity.getIsHidden()) {
            return entities;
        }

        MenuDataEntity addMenuDataEntity = new MenuDataEntity();

        {
            addMenuDataEntity.setApiName(MenuUtil.getApiName(appId, uniqueKey));
            addMenuDataEntity.setGroupApiName(targetMenuDataEntity.getGroupApiName());
            addMenuDataEntity.setName(metaMenuDataMap.get(uniqueKey).getName());
            addMenuDataEntity.setType(metaMenuDataMap.get(uniqueKey).getMenuType());
            addMenuDataEntity.setIsHidden(false);
            addMenuDataEntity.setAppId(menuDataEntityMap.get(uniqueKey) == null ? null : menuDataEntityMap.get(uniqueKey).getAppId());
            int num = targetMenuDataEntity.getOrderNumber();
            addMenuDataEntity.setOrderNumber(++num);
        }


        for (MenuDataEntity menuDataEntity : entities) {

            if (menuDataEntity.getOrderNumber() > targetMenuDataEntity.getOrderNumber()) {
                int orderNumber = menuDataEntity.getOrderNumber();
                orderNumber++;
                menuDataEntity.setOrderNumber(orderNumber);
            }
        }


        entities.add(addMenuDataEntity);

        return entities;

    }

    public static MenuData buildMenuDataFromDo(MenuDataEntity entity) {
        MenuData menuData = new MenuData();
        menuData.setApiName(entity.getApiName());
        menuData.setName(entity.getName());
        menuData.setIsHidden(entity.getIsHidden());
        menuData.setOrderNumber(entity.getOrderNumber());
        menuData.setType(entity.getType());
        menuData.setGroupApiName(entity.getGroupApiName());
        return menuData;
    }

    public static List<String> getMenuNames(List<MenuItem> menuItems) {
        if (CollectionUtils.isEmpty(menuItems)) {
            return Lists.newArrayList();
        }
        List<String> menuNames = menuItems.stream().
                filter(menuItem -> !MenuType.GROUP.equals(menuItem.getMenuItemType())).
                map(menuItem -> menuItem.getMetaMenuData().getName()).
                collect(Collectors.toList());
        List<String> groupNames = menuItems.stream().
                filter(menuItem -> MenuType.GROUP.equals(menuItem.getMenuItemType())).
                map(menuItem -> menuItem.getMenuData().getName()).
                collect(Collectors.toList());
        menuNames.addAll(groupNames);
        return menuNames;
    }
}
