package com.facishare.webpage.customer.system;

import com.facishare.webpage.customer.dao.entity.PageTempleEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/25 2:41 下午
 */
public interface TemplateSystemService {

    Map<String, List<PageTempleEntity>> querySystemTemplateMap(int tenantId, int appType, List<String> appIds, String type);

    List<PageTempleEntity> queryTemplateList(int tenantId, int appType, String appId, String type);

    List<PageTempleEntity> queryUserTemplateList(int tenantId, int employeeId, int appType, String appId, String type);

    PageTempleEntity queryPageTempleEntityById(int tenantId, String templateId);

    PageTempleEntity getPageTemplateBySynFromWebTemplateId(String synFromWebTemplateId);

    PageTempleEntity createOrUpdate(int tenantId, PageTempleEntity pageTempleEntity);





}
