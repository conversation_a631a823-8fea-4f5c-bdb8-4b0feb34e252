package com.facishare.webpage.customer.rest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.model.HomePageLayoutCard;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.common.EmployeeConfigCommonService;
import com.facishare.webpage.customer.config.ComponentConfig;
import com.facishare.webpage.customer.dao.HomePageLayoutDao;
import com.facishare.webpage.customer.dao.entity.HomePageLayoutEntity;
import com.facishare.webpage.customer.api.model.EmployeeConfig;
import com.facishare.webpage.customer.remote.TempFileToFormalFile;
import com.facishare.webpage.customer.rest.arg.GetEmployeeConfigValuesArg;
import com.facishare.webpage.customer.rest.arg.GetEmployeeHomePageListArg;
import com.facishare.webpage.customer.rest.arg.GetHomePageByIdArg;
import com.facishare.webpage.customer.rest.arg.GetUserCustomerLayoutRestArg;
import com.facishare.webpage.customer.rest.result.GetEmployeeConfigValuesResult;
import com.facishare.webpage.customer.rest.result.GetEmployeeHomePageListResult;
import com.facishare.webpage.customer.rest.result.GetHomePageByIdResult;
import com.facishare.webpage.customer.rest.result.GetUserCustomerLayoutRestResult;
import com.facishare.webpage.customer.service.EmployeeConfigBaseService;
import com.facishare.webpage.customer.service.HomePageBaseService;
import com.facishare.webpage.customer.util.component.NewDataCovertOldData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/8/15
 */
@Path("/homePageRest")
@Slf4j
@Controller
public class HomePageRestActionImpl implements HomePageRestAction {

    @Autowired
    private HomePageBaseService homePageBaseService;
    @Autowired
    private EmployeeConfigBaseService employeeConfigBaseService;
    @Autowired
    private EmployeeConfigCommonService employeeConfigCommonService;
    @Resource
    private ComponentConfig componentConfig;
    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private TempFileToFormalFile tempFileToFormalFile;
    @Autowired
    private HomePageLayoutDao homePageLayoutDao;

    public static final String TN_PATH_PREFIX = "TN_";
    public static final String TA_PATH_PREFIX = "TA_";
    public static final String N_PATH_PREFIX = "N_";
    public static final String API_NAME = "api_name";
    public static final String SLIDEIMAGE = "slideImage";
    public static final String IMGS = "imgs";
    public static final String IMG = "img";
    public static final String COMPONENTS = "components";

    @Override
    @Path("/getHomePageById")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public GetHomePageByIdResult getHomePageById(@HeaderParam("x-fs-ei") int tenantId, @HeaderParam("x-fs-locale") Locale locale, GetHomePageByIdArg arg) {

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(locale);
        HomePageLayoutTO homePageLayoutTO =
                homePageBaseService.getHomePageLayoutByApiName(
                        buildUserInfo(tenantId, arg.getEnterpriseAccount(), arg.getEmployeeId()),
                        arg.getLayoutId(),
                        arg.getLayoutId(),
                        arg.getAppType(),
                        clientInfo, false);
        GetHomePageByIdResult result = new GetHomePageByIdResult();
        result.setHomePageLayoutTO(homePageLayoutTO);
        return result;
    }

    @Override
    @Path("/getEmployeeConfigValues")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public GetEmployeeConfigValuesResult getEmployeeConfigValues(@HeaderParam("x-fs-ei") int tenantId, GetEmployeeConfigValuesArg arg) {
        List<EmployeeConfig> employeeConfigList = employeeConfigBaseService.getEmployeeConfigValueByKeys(
                arg.getLayoutId(), tenantId, arg.getEmployeeId(), arg.getKeys());
        employeeConfigList = employeeConfigCommonService.buildEmployeeConfig(employeeConfigList, tenantId, arg.getEmployeeId(), null, arg.getType());
        GetEmployeeConfigValuesResult result = new GetEmployeeConfigValuesResult();
        result.setConfigInfoList(employeeConfigList);
        return result;
    }

    @Override
    @Path("/getEmployeeHomePageList")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public GetEmployeeHomePageListResult getEmployeeHomePageList(@HeaderParam("x-fs-ei") int tenantId, @HeaderParam("x-fs-locale") Locale locale, GetEmployeeHomePageListArg arg) {
        Boolean enablePersonPageConfig = homePageBaseService.queryEnablePersonPageConfig(tenantId);
        List<HomePageLayoutTO> homePageLayoutList = homePageBaseService.getEmployeeHomePageLayoutList(tenantId, arg.getEmployeeId(), BizType.CRM.getType(), Constant.APP_CRM, locale, enablePersonPageConfig);
        if (CollectionUtils.isNotEmpty(homePageLayoutList)) {
            homePageLayoutList = homePageLayoutList.stream().map(x -> {
                if (x.getDataVersion() == 200) {
                    Map<String, Integer> widgetTypeMap = componentConfig.getWidgetTypeMap();
                    List<String> crmFilterComponents = componentConfig.getCrmFilterComponentsByApp();
                    List<HomePageLayoutCard> homePageLayoutCardList = NewDataCovertOldData.covertNewData(x.getCustomerLayout(), widgetTypeMap, crmFilterComponents);
                    x.setHomePageLayouts(homePageLayoutCardList);
                }
                return x;
            }).collect(Collectors.toList());
        }
        GetEmployeeHomePageListResult result = new GetEmployeeHomePageListResult();
        result.setHomePageLayoutTOList(homePageLayoutList);

        return result;
    }

    @Override
    @Path("/queryUserCustomerLayout")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public GetUserCustomerLayoutRestResult queryUserCustomerLayout(@HeaderParam("x-fs-ei") int tenantId, @HeaderParam("x-fs-locale") Locale locale, GetUserCustomerLayoutRestArg arg) {
        String enterpriseIdAccount = eieaConverter.enterpriseIdToAccount(tenantId);
        HomePageLayoutTO homePageLayout = homePageBaseService.getUserHomePageLayoutByApiName(buildUserInfo(tenantId, enterpriseIdAccount, arg.getEmployeeId()), null, arg.getLayoutId(), null, arg.getAppType(), locale);
        GetUserCustomerLayoutRestResult result = new GetUserCustomerLayoutRestResult();
        result.setHomePageLayout(homePageLayout);
        return result;
    }

    private UserInfo buildUserInfo(int tenantId, String enterpriseAccount, int employeeId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(tenantId);
        userInfo.setEnterpriseAccount(enterpriseAccount);
        userInfo.setEmployeeId(employeeId);
        return userInfo;
    }

    /**
     * 首页布局更改集后动作
     *
     * @param arg
     * @return
     */
    @Override
    @Path("/homePageLayoutAfterAction")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public void homePageLayoutAfterAction(@RequestBody GetHomePageByIdArg arg) {
        log.info("homePageLayoutAfterAction.start.param ={}", JSONObject.toJSONString(arg));
        HomePageLayoutEntity homePageLayoutEntity = homePageLayoutDao.getHomePageByTenantIdAndApiName(arg.getEnterpriseId(), arg.getApiName(), null);
        List<JSONObject> customerLayoutList = new ArrayList<>();
        customerLayoutList.add(JSONObject.parseObject(homePageLayoutEntity.getCustomerLayout()));
        if (CollectionUtils.isNotEmpty(homePageLayoutEntity.getCustomerLayoutList())) {
            customerLayoutList.addAll(homePageLayoutEntity.getCustomerLayoutList().stream().map(item->JSONObject.parseObject(item)).collect(Collectors.toList()));
        }
        List<String> imageList = new ArrayList<>();
        customerLayoutList.forEach(layout -> {
            JSONArray components = layout.getJSONArray(COMPONENTS);
            if (CollectionUtils.isNotEmpty(components)) {
                components.forEach(component -> {
                    JSONObject conponentJson = JSONObject.parseObject(JSONObject.toJSONString(component));
                    if (SLIDEIMAGE.equals(conponentJson.get(API_NAME))) {
                        JSONArray imgs = conponentJson.getJSONArray(IMGS);
                        imgs.forEach(img -> {
                            JSONObject imgJson = JSONObject.parseObject(JSONObject.toJSONString(img));
                            imageList.add(imgJson.getString(IMG));
                        });
                    }
                });
            }
        });

        List<String> imageListNew = imageList.stream().filter(item -> !Strings.isNullOrEmpty(item) && (item.startsWith(TN_PATH_PREFIX) || item.startsWith(N_PATH_PREFIX))).distinct().collect(Collectors.toList());
        //copy图片
        imageListNew.forEach(item -> {
            tempFileToFormalFile.copyFileNPathToOther(arg.getEnterpriseId(), arg.getOldEnterpriseId(), 1000, item);
        });
        log.info("homePageLayoutAfterAction.end.param ={}", JSONObject.toJSONString(arg));
    }


}
