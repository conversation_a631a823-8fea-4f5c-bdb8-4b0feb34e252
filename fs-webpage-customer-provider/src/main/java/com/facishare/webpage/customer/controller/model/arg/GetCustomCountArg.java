package com.facishare.webpage.customer.controller.model.arg;

import com.alibaba.fastjson.annotation.JSONField;
import io.protostuff.Tag;
import lombok.Data;

/**
 * Created by she<PERSON> on 19/9/25.
 */
@Data
public class GetCustomCountArg extends BaseArg {

    @Tag(1)
    @JSONField(name = "M1")
    private String appId;

    @Tag(2)
    @JSONField(name = "M2")
    private String countSourceData;


    @Override
    public void valid() throws Exception {

    }
}
