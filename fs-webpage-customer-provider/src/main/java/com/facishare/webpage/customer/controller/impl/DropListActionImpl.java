package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.DropListAction;
import com.facishare.webpage.customer.controller.model.arg.component.GetMenuDropListArg;
import com.facishare.webpage.customer.controller.model.arg.component.GetUtilityBarDropListArg;
import com.facishare.webpage.customer.controller.model.result.component.GetMenuDropListResult;
import com.facishare.webpage.customer.controller.model.result.component.GetUtilityBarDropListResult;
import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.service.CustomerDropListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.facishare.webpage.customer.constant.AppTypeEnum.CUSTOMER_LINK_APP;

/**
 * Created by zhangyu on 2020/10/27
 */
@Controller
@Slf4j
@RequestMapping("/dropList")
public class DropListActionImpl implements DropListAction {

    @Resource
    private CustomerDropListService customerDropListService;
    @Autowired
    private DefaultTenantConfig defaultTenantConfig;

    @Override
    @RequestMapping(value = "getMenuDropList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetMenuDropListResult getMenuDropList(@FSUserInfo UserInfo userInfo,
                                                 @FSClientInfo ClientInfo clientInfo,
                                                 @RequestBody GetMenuDropListArg arg) {
        try{
            arg.valid();
            RequestContextManager.initContextForIsFromManage(true);
            String appId = arg.getAppId();
            String customerLinkAppId = null;
            if (arg.getAppType() == CUSTOMER_LINK_APP.getAppType()) {
                appId = WebPageConstants.CROSS_PaaS;
                customerLinkAppId = arg.getAppId();
            }
            if (StringUtils.isNotBlank(appId) && appId.equals(defaultTenantConfig.getQudaomenhuAppId())) {
                customerLinkAppId = arg.getAppId();
            }

            List<DropListItem> menuDropList = customerDropListService.getMenuDropList(
                    DataSourceEnv.values(arg.isDataSourceEnv()),
                    userInfo,
                    appId,
                    false,
                    clientInfo.getLocale(),
                    customerLinkAppId,
                    arg.isPreviewNewCrmFlag());
            GetMenuDropListResult result = new GetMenuDropListResult();
            result.setMenuDropList(menuDropList);
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }

    @Override
    @RequestMapping(value = "getUtilityBarDropList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetUtilityBarDropListResult getUtilityBarDropList(@FSUserInfo UserInfo userInfo,
                                                             @FSClientInfo ClientInfo clientInfo,
                                                             @RequestBody GetUtilityBarDropListArg arg) {
        List<DropListItem> utilityBarDropList = customerDropListService.getUtilityBarDropList(
                userInfo,
                arg.getAppType(),
                arg.getAppId(),
                clientInfo.getLocale());
        GetUtilityBarDropListResult result = new GetUtilityBarDropListResult();
        result.setUtilityBarDropList(utilityBarDropList);
        return result;
    }

    @Override
    @RequestMapping(value = "getUserMenuDropList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetMenuDropListResult getUserMenuDropList(@FSUserInfo UserInfo userInfo,
                                                     @FSClientInfo ClientInfo clientInfo,
                                                     @RequestBody GetMenuDropListArg arg) {
        String appId = arg.getAppId();
        String customerLinkAppId = null;
        if (arg.getAppType() == CUSTOMER_LINK_APP.getAppType()) {
            appId = WebPageConstants.CROSS_PaaS;
            customerLinkAppId = arg.getAppId();
        }
        arg.valid();
        List<DropListItem> menuDropList = customerDropListService.getMenuDropList(
                DataSourceEnv.INNER,
                userInfo,
                appId,
                true,
                clientInfo.getLocale(), customerLinkAppId, arg.isPreviewNewCrmFlag());
        GetMenuDropListResult result = new GetMenuDropListResult();
        result.setMenuDropList(menuDropList);
        return result;
    }
}
