package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.api.constant.PaaSStatus;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.ThemeLayoutEntity;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2024/11/5.
 */
@Setter
@Slf4j
public class ThemeLayoutEntityDaoImpl implements ThemeLayoutEntityDao {

    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(ThemeLayoutEntity.class, true);
    }

    private Query<ThemeLayoutEntity> buildQuery(int tenantId, String clientType) {
        Query<ThemeLayoutEntity> query = datastore.createQuery(ThemeLayoutEntity.class);
        query.field("tenantId").equal(tenantId);
        if (ClientType.mobile.same(clientType)) {
            query.field("clientType").equal(ClientType.mobile.getValue());
        } else {
            query.or(
                    query.criteria("clientType").equal(ClientType.web.getValue()),
                    query.criteria("clientType").doesNotExist()
            );
        }
        return query;
    }

    @Override
    public List<ThemeLayoutEntity> findBySiteApiNameIncludeDisable(int tenantId, String siteApiName) {
        return findBySiteApiName(tenantId, siteApiName, null, Collections.emptyList());
    }

    @Override
    public List<ThemeLayoutEntity> findBySiteApiNameIncludeDisable(int tenantId, String siteApiName, String clientType) {
        return findBySiteApiName(tenantId, siteApiName, clientType, Collections.emptyList());
    }

    private List<ThemeLayoutEntity> findBySiteApiName(int tenantId, String siteApiName, String clientType, List<Integer> statusList) {
        Query<ThemeLayoutEntity> query = buildQuery(tenantId, clientType);
        if (CollectionUtils.isNotEmpty(statusList)) {
            query.field("status").in(statusList);
        }
        query.field("siteApiName").equal(siteApiName);
        query.order("createTime");
        return query.asList();
    }

    @Override
    public List<ThemeLayoutEntity> findByApiNames(int tenantId, List<String> apiNames, String clientType) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Lists.newArrayList();
        }
        Query<ThemeLayoutEntity> query = buildQuery(tenantId, clientType);
        query.field("status").equal(PaaSStatus.enable);
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.order("createTime");
        return query.asList();
    }


    @Override
    public List<ThemeLayoutEntity> findByApiNamesIncludeDisable(int tenantId, List<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Lists.newArrayList();
        }
        Query<ThemeLayoutEntity> query = datastore.createQuery(ThemeLayoutEntity.class);
        query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
        query.field("tenantId").equal(tenantId);
        query.field("apiName").in(apiNames);
        query.order("createTime");
        return query.asList();
    }

    @Override
    public void batchSave(User user, List<ThemeLayoutEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(entity -> {
            if (StringUtils.isBlank(entity.getId())) {
                entity.setId(ObjectId.get().toHexString());
            }
            entity.setTenantId(user.getTenantId());
            entity.setCreatorId(user.getUserId());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdaterId(user.getUserId());
            entity.setUpdateTime(entity.getCreateTime());
            if (Objects.isNull(entity.getStatus())) {
                entity.setStatus(PaaSStatus.enable);
            }
            if (Strings.isNullOrEmpty(entity.getSourceType())) {
                entity.setSourceType(SourceType.CUSTOMER);
            }
        });
        datastore.save(entityList);
    }

    @Override
    public void batchUpdate(User user, List<ThemeLayoutEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        long now = System.currentTimeMillis();
        entityList.forEach(entity -> {
            Query<ThemeLayoutEntity> query = datastore.createQuery(ThemeLayoutEntity.class);
            query.field("tenantId").equal(user.getTenantId());
            query.field("_id").equal(entity.getId());
            UpdateOperations<ThemeLayoutEntity> updateOperations = datastore.createUpdateOperations(ThemeLayoutEntity.class);
            if (Objects.nonNull(entity.getName())) {
                updateOperations.set("name", entity.getName());
            }
            if (Objects.nonNull(entity.getLayoutStructure())) {
                updateOperations.set("layoutStructure", entity.getLayoutStructure());
            }
            updateOperations.set("updateTime", now);
            updateOperations.set("updaterId", user.getUserId());
            datastore.findAndModify(query, updateOperations, false);
        });
    }

    @Override
    public void updateStatus(User user, List<String> ids, int status) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Query<ThemeLayoutEntity> query = datastore.createQuery(ThemeLayoutEntity.class);
        query.field("tenantId").equal(user.getTenantId());
        query.field("_id").in(ids);
        query.field("status").notEqual(status);
        UpdateOperations<ThemeLayoutEntity> updateOperations = datastore.createUpdateOperations(ThemeLayoutEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updateTime", System.currentTimeMillis());
        updateOperations.set("updaterId", user.getUserId());
        if (status == PaaSStatus.delete) {
            updateOperations.set("deleteId", ObjectId.get().toString());
        }
        datastore.update(query, updateOperations);
    }
}
