package com.facishare.webpage.customer.controller.model.arg.menu;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/12/23
 */
@Data
public class GetCrossMenuArg implements Serializable {

    private String upEnterpriseAccount;

    public void valid() {
        if (Strings.isNullOrEmpty(upEnterpriseAccount)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

}
