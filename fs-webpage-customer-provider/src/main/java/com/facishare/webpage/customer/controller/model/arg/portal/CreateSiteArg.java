package com.facishare.webpage.customer.controller.model.arg.portal;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2024/11/4.
 */
@Data
public class CreateSiteArg {
    private SiteInfoDTO siteInfo;
    private List<ThemeLayoutDTO> themeLayoutList;
    private List<SiteMenuDTO> menuList;
    private List<SitePageDTO> pageList;
    private Boolean isPublish;

    public void valid() {
        if (Objects.isNull(siteInfo)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if (StringUtils.isBlank(siteInfo.getApiName())) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if (StringUtils.isBlank(siteInfo.getAppId())) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if (CollectionUtils.isEmpty(pageList)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        pageList.forEach(x -> {
            if (StringUtils.isBlank(x.getApiName())) {
                throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
            }
            if (StringUtils.isNotBlank(x.getThemeLayoutApiName())) {
                if (CollectionUtils.isEmpty(themeLayoutList)) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
                if (themeLayoutList.stream().noneMatch(y -> Objects.equals(y.getApiName(), x.getThemeLayoutApiName()))) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
            }
        });
        if (CollectionUtils.isNotEmpty(themeLayoutList)) {
            themeLayoutList.forEach(x -> {
                if (StringUtils.isBlank(x.getApiName())) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(menuList)) {
            menuList.forEach(x -> {
                if (StringUtils.isBlank(x.getApiName())) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
            });
        }
    }
}
