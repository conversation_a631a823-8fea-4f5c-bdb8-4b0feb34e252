package com.facishare.webpage.customer.common.resource.model.result;

import com.facishare.webpage.customer.common.resource.model.PageInfo;
import com.facishare.webpage.customer.common.resource.model.TenantGroup;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/8 6:09 下午
 */
@Data
public class FindTenantGroupByOutTenantIdsResult implements Serializable {

    private int errCode;
    private String errMessage;
    private List<TenantGroup> result;
    private boolean success;
    private PageInfo pageInfo;

}
