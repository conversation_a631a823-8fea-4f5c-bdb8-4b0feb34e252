package com.facishare.webpage.customer.model.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.objgroup.common.service.model.resource.QueryComponentsResult;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.core.component.Component;
import com.facishare.webpage.customer.core.util.CollectionUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Created by zhangyu on 2020/6/3
 */
@Data
public class ComponentCustomer extends Component {

    private QueryComponentsResult.ComponentData componentData;

    private String appId;

    @Override
    public String getId() {
        return componentData.getApiName();
    }

    @Override
    public String getPId() {
        return ComponentConstant.CUS_COMPONENT;
    }

    @Override
    public String getName() {
        return componentData.getName();
    }

    @Override
    public String getDropItemType() {
        return ComponentConstant.dropListCompType;
    }

    @Override
    public String getApiName() {
        return componentData.getApiName();
    }

    @Override
    public String getType() {
        return "custom_comp";
    }

    @Override
    public int getLimit() {
        return 1;
    }

    @Override
    public JSONObject getProps() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("api_name", getApiName());
        jsonObject.put("custom_comp_api_name", getApiName());
        jsonObject.put("custom_comp_url", componentData.getUrl());
        jsonObject.put("header", componentData.getName());
        jsonObject.put("componentType", componentData.getComponentType());
        jsonObject.put("cmptId", componentData.getCmptId());
        if (StringUtils.isNotEmpty(getAppId())) {
            Map<String, String> scopeConfig = DefaultTenantConfig.getCustomComponentScopeByAppId(getAppId());
            if (CollectionUtils.notEmpty(scopeConfig)) {
                jsonObject.putAll(scopeConfig);
            }
        }
        return jsonObject;
    }

    @Override
    public int getGrayLimit() {
        return 1;
    }
}
