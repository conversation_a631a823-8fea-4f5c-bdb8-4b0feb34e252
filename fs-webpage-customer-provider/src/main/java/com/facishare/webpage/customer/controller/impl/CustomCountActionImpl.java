package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.CustomCountAction;
import com.facishare.webpage.customer.controller.model.arg.BatchGetCustomCountArg;
import com.facishare.webpage.customer.controller.model.arg.GetCustomCountArg;
import com.facishare.webpage.customer.controller.model.result.BatchGetCustomCountResult;
import com.facishare.webpage.customer.controller.model.result.GetCustomCountResult;
import com.facishare.webpage.customer.service.CustomCountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by shecheng on 19/9/24.
 */

@Controller
@Slf4j
@RequestMapping("/customCount")
public class CustomCountActionImpl implements CustomCountAction {

    @Resource
    private CustomCountService customCountService;

    @Override
    @RequestMapping(value = "getCustomCount", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetCustomCountResult getCustomCount(@FSUserInfo UserInfo userInfo, @FSOuterUserInfo OuterUserInfo outerUserInfo, @RequestBody GetCustomCountArg arg) {
        return customCountService.getCustomCount(userInfo, outerUserInfo, arg);
    }

    @Override
    @RequestMapping(value = "batchGetCustomCount", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public BatchGetCustomCountResult batchGetCustomCount(@FSUserInfo UserInfo userInfo, @FSOuterUserInfo OuterUserInfo outerUserInfo,@RequestBody  BatchGetCustomCountArg arg) {
        return customCountService.batchGetCustomCount(userInfo, outerUserInfo, arg);
    }
}
