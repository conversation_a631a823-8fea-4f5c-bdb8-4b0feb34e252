package com.facishare.webpage.customer.service.impl;

import com.facishare.webpage.customer.api.constant.EmployeeConstant;
import com.facishare.webpage.customer.dao.EmployeeConfigDao;
import com.facishare.webpage.customer.dao.entity.EmployeeConfigEntity;
import com.facishare.webpage.customer.api.model.EmployeeConfig;
import com.facishare.webpage.customer.service.EmployeeConfigBaseService;
import com.facishare.webpage.customer.util.EmployeeConfigUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2019/10/29
 */
public class EmployeeConfigBaseServiceImpl implements EmployeeConfigBaseService {
    private static int defaultEmployeeId = 0;

    @Autowired
    private EmployeeConfigDao employeeConfigDao;

    @Override
    public List<EmployeeConfig> getEmployeeConfigValueByKeys(String layoutId, int tenantId, int employeeId, List<Integer> keys) {
        if (Strings.isNullOrEmpty(layoutId)) {
            return Lists.newArrayList();
        }
        List<EmployeeConfigEntity> employeeConfigs = Lists.newArrayList();
        keys.stream().forEach(key -> {
            EmployeeConfigEntity employeeConfig;
            if (key == EmployeeConstant.HomePageDefault || key == EmployeeConstant.APP_LAYOUT_LIST_ORDER) {
                employeeConfig = employeeConfigDao.getEmployeeConfig(layoutId, tenantId, employeeId, key);
            } else {
                employeeConfig = employeeConfigDao.getEmployeeConfig(layoutId, tenantId, defaultEmployeeId, key);
            }
            if (employeeConfig != null) {
                employeeConfigs.add(employeeConfig);
            }
        });
        if (CollectionUtils.isEmpty(employeeConfigs)) {
            return Lists.newArrayList();
        }
        return employeeConfigs.stream().

                map(EmployeeConfigUtil::buildEmployeeConfig).

                collect(Collectors.toList());
    }

    @Override
    public boolean setEmployeeConfigValue(String layoutId, int tenantId, int employeeId, EmployeeConfig employeeConfig) {
        EmployeeConfigEntity employeeConfigEntity = employeeConfigDao.upsertEmployeeConfig(tenantId, employeeId, layoutId, employeeConfig);

        if (employeeConfigEntity != null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean setEmployeeConfigByApiName(String apiName, int tenantId, int employeeId, EmployeeConfig employeeConfig) {
        EmployeeConfigEntity employeeConfigEntity =
                employeeConfigDao.upsertEmployeeConfigByApiName(tenantId, employeeId, apiName, employeeConfig);

        if (employeeConfigEntity != null) {
            return true;
        }
        return false;
    }

    @Override
    public EmployeeConfig getEmployeeConfigStatusByKey(String layoutId, int tenantId, int employeeId, int key) {
        EmployeeConfigEntity employeeConfig = employeeConfigDao.getEmployeeConfig(layoutId, tenantId, employeeId, key);
        if (Objects.isNull(employeeConfig)) {
            return new EmployeeConfig();
        }
        EmployeeConfig employeeConfigResult = new EmployeeConfig();
        employeeConfigResult.setValue(employeeConfig.getEValue());
        return employeeConfigResult;
    }

    @Override
    public List<EmployeeConfig> getEmployeeConfigValueByApiNameHasNotDefault(String apiName, int enterpriseId, int employeeId) {
        EmployeeConfigEntity employeeConfig = employeeConfigDao.getEmployeeConfig(apiName, enterpriseId, employeeId, EmployeeConstant.HomePageDefault);
        if (null != employeeConfig) {
            return Lists.newArrayList(EmployeeConfigUtil.buildEmployeeConfig(employeeConfig));
        }
        return new ArrayList<>();
    }
}
