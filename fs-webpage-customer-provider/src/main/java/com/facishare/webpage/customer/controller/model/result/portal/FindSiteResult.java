package com.facishare.webpage.customer.controller.model.result.portal;

import com.facishare.webpage.customer.controller.model.I18nInfoDTO;
import com.facishare.webpage.customer.dao.entity.SiteLang;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2024/11/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FindSiteResult {
    private SiteInfoVO siteInfo;
    private List<ThemeLayoutVO> themeLayoutList;
    private List<SiteMenuVO> menuList;
    private List<SitePageVO> pageList;

    /**
     * 文件资源信息
     */
    private List<FileInfoVO> fileInfoList;

    private SiteConfigVO siteConfig;
    private List<ThemeStyleVO> themeStyleList;

    private List<I18nInfoDTO> i18nInfoList;
    private List<SiteLang> langList;
    @Builder.Default
    private boolean permissionFlag = true;

}
