package com.facishare.webpage.customer.metadata;

import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.core.config.MenusConfig;
import com.facishare.webpage.customer.metadata.model.ApplicationData;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/8/24
 */
@Component
public class NavigateMenuConfig {

    @Autowired
    private AppMenuConfig appMenuConfig;
    @Autowired
    private MenusConfig menusConfig;
    @Resource
    private LanguageService languageService;


    public List<ApplicationData> getNavigateMenuByConfig(int tenantId, String appId, Locale locale) {

        List<String> menuIds = appMenuConfig.getShowNavigateMenuIdsByAppId(appId);

        List<Menu> menuList = menusConfig.getMenusByIds(menuIds);

        List<String> grayNavigateMenuIds = appMenuConfig.getGrayNavigateMenuIdsByAppId(appId);

        if (CollectionUtils.isNotEmpty(grayNavigateMenuIds)) {
            menuList = menuList.stream().
                    filter(menu -> !grayNavigateMenuIds.contains(menu.getId()) ||
                            (grayNavigateMenuIds.contains(menu.getId()) && WebPageGraySwitch.isAllowNavigateMenuByBusiness(menu.getId(), tenantId))).
                    collect(Collectors.toList());
        }

        Map<String, String> queryMenusLanguage = languageService.queryMenusLanguage(tenantId, menuList, locale);

        return menuList.stream().map(menu -> {
            ApplicationData applicationData = new ApplicationData();
            {
                applicationData.setAppName(queryMenusLanguage.getOrDefault(menu.getNameI18nKey(), menu.getName()));
                applicationData.setAppId(menu.getId());
                applicationData.setIcon(menu.getIcon().getIcon_1());
                applicationData.setWebUrl(menu.getUrl().getWebUrl());
            }
            return applicationData;
        }).collect(Collectors.toList());

    }

}
