package com.facishare.webpage.customer.util.retry;

import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class RetryUtil {
    public static List<Integer> DEFAULT_RETRY_GRADIENT_MINUTE = Lists.newArrayList(1, 3, 7, 15, 30);
    public static ScheduledExecutorService executorService = Executors.newScheduledThreadPool(3);

    public static boolean addRetryTask(RetryMethod retryMethod, List<Integer> retryGradient, TimeUnit retryTimeUnit) {
        // 尝试当前线程执行
        boolean success = retryMethod.retry();
        if (!success) {
            RetryData retryData = new RetryData(retryMethod, retryGradient, retryTimeUnit);
            doRetry(retryData);
        }
        return success;
    }

    public static boolean addRetryTask(RetryMethod retryMethod) {
        return addRetryTask(retryMethod, DEFAULT_RETRY_GRADIENT_MINUTE, TimeUnit.MINUTES);
    }

    private static void doRetry(RetryData retryData) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                RetryMethod retryMethodTmp = retryData.getRetryMethod();
                boolean success = retryMethodTmp.retry();
                if (!success) {
                    if (!retryData.isFailure()) {
                        doRetry(retryData);
                    } else {
                        log.error("retry all gradient failure,retryData={}", retryData);
                    }
                }
            }
        };
        executorService.schedule(MonitorTaskWrapper.wrap(runnable), retryData.getNextRetryTimeAndIncrementRetry(), retryData.getRetryTimeUnit());
    }

    public static void main(String[] args) {
        RetryMethod retryMethod = () -> {
            System.out.println(new Date());
            System.out.println("1,2,3");
            return false;
        };
        addRetryTask(retryMethod, Lists.newArrayList(5), TimeUnit.SECONDS);
        System.out.println("aaaa");
    }
}
