package com.facishare.webpage.customer.component.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.objgroup.common.service.BizConfService;
import com.facishare.qixin.objgroup.common.service.model.resource.QueryComponentsResult;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.constant.TempleType;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.resource.UdObjRestResource;
import com.facishare.webpage.customer.common.resource.model.arg.FindAllPluginByApiNameArg;
import com.facishare.webpage.customer.component.ComponentService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.config.ComponentConfig;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.UIPaaSConfig;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.constant.ObjectLayoutTypeEnum;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.impl.TenantHomePageActionImpl;
import com.facishare.webpage.customer.core.business.ComponentListManager;
import com.facishare.webpage.customer.core.config.ApplicationLayeredConfig;
import com.facishare.webpage.customer.core.config.ComponentNameConfig;
import com.facishare.webpage.customer.core.config.PageTemplateConfig;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.ComponentTypConst;
import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.core.model.PageTemplateConfigVO;
import com.facishare.webpage.customer.model.component.*;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.facishare.webpage.customer.service.RemoteService;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.fxiaoke.common.Pair;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.constant.BizType.isWebsite;

/**
 * Created by zhangyu on 2020/6/3
 */
@Component
public class ComponentServiceImpl implements ComponentService {

    private static final Logger logger = LoggerFactory.getLogger(ComponentServiceImpl.class);

    @Autowired
    private ComponentListManager componentListManager;
    @Autowired
    private DefaultTenantConfig defaultTenantConfig;
    @Resource
    private ComponentNameConfig componentNameConfig;
    @Resource
    private RemoteService remoteService;
    @Autowired
    private LanguageService languageService;
    @Resource
    private ComponentConfig componentConfig;
    @Resource
    private AppMenuConfig appMenuConfig;
    @Resource
    private BizConfService bizConfService;
    @Resource
    private PageTemplateConfig pageTemplateConfig;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Resource
    private UIPaaSConfig uiPaaSConfig;

    @Resource
    private UdObjRestResource udObjRestResource;

    @Override
    public List<DropListItem> getDropListItemList(int tenantId,
                                                  int bizType,
                                                  String bizId,
                                                  List<DropListItem> businessComponents,    // 业务组件
                                                  List<DropListItem> cusComponents, // 自定义组件(用户管理后台开发的)
                                                  Locale locale,
                                                  String type,
                                                  String linkAppId) {
        //获取通用组件(配置中心定义的)
        List<DropListItem> generalDropList = getGeneralDropList(tenantId, bizType, bizId, locale, type, linkAppId);

        List<DropListItem> dropListItemList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(businessComponents)) {
            dropListItemList.addAll(businessComponents);
        }

        if (CollectionUtils.isNotEmpty(cusComponents)) {
            dropListItemList.addAll(cusComponents);
        }
        dropListItemList.addAll(generalDropList);
        dropListItemList = filterNoChildDropList(dropListItemList);
        // 处理所有组件的灰度
        List<String> components = appMenuConfig.getGrayComponentByDeviceType(TempleIdUtil.getCustomerAppId(bizType, bizId), type);
        dropListItemList = dropListItemList.stream().
                filter(x -> !components.contains(x.getId()) ||
                        (components.contains(x.getId()) &&
                                WebPageGraySwitch.isAllowComponentByType(tenantId, TempleIdUtil.getCustomerAppId(bizType, bizId), x.getId(), type))).
                collect(Collectors.toList());

        return dropListItemList;
    }

    @Override
    public boolean checkPrivilege(int appType,
                                  String appId,
                                  ComponentDto componentDto,
                                  Set<String> bizConfKeys, List<String> versionAndPackages,
                                  boolean needCheckPermission,
                                  List<String> openAppIds) {
        String objectApiName = TempleIdUtil.getObjectApiName(appType, appId);
        return checkPrivilege(appType, appId, componentDto, bizConfKeys, versionAndPackages, needCheckPermission, openAppIds,
                null, null, objectApiName);
    }

    @Override
    public boolean checkPrivilege(int bizType, String bizId, ComponentDto componentDto, Set<String> bizConfKeys,
                                  List<String> versionAndPackages, boolean needCheckPermission, List<String> openAppIds,
                                  String tenantId, String appId, String objectApiName) {
        boolean flag = true;

        if (componentDto.getTenantPrivilege() != null) {
            if (!checkTenantPrivilege(tenantId, bizType, bizId, appId, needCheckPermission, openAppIds,
                    objectApiName, componentDto.getTenantPrivilege(), bizConfKeys, versionAndPackages)) {
                flag = false;
            }
        }
        if (componentDto.getWidget() != null && componentDto.getWidget().getTenantPrivilege() != null) {
            if (!checkTenantPrivilege(tenantId, bizType, bizId, appId, needCheckPermission,
                    openAppIds, objectApiName, componentDto.getWidget().getTenantPrivilege(), bizConfKeys, versionAndPackages)) {
                flag = false;
            }
        }

        return flag;
    }

    @Override
    public List<JSONObject> getComponents(int tenantId, int appType, String appId, Locale locale) {

        List<DropListItem> appWidgetDropList = getGeneralDropList(tenantId, appType, appId, locale, TempleType.APP, appId);

        appWidgetDropList = appWidgetDropList.stream().filter(dropListItem -> ComponentConstant.dropListCompType.equals(dropListItem.getType())).collect(Collectors.toList());

        List<DropListItem> businessComponents = getBusinessComponents(tenantId, appType, appId, locale);

        List<DropListItem> dropListItems = Lists.newArrayList();
        dropListItems.addAll(businessComponents);
        dropListItems.addAll(appWidgetDropList);

        return dropListItems.stream().
                filter(dropListItem -> !componentConfig.getAppShowComponents().contains(dropListItem.getId())).
                map(dropListItem -> dropListItem.getComponent()).
                collect(Collectors.toList());
    }

    private Pair<String, String> getWebSiteConfigAppId(String bizId, int bizType, String appId, String type, int tenantId) {

        String customerAppId = "";
        String configKey = "";
        BizType bizTypeValue = BizType.getBizTypeValue(bizType);    // 真正的页面类型
        if (bizTypeValue == null) {
            return new Pair<>(configKey, customerAppId);
        }

        // 该页面属于特殊页面, 该应用的特殊页面灰度使用本应用独立的配置,且配置存在
        configKey = String.join(Constant.SEPARATOR, bizTypeValue.getDefaultAppId(), appId, type);
        if (BizType.isSpecialWebsite(bizType) && !componentListManager.isComponentEmpty(configKey)) {
            customerAppId = appId;
            return new Pair<>(configKey, customerAppId);
        }

        configKey = String.join(Constant.SEPARATOR, bizTypeValue.getDefaultAppId(), type);
        // 该页面属于特殊页面, 使用这类页面的通用配置, 且配置存在
        if (BizType.isSpecialWebsite(bizType) && !componentListManager.isComponentEmpty(configKey)) {
            customerAppId = bizTypeValue.getDefaultAppId();
            return new Pair<>(configKey, customerAppId);
        }

        configKey = String.join(Constant.SEPARATOR, BizType.WEBSITE.getDefaultAppId(), appId, type);
        // 该应用的站点页面灰度使用应用单独的, 且存在这样的配置
        if (!componentListManager.isComponentEmpty(configKey)) {
            customerAppId = appId;
            return new Pair<>(configKey, customerAppId);
        }

        // C端门户页面通用的drop
        configKey = String.join(Constant.SEPARATOR, BizType.WEBSITE.getDefaultAppId(), type);
        customerAppId = BizType.WEBSITE.getDefaultAppId();
        return new Pair<>(configKey, customerAppId);
    }

    /**
     * @param bizId    {@link TenantHomePageActionImpl#getBizId(int, String)} 一般就是 {@link com.facishare.webpage.customer.api.constant.BizType} 的 defaultAppId
     * @param bizType  {@link com.facishare.webpage.customer.api.constant.BizType} 的 type
     * @param appId    区别于bizId可能对某一类进行聚合(比如互联自定义应用), 表示真正的应用id
     * @param type     枚举, 但是目前app的配置以及迁移到userExt, 这里只有web适用 {@link TempleType}
     * @param tenantId
     * @return 配置文件fs-webpage-customer-web-component-list-map的 < key, 拼key的customerAppId >
     */
    private Pair<String, String> getConfigAppId(String bizId, int bizType, String appId, String type, int tenantId) {
        String configAppId = bizId;
        if (TempleType.WEB.equals(type)) {   // app端drop目前走user-extension
            logger.info("start configAppId, bizId: {}, bizType: {}, appId: {}, type: {}", bizId, bizType, appId, type);
        }
        if (bizType == AppTypeEnum.CUSTOMER_LINK_APP.getAppType()) {
            configAppId = WebPageConstants.CROSS_PaaS;
        }
        String customerAppId = "";
        String configKey = "";
        if (!isWebsite(bizType)) {
            customerAppId = TempleIdUtil.getCustomerAppId(bizType, configAppId);
            if (uiPaaSConfig.checkUseV3DropList(customerAppId)
                    && WebPageGraySwitch.isAllowByBusiness(
                    String.join(Constant.SEPARATOR, WebPageGraySwitch.USE_V3_DROP_LIST, customerAppId), tenantId)) {
                configKey = String.join(Constant.SEPARATOR, customerAppId, type, "V3");
            } else {
                configKey = String.join(Constant.SEPARATOR, customerAppId, type);
            }
        } else {
            Pair<String, String> pair = getWebSiteConfigAppId(bizId, bizType, appId, type, tenantId);
            configKey = pair.getT1();
            customerAppId = pair.getT2();
        }

        if (TempleType.WEB.equals(type)) {
            logger.info("end configKey: {}, customerAppId: {}", configKey, customerAppId);
        }
        return new Pair<>(configKey, customerAppId);
    }

    private List<DropListItem> getGeneralDropList(int tenantId, int bizType, String bizId, Locale locale, String type,
                                                  String appId) {
        Pair<String, String> pair = getConfigAppId(bizId, bizType, appId, type, tenantId);
        String configKey = pair.getT1();
        String customerAppId = pair.getT2();

        List<ComponentDto> componentDtoList = componentListManager.getComponentDtoListByAppId(configKey);
        if (bizType == 4 && bizId.indexOf("_") > 0) {   // 对象布局, 它的bizType和自定义页面一样
            int index = bizId.indexOf("_");
            String objectApiName = bizId.substring(index + 1);
            componentDtoList = filterComponentObjectPlugins(componentDtoList, objectApiName, tenantId);
        }
        //变更场景appId
        List<ComponentDto> newComponentDtoList = new ArrayList<>();
        if (bizType == AppTypeEnum.CUSTOMER_LINK_APP.getAppType()) {
            List<ComponentDto> finalNewComponentDtoList = newComponentDtoList;
            componentDtoList.forEach(x -> {
                ComponentDto componentDto = new ComponentDto();
                BeanUtils.copyProperties(x, componentDto);
                if (Objects.nonNull(componentDto.getTenantPrivilege()) && StringUtils.equals(WebPageConstants.CROSS_PaaS, componentDto.getTenantPrivilege().getAppId())) {
                    TenantPrivilege tenantPrivilege = new TenantPrivilege();
                    tenantPrivilege.setBizConfKeys(x.getTenantPrivilege().getBizConfKeys());
                    tenantPrivilege.setObjectApiName(x.getTenantPrivilege().getObjectApiName());
                    tenantPrivilege.setEnableObjects(x.getTenantPrivilege().getEnableObjects());
                    tenantPrivilege.setLicenseModuleCodes(x.getTenantPrivilege().getLicenseModuleCodes());
                    tenantPrivilege.setNotSupportSource(x.getTenantPrivilege().getNotSupportSource());
                    tenantPrivilege.setDisEnableObjects(x.getTenantPrivilege().getDisEnableObjects());
                    tenantPrivilege.setLicenseProductCodes(x.getTenantPrivilege().getLicenseProductCodes());
                    tenantPrivilege.setAppId(appId);
                    componentDto.setTenantPrivilege(tenantPrivilege);
                }
                finalNewComponentDtoList.add(componentDto);
            });
            newComponentDtoList = finalNewComponentDtoList;
        } else {
            newComponentDtoList = componentDtoList;
        }
        // 与应用不关联的组件灰度

        // 配置中心组件的灰度
        List<String> grayComponentByAppId = appMenuConfig.getGrayComponentByAppId(customerAppId);
        newComponentDtoList = newComponentDtoList.stream()
                .filter(componentDto -> appMenuConfig.isComponentGray(componentDto.getId(), String.valueOf(tenantId)))  // 按照组件维度直接灰度
                .filter(componentDto -> !grayComponentByAppId.contains(componentDto.getId()) ||
                        (grayComponentByAppId.contains(componentDto.getId()) && WebPageGraySwitch.isAllowComponentByBusiness(componentDto.getId(), tenantId)))
                .collect(Collectors.toList());

        Map<String, String> componentLanguage = languageService.queryComponentLanguage(tenantId, newComponentDtoList, locale);
        //自定义互联应用需要获取当前应用的appId
        //获取web端的配置组件 ComponentDto -> DropListItem 同时有权限判断
        List<DropListItem> generalDropList = getGeneralAndTabsComponents(tenantId, bizType, bizId, appId, newComponentDtoList, componentLanguage);

        return generalDropList;
    }

    private List<ComponentDto> filterComponentObjectPlugins(List<ComponentDto> componentDtoList, String apiName, int tenantId) {

        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", String.valueOf(tenantId));
        headers.put("X-fs-Enterprise-Id", String.valueOf(tenantId));
        FindAllPluginByApiNameArg findAllPluginByApiNameArg = new FindAllPluginByApiNameArg();
        findAllPluginByApiNameArg.setObjectApiName(apiName);
        JSONObject result = udObjRestResource.getObjectPlugins(headers, findAllPluginByApiNameArg);
        List<String> plugins = result.getJSONObject("data").getJSONArray("pluginApiNames").toJavaList(String.class);
        componentDtoList = componentDtoList.stream().filter(x -> checkObjectPlugins(x, plugins)).collect(Collectors.toList());
        return componentDtoList;
    }

    private boolean checkObjectPlugins(ComponentDto componentDto, List<String> plugins) {
        if (Objects.isNull(componentDto.getWidget())) {
            return true;
        }
        TenantPrivilege tenantPrivilege = componentDto.getWidget().getTenantPrivilege();
        if (Objects.isNull(tenantPrivilege)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(tenantPrivilege.getObjectPlugins())) {
            return CollectionUtils.containsAny(tenantPrivilege.getObjectPlugins(), plugins);
        }
        return true;
    }

    private boolean checkTenantPrivilege(String tenantId, int bizType, String bizId, String appId,
                                         boolean needCheckPermission,
                                         List<String> openAppIds,
                                         String objectApiName,
                                         TenantPrivilege tenantPrivilege,
                                         Set<String> bizConfKeys, List<String> versionAndPackages) {

        if (tenantPrivilege == null) {
            return true;
        }

        String tenantPrivilegeAppId = tenantPrivilege.getAppId();
        if (needCheckPermission && StringUtils.isNotEmpty(tenantPrivilegeAppId) && !openAppIds.contains(tenantPrivilegeAppId)) {
            return false;
        }
        if (checkWithAppId(tenantId, bizType, bizId, appId, tenantPrivilegeAppId)) {
            return false;
        }

        if (!CollectionUtils.isEmpty(tenantPrivilege.getLicenseProductCodes())
                && !CollectionUtils.containsAny(tenantPrivilege.getLicenseProductCodes(), versionAndPackages)) {
            return false;
        }

        if (!CollectionUtils.isEmpty(tenantPrivilege.getLicenseModuleCodes())
                && !CollectionUtils.containsAny(tenantPrivilege.getLicenseModuleCodes(), versionAndPackages)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(tenantPrivilege.getBizConfKeys())
                && !CollectionUtils.containsAny(tenantPrivilege.getBizConfKeys(), bizConfKeys)) {
            return false;
        }

        //objectApiName为null的时候，不对对象进行校验，直接返回
        if (StringUtils.isEmpty(objectApiName)) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(tenantPrivilege.getDisEnableObjects()) &&
                checkDisEnableObjs(tenantPrivilege.getDisEnableObjects(), objectApiName)) {
            return false;
        }

        if (CollectionUtils.isNotEmpty(tenantPrivilege.getEnableObjects()) &&
                checkEnableObjs(tenantPrivilege.getEnableObjects(), objectApiName)) {
            return false;
        }

        return true;
    }

    private boolean checkWithAppId(String tenantId, int bizType, String bizId, String appId, String tenantPrivilegeAppId) {
        if (StringUtils.isEmpty(tenantId) || BizType.CUSTOMER.getType() != bizType) {
            return false;
        }
        // 通过 bizType、bizId 和 appId 联合判断走的是新逻辑还是旧逻辑
        String webPageKey = TempleIdUtil.getPrefixByAppId(bizType, bizId);
        if (StringUtils.isEmpty(ObjectLayoutTypeEnum.getBusinessKeyByWebPageKey(webPageKey)) || Objects.equals(bizId, appId)) {
            return false;
        }
        if (StringUtils.isEmpty(tenantPrivilegeAppId)) {
            return false;
        }
        // 判断组件配置的 appId 是否灰度当前企业
        if (!ApplicationLayeredConfig.isAllow(tenantPrivilegeAppId, tenantId)) {
            return true;
        }
        return !Objects.equals(appId, tenantPrivilegeAppId);
    }

    private boolean checkDisEnableObjs(List<String> apiNames, String objectApiName) {
        if (apiNames.contains(objectApiName)) {
            return true;
        }
        if (objectApiName.endsWith("__c")) {
            objectApiName = "udobj";
        }
        return apiNames.contains(objectApiName);
    }

    private boolean checkEnableObjs(List<String> apiNames, String objectApiName) {
        if (apiNames.contains(objectApiName)) {
            return false;
        }
        if (objectApiName.endsWith("__c")) {
            objectApiName = "udobj";
        }
        return !apiNames.contains(objectApiName);
    }

    @Override
    public List<DropListItem> getGeneralAndTabsComponents(int tenantId, int appType, String appId, List<ComponentDto> componentDtoList, Map<String, String> componentLanguage) {
        return getGeneralAndTabsComponents(tenantId, appType, appId, appId, componentDtoList, componentLanguage);
    }

    @Override
    public List<DropListItem> getGeneralAndTabsComponents(int tenantId, int bizType, String bizId, String appId, List<ComponentDto> componentDtoList, Map<String, String> componentLanguage) {
        if (CollectionUtils.isEmpty(componentDtoList)) {
            return Lists.newArrayList();
        }

        List<String> defaultTenantIds = this.defaultTenantConfig.getDefaultTenantConfig();

        List<String> versionAndPackages = remoteService.getVersionAndPackages(tenantId);

        PageTemplateConfigVO pageTemplateConfig = this.pageTemplateConfig.getPageTemplateConfig(appId); // 部分预置应用的模式定义
        boolean needCheckPermission = false;
        List<String> openAppIds = Lists.newArrayList();
        if (pageTemplateConfig != null && pageTemplateConfig.isCrossApp()) {
            openAppIds = remoteCrossService.getUpSimpleLinkApp(tenantId).stream().map(SimpleLinkAppResult::getAppId).collect(Collectors.toList());
            needCheckPermission = true;
        }

        Set<String> bizConfKeys = getBizConfKeys(componentDtoList);

        Map<String, String> bizConfMap = bizConfService.batchQueryConfig(tenantId, bizConfKeys);
        Set<String> newBizConfKeys = bizConfMap.keySet();

        boolean finalNeedCheckPermission = needCheckPermission;
        List<String> finalOpenAppIds = openAppIds;
        String objectApiName = TempleIdUtil.getObjectApiName(bizType, bizId);
        String tenantStr = String.valueOf(tenantId);
        return componentDtoList.stream().map(componentDto -> {
            DropListItem dropListItem = buildGeneralAndTabsComponents(objectApiName, componentDto, componentLanguage);

            if ((BizType.CRM.getType() == bizType && defaultTenantIds.contains(tenantId))) {
                return dropListItem;
            }

            boolean checkPrivilege = checkPrivilege(bizType, bizId, componentDto, newBizConfKeys, versionAndPackages,
                    finalNeedCheckPermission, finalOpenAppIds, tenantStr, appId, objectApiName);

            if (checkPrivilege) {
                return dropListItem;
            }
            return null;
        }).filter(dropListItem -> dropListItem != null).collect(Collectors.toList());
    }

    @Override
    public List<DropListItem> getCusComponents(Integer enterpriseId,
                                               Integer appType,
                                               String appId,
                                               List<QueryComponentsResult.ComponentData> componentDataList,
                                               String clientType) {
        if (CollectionUtils.isEmpty(componentDataList)) {
            componentDataList = remoteService.getCusComponentList(enterpriseId, appType, appId);
        }
        return covertCusComponentByClient(componentDataList, clientType, TempleIdUtil.getCustomerAppId(appType, appId));
    }

    /**
     * 根据端对自定义组件进行过滤
     *
     * @param componentDataList
     * @param clientType
     * @param appId
     * @return
     */
    private List<DropListItem> covertCusComponentByClient(List<QueryComponentsResult.ComponentData> componentDataList, String clientType, String appId) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(componentDataList)) {
            return Lists.newArrayList();
        }
        componentDataList = componentDataList.stream().
                filter(componentData -> componentData.getClientTypes().contains(clientType)).
                collect(Collectors.toList());
        return componentDataList.stream().map(componentData -> {
            ComponentCustomer componentCustomer = new ComponentCustomer();
            componentCustomer.setComponentData(componentData);
            componentCustomer.setAppId(appId);
            return componentCustomer.buildDropListItem();
        }).collect(Collectors.toList());
    }

    private Set<String> getBizConfKeys(List<ComponentDto> componentDtoList) {
        if (CollectionUtils.isEmpty(componentDtoList)) {
            return Sets.newHashSet();
        }
        Set<String> bizConfKeys = Sets.newHashSet();
        componentDtoList.stream().forEach(x -> {
            if (x.getTenantPrivilege() != null && CollectionUtils.isNotEmpty(x.getTenantPrivilege().getBizConfKeys())) {
                bizConfKeys.addAll(x.getTenantPrivilege().getBizConfKeys());
                return;
            }
            if (x.getWidget() != null && x.getWidget().getTenantPrivilege() != null && CollectionUtils.isNotEmpty(x.getWidget().getTenantPrivilege().getBizConfKeys())) {
                bizConfKeys.addAll(x.getWidget().getTenantPrivilege().getBizConfKeys());
            }
        });
        return bizConfKeys;
    }

    private DropListItem buildGeneralAndTabsComponents(String objectApiName, ComponentDto componentDto, Map<String, String> componentLanguage) {

        if (ComponentTypConst.WIDGET_TYPE == componentDto.getComponentType()
                && componentDto.getWidget() != null
                && componentDto.getWidget().getWidgetType() == 402) {
            return getTabsComponent(componentDto, componentLanguage);
        } else if (ComponentTypConst.WIDGET_TYPE == componentDto.getComponentType()
                && componentDto.getWidget() != null
                && componentDto.getWidget().getWidgetType() == 403) {
            return getNavigateComponent(componentDto, componentLanguage);
        } else if (ComponentTypConst.WIDGET_TYPE == componentDto.getComponentType()
                && componentDto.getWidget() != null
                && componentDto.getWidget().getWidgetType() == 405) {
            return getGridComponent(componentDto, componentLanguage);
        } else {
            return getGeneralComponent(objectApiName, componentDto, componentLanguage);
        }
    }

    private DropListItem getGridComponent(ComponentDto componentDto, Map<String, String> componentLanguage) {
        ComponentGrid componentGrid = new ComponentGrid();
        componentGrid.setComponentDto(componentDto);
        componentGrid.setComponentLanguage(componentLanguage);
        return componentGrid.buildDropListItem();
    }

    private DropListItem getNavigateComponent(ComponentDto componentDto, Map<String, String> componentLanguage) {
        ComponentNavigate componentNavigate = new ComponentNavigate();
        componentNavigate.setComponentDto(componentDto);
        componentNavigate.setComponentLanguage(componentLanguage);
        return componentNavigate.buildDropListItem();
    }

    private DropListItem getGeneralComponent(String objectApiName, ComponentDto componentDto, Map<String, String> componentLanguage) {

        ComponentWidget componentWidget = new ComponentWidget();
        componentWidget.setComponentDto(componentDto);
        componentWidget.setComponentLanguage(componentLanguage);
        componentWidget.setObjectApiName(objectApiName);
        componentWidget.setComponentNameConfig(componentNameConfig);

        DropListItem dropListItem = componentWidget.buildDropListItem();
        return dropListItem;
    }

    private DropListItem getTabsComponent(ComponentDto componentDto, Map<String, String> componentLanguage) {
        ComponentTabs componentTabs = new ComponentTabs();
        componentTabs.setComponentDto(componentDto);
        componentTabs.setComponentLanguage(componentLanguage);
        componentTabs.setComponentNameConfig(componentNameConfig);
        return componentTabs.buildDropListItem();
    }

    @Override
    public List<DropListItem> getBusinessComponents(int tenantId, int appType, String appId, Locale locale) {

        if (BizType.CUSTOMER.getType() != appType
                || StringUtils.isEmpty(appId)
                || StringUtils.isEmpty(TempleIdUtil.getObjectApiName(appType, appId))) {
            return Lists.newArrayList();
        }

        List<JSONObject> businessComponents = remoteService.getBusinessComponents(tenantId, appType, appId, locale);

        if (CollectionUtils.isEmpty(businessComponents)) {
            return Lists.newArrayList();
        }
        return businessComponents.stream().map(busComponent -> {
            DropListItem businessDropList = buildBusinessComponent(busComponent);
            return businessDropList;
        }).collect(Collectors.toList());

    }

    private DropListItem buildBusinessComponent(JSONObject busComponent) {
        ComponentBusiness componentBusiness = new ComponentBusiness();
        componentBusiness.setBusComponent(busComponent);

        return componentBusiness.buildDropListItem();
    }

    @Override
    public List<DropListItem> filterNoChildDropList(List<DropListItem> dropListItemList) {
        List<String> pIds = dropListItemList.stream()
                .map(dropListItem -> dropListItem.getParentId())
                .collect(Collectors.toList());
        List<String> groupIds = dropListItemList.stream()
                .filter(dropListItem -> ComponentConstant.dropListGroupType.equals(dropListItem.getType()))
                .map(dropListItem -> dropListItem.getId())
                .collect(Collectors.toList());
        List<String> removeGroupIds = (List<String>) CollectionUtils.subtract(groupIds, pIds);
        // 过滤掉没有父节点的数据 和 没有数据的父节点
        return dropListItemList.stream().
                filter(dropListItem -> !removeGroupIds.contains(dropListItem.getId())).
                filter(x -> ComponentConstant.dropListGroupType.equals(x.getType()) ||
                        (ComponentConstant.dropListCompType.equals(x.getType()) && groupIds.contains(x.getParentId()))).collect(Collectors.toList());

    }

}
