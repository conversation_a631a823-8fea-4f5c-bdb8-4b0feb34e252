package com.facishare.webpage.customer.constant;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/11/13
 */
public enum ChecMaxPageNumEnum {

    /**
     * 个人首页
     */
    HOME_PAGE(1, "个人首页", "maxPersonCount"),
    /**
     * 个人页面模板
     */
    USER_PAGE_TTEMPLET(2, "个人页面模板", "maxUserPageTemplateCount");

    private int pageType;
    private String pageName;
    private String configAttr;

    ChecMaxPageNumEnum(int pageType, String pageName, String configAttr) {
        this.pageType = pageType;
        this.pageName = pageName;
        this.configAttr = configAttr;
    }

    public int getPageType() {
        return pageType;
    }

    public String getPageName() {
        return pageName;
    }

    public String getConfigAttr() {
        return configAttr;
    }

    public static ChecMaxPageNumEnum getAppTypeEnum(int pageType) {
        for (ChecMaxPageNumEnum checMaxPageNumEnum : ChecMaxPageNumEnum.values()) {
            if (pageType == checMaxPageNumEnum.getPageType()) {
                return checMaxPageNumEnum;
            }
        }
        return null;
    }

}
