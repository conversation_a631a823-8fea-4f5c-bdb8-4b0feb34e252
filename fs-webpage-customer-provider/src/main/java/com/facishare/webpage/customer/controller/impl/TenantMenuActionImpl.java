package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum;
import com.facishare.qixin.permission.ValidateFunctionPermission;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.IndexIcon;
import com.facishare.webpage.customer.api.model.core.PreObject;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.config.AddMenuItemConfig;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.constant.MenuStatus;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.TenantMenuAction;
import com.facishare.webpage.customer.controller.model.*;
import com.facishare.webpage.customer.controller.model.arg.menu.*;
import com.facishare.webpage.customer.controller.model.result.menu.*;
import com.facishare.webpage.customer.core.config.ObjectConfig;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.facishare.webpage.customer.event.WebPageEventService;
import com.facishare.webpage.customer.metadata.MetaMenuService;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.*;
import com.facishare.webpage.customer.remote.RoleNameService;
import com.facishare.webpage.customer.service.HomePageBaseService;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.facishare.webpage.customer.service.SearchWordsService;
import com.facishare.webpage.customer.service.TenantMenuService;
import com.facishare.webpage.customer.system.MenuSystemService;
import com.facishare.webpage.customer.util.MenuObjUtil;
import com.facishare.webpage.customer.util.MenuUtil;
import com.facishare.webpage.customer.util.TenantMenuDataUtil;
import com.facishare.webpage.customer.util.TenantMenuTempleUtil;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhangyi on 2019/9/9.
 */
@Controller
@Slf4j
@RequestMapping({"/tenantMenu", "API/v1/object/crm_menu_admin/service", "API/v1/object/crm_menu_init/service"})
public class TenantMenuActionImpl implements TenantMenuAction {
    private static final Logger logger = LoggerFactory.getLogger(TenantMenuActionImpl.class);

    @Autowired
    private TenantMenuService tenantMenuService;
    @Resource
    private ObjectConfig objectConfig;
    @Autowired
    private RoleNameService roleNameService;
    @Resource
    private LanguageService languageService;
    @Resource
    private MenuObjUtil menuObjUtil;
    @Resource
    private HomePageBaseService homePageBaseService;
    @Resource
    private MetaMenuService metaMenuService;
    @Resource
    private AddMenuItemConfig addMenuItemConfig;
    @Resource
    private SearchWordsService searchWordsService;
    @Resource
    private OrganizationCommonService organizationCommonService;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Resource
    private WebPageEventService webPageEventService;
    @Resource
    private I18nService i18nService;
    @Resource
    private MenuSystemService menuSystemService;
    @Resource
    private CheckService checkService;

    @Override
    @RequestMapping(value = "getTenantMenuDetailById", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public GetTenantMenuByIdResult getTenantMenuDetailById(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetTenantMenuDetailByIdArg arg) {
        arg.valid();
        int enterpriseId = userInfo.getEnterpriseId();
        MenuTempleAO menuTempleAO = tenantMenuService.findTenantMenuById(DataSourceEnv.CROSS, arg.getMenuTempleId(), enterpriseId, clientInfo.getLocale(), false);
        MenuTemple menuTemple = menuTempleAO.getMenuTemple();
        List<TenantMenuItemVO> tenantMenuItemVO = buildTenantMenuItemVOList(userInfo.getEnterpriseId(), menuTemple, clientInfo);
        GetTenantMenuByIdResult result = new GetTenantMenuByIdResult();
        result.setDealHomeFlag(menuTemple.getDealHomeFlag());
        result.setTenantMenuItemVOS(tenantMenuItemVO);
        result.setIsShowMenuIcon(menuTemple.getIsShowMenuIcon());
        result.setHiddenQuickCreate(menuTemple.getHiddenQuickCreate());
        return result;
    }

    @Override
    @RequestMapping(value = "getCrossMenuById", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public GetCrossMenuByIdResult getCrossMenuById(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetCrossMenuByIdArg arg) {
        try {
            arg.valid();
            RequestContextManager.initContextForIsFromManage(true);

            GetTenantMenuDetailByIdArg tenantMenuDetailByIdArg = new GetTenantMenuDetailByIdArg();
            tenantMenuDetailByIdArg.setMenuTempleId(arg.getMenuTempleId());
            GetTenantMenuByIdResult tenantMenuByIdResult = getTenantMenuDetailById(userInfo, clientInfo, tenantMenuDetailByIdArg);

            List<SimpleLinkAppResult> upSimpleLinkApp = remoteCrossService.getUpSimpleLinkApp(userInfo.getEnterpriseId(), clientInfo.getLocale());
            List<String> openAppIds = upSimpleLinkApp.stream().map(x -> x.getAppId()).collect(Collectors.toList());
            openAppIds.add(WebPageConstants.CROSS_PaaS);
            openAppIds.add(WebPageConstants.CROSS_PORTAL_PAGE);

            List<TenantMenuItemVO> tenantMenuItemVOS = tenantMenuByIdResult.getTenantMenuItemVOS().stream().
                    filter(x -> x.getIsHidden() != null && !x.getIsHidden()).
                    filter(x -> MenuType.GROUP.equals(x.getType())
                            || openAppIds.contains(x.getAppId())).
                    collect(Collectors.toList());

            GetCrossMenuByIdResult result = new GetCrossMenuByIdResult();
            result.setTenantMenuItemVOS(tenantMenuItemVOS);
            result.setIsShowMenuIcon(tenantMenuByIdResult.getIsShowMenuIcon());
            result.setHiddenQuickCreate(tenantMenuByIdResult.getHiddenQuickCreate());
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }

    @Override
    @RequestMapping(value = "getTenantDefaultMenuDetailByAppId", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public GetTenantDefaultMenuDetailByAppIdResult getTenantDefaultMenuDetailByAppId(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody(required = false) GetTenantDefaultMenuDetailByAppIdArg arg) {
        String appId = WebPageConstants.APP_CRM;
        if (ObjectUtils.isNotEmpty(arg)) {
            arg.valid();
            appId = arg.getAppId();
        }
        int enterpriseId = userInfo.getEnterpriseId();
        List<MetaMenuData> metaMenuDataList = metaMenuService.getMetaMenuList(DataSourceEnv.values(arg.isInner()), enterpriseId, appId, clientInfo.getLocale(), false);
        if (WebPageConstants.APP_CRM.equals(appId)) {
            metaMenuDataList = menuObjUtil.filterNeedHiddenMetaMenuData(enterpriseId, metaMenuDataList, false);
        }
        metaMenuDataList = metaMenuDataList.stream().filter(metaMenuData -> !StringUtils.equals(MenuType.PAGE_TEMPLATE, metaMenuData.getMenuType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metaMenuDataList)) {
            logger.warn("getDefaultMenu return null by userInfo {},arg {}", userInfo, arg);
            throw new WebPageException(InterErrorCode.MENU_DEFAULT_NOT_FUND);
        }
        List<String> menuNames = metaMenuDataList.stream().map(metaMenuData -> metaMenuData.getName()).collect(Collectors.toList());
        Map<String, List<String>> searchWordsMap = searchWordsService.getSearchWords(menuNames);
        GetTenantDefaultMenuDetailByAppIdResult result = new GetTenantDefaultMenuDetailByAppIdResult();
        result.setDefaultMenuVOS(TenantMenuDataUtil.buildDefaultMenuVos(metaMenuDataList, searchWordsMap));
        return result;
    }


    @Override
    @RequestMapping(value = "menu_item_list", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public List<DefaultMenuVO> menuItemList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody(required = false) MenuItemListArg arg) {

        GetTenantDefaultMenuDetailByAppIdArg detailByAppIdArg = new GetTenantDefaultMenuDetailByAppIdArg();
        detailByAppIdArg.setAppId(WebPageConstants.APP_CRM);
        detailByAppIdArg.setInner(true);
        GetTenantDefaultMenuDetailByAppIdResult result = getTenantDefaultMenuDetailByAppId(userInfo, clientInfo, detailByAppIdArg);

        return result.getDefaultMenuVOS();
    }

    @Override
    @RequestMapping(value = "view", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public MenuViewResult menuView(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody MenuViewArg arg) {
        arg.valid();
        if (null == clientInfo.getLocale()) {
            clientInfo.setLocale(Locale.CHINESE);
            log.error("menuView.clientInfo.getLocale() is null, set Locale.CHINESE");
        }
        int enterpriseId = userInfo.getEnterpriseId();
        MenuTempleAO menuTempleAO = tenantMenuService.findTenantMenuById(DataSourceEnv.INNER, arg.getMenuId(), enterpriseId, clientInfo.getLocale(), arg.isPreviewNewCrmFlag());
        MenuTemple menuTemple = menuTempleAO.getMenuTemple();
        List<MetaMenuData> metaMenuDataList = menuTempleAO.getMetaMenuDataList();
        List<TenantMenuItemVO> tenantMenuItemVO = getTenantMenuItemVO(enterpriseId, menuTemple, metaMenuDataList, clientInfo, arg.isPreviewNewCrmFlag());

        MenuViewResult result = new MenuViewResult();
        result.setDetailMenuItems(tenantMenuItemVO);
        MenuTempleVO menuTempleVO = new MenuTempleVO();
        menuTempleVO.setMenuId(menuTemple.getMenuId());
        menuTempleVO.setName(menuTemple.getName());
        menuTempleVO.setDescription(menuTemple.getDescription());
        menuTempleVO.setIsSystem(menuTemple.isSystem());
        menuTempleVO.setApiName(menuTemple.getMenuId());
        menuTempleVO.setActiveStatus(String.valueOf(menuTemple.getStatus()));
        menuTempleVO.setIsShowMenuIcon(menuTemple.getIsShowMenuIcon());
        menuTempleVO.setHiddenQuickCreate(menuTemple.getHiddenQuickCreate());
        Boolean dealHomeFlag = menuTemple.getDealHomeFlag();
        if (!Constant.APP_CRM.equals(menuTemple.getAppId())) {
            dealHomeFlag = true;
        }
        menuTempleVO.setDealHomeFlag(dealHomeFlag);
        result.setObjectData(menuTempleVO);
        result.setScopeList(menuTemple.getScopeList());
        return result;
    }

    @Override
    @RequestMapping(value = "getMenuById", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public GetMenuByIdResult getMenuById(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetMenuByIdArg arg) {
        try {
            arg.valid();
            RequestContextManager.initContextForIsFromManage(true);
            MenuViewArg menuViewArg = new MenuViewArg();
            menuViewArg.setMenuId(arg.getMenuId());
            menuViewArg.setPreviewNewCrmFlag(arg.isPreviewNewCrmFlag());
            if (null == clientInfo.getLocale()) {
                clientInfo.setLocale(Locale.CHINESE);
                log.error("getMenuById.clientInfo.getLocale() is null, set Locale.CHINESE");
            }
            MenuViewResult menuViewResult = menuView(userInfo, clientInfo, menuViewArg);
            List<TenantMenuItemVO> tenantMenuItemVOS = menuViewResult.getDetailMenuItems();
            if (arg.isPreviewNewCrmFlag()) {
                //追加首页
                tenantMenuItemVOS.add(0, MenuUtil.getDefaultIndexHome(Constant.APP_CRM));
            } else if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.useDealHomeFlagGrayEi, userInfo.getEnterpriseId())
                    && !checkService.checkGoNewCRM(userInfo.getEnterpriseId())) {
                MenuUtil.addNewMenuByTenantMenuItemVO(tenantMenuItemVOS, Lists.newArrayList(MenuUtil.getDefaultIndexHome(Constant.APP_CRM),
                        MenuUtil.getDefaultRecent(), MenuUtil.getDefaultCrmRemind(), MenuUtil.getDefaultCrmToDo()));
            }
            tenantMenuItemVOS = tenantMenuItemVOS.stream().
                    filter(x -> BooleanUtils.isNotTrue(x.getIsHidden())).collect(Collectors.toList());

            GetMenuByIdResult result = new GetMenuByIdResult();
            result.setDetailMenuItems(tenantMenuItemVOS);
            result.setObjectData(menuViewResult.getObjectData());
            result.setScopeList(menuViewResult.getScopeList());
            result.setRoleIdList(menuViewResult.getRoleIdList());

            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }


    @Override
    @RequestMapping(value = "menu_list", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public MenuListResult menuList(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody(required = false) MenuListArg arg) {
        try {
            RequestContextManager.initContextForIsFromManage(arg.isFormManagerFlag());
            int enterpriseId = userInfo.getEnterpriseId();
            String appId;
            if (arg == null || StringUtils.isEmpty(arg.getAppId())) {
                appId = WebPageConstants.APP_CRM;
            } else {
                arg.valid();
                appId = arg.getAppId();
            }
            Locale locale = Objects.isNull(clientInfo.getLocale()) ? Locale.CHINA : clientInfo.getLocale();
            List<MenuTemple> menuTemples = tenantMenuService.getMenuTempleList(enterpriseId, appId, Lists.newArrayList(), locale);
            if (CollectionUtils.isNotEmpty(menuTemples)) {
                menuTemples = menuTemples.stream()
                        .filter(x -> x.getStatus() > MenuStatus.tempStatus)
                        .collect(Collectors.toList());
            }
            //非PaaS应用，需要设置适用范围名称、创建人名称、更新人名称
            if (!WebPageUtils.checkPaaSApp(appId)) {
                List<Scope> scopeList = queryAllScope(menuTemples);
                Map<Integer, Object> scopeNameMap = organizationCommonService.getScopeName(enterpriseId, appId, scopeList, null);
                menuTemples = addEmployeeName(menuTemples, scopeNameMap);
                menuTemples = addRoleName(scopeNameMap, menuTemples);
                setScopeNameList(menuTemples, scopeNameMap);
            }
            MenuListResult result = new MenuListResult();
            result.setMenuTempleVOS(TenantMenuTempleUtil.buildMenuTempleVOS(menuTemples));
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }


    private List<Scope> queryAllScope(List<MenuTemple> menuTempleList) {
        if (CollectionUtils.isEmpty(menuTempleList)) {
            return Lists.newArrayList();
        }
        List<Scope> scopeList = Lists.newArrayList();
        menuTempleList.stream().forEach(x -> {
            List<Scope> employeeScopes = ScopesUtil.
                    buildScope(ScopeType.Employee.getType(), Lists.newArrayList(String.valueOf(x.getCreatorId()), String.valueOf(x.getUpdaterId())));
            scopeList.addAll(employeeScopes);
            if (CollectionUtils.isNotEmpty(x.getScopeList())) {
                scopeList.addAll(x.getScopeList());
            }
        });
        return scopeList;
    }

    private void setScopeNameList(List<MenuTemple> tenantMenuTos, Map<Integer, Object> scopeNameMap) {
        if (CollectionUtils.isEmpty(tenantMenuTos)) {
            return;
        }
        Set<Scope> scopeSet = Sets.newHashSet();
        tenantMenuTos.stream().forEach(menuTemple -> {
            if (CollectionUtils.isNotEmpty(menuTemple.getScopeList())) {
                scopeSet.addAll(menuTemple.getScopeList());
            }
        });

        for (MenuTemple menuTemple : tenantMenuTos) {
            List<String> scopeNames = ScopesUtil.getScopeNames(scopeNameMap, menuTemple.getScopeList());
            menuTemple.setScopeNameList(scopeNames);
        }
    }

    private List<MenuTemple> addRoleName(Map<Integer, Object> scopeNameMap, List<MenuTemple> tenantMenuTos) {
        if (CollectionUtils.isEmpty(tenantMenuTos)) {
            return tenantMenuTos;
        }
        Map<String, String> roleMap = (Map<String, String>) scopeNameMap.getOrDefault(ScopeType.Role.getType(), Maps.newHashMap());
        return tenantMenuTos.stream().map(x -> {
            List<String> roleNames = Lists.newArrayList();
            if (CollectionUtils.isEmpty(x.getRoleIds())) {
                return x;
            }
            x.getRoleIds().stream().forEach(roleId -> {
                roleNames.add(MapUtils.getString(roleMap, roleId, null));
            });
            x.setRoleNames(roleNames.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
            return x;
        }).collect(Collectors.toList());
    }

    private List<MenuTemple> addEmployeeName(List<MenuTemple> menuTemples, Map<Integer, Object> scopeNameMap) {
        if (CollectionUtils.isEmpty(menuTemples)) {
            return menuTemples;
        }
        return menuTemples.stream().map(tenantMenuTo -> {
            tenantMenuTo.setCreatorName(ScopesUtil.getEmployeeName(scopeNameMap, tenantMenuTo.getCreatorId()));
            tenantMenuTo.setUpdaterName(ScopesUtil.getEmployeeName(scopeNameMap, tenantMenuTo.getUpdaterId()));
            return tenantMenuTo;
        }).collect(Collectors.toList());
    }

    @Override
    @RequestMapping(value = "validate_menu_limit", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public ValidateMenuLimitResult validateMenuLimit(@RequestBody(required = false) ValidateMenuLimitArg arg, @FSUserInfo UserInfo userInfo) {
        ValidateMenuLimitResult result = new ValidateMenuLimitResult();
        boolean status = tenantMenuService.validateMenuLimit(userInfo.getEnterpriseId(), WebPageConstants.APP_CRM);
        result.setResult(status);
        return result;
    }

    @Override
    @RequestMapping(value = "validate_menu_name", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public ValidateMenuNameResult validateMenuName(@RequestBody ValidateMenuNameArg arg, @FSUserInfo UserInfo userInfo) {
        ValidateMenuNameResult result = new ValidateMenuNameResult();
        boolean status = tenantMenuService.validateMenuName(userInfo.getEnterpriseId(), WebPageConstants.APP_CRM, arg.getName());
        result.setResult(status);
        return result;
    }

    @Override
    @RequestMapping(value = "role_list", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public List<MenuRoleInfo> roleList(@RequestBody(required = false) RoleListArg arg, @FSUserInfo UserInfo userInfo) {
        Map<String, String> rolesNames = roleNameService.getRoleName(userInfo.getEnterpriseId());
        List<MenuRoleInfo> menuRoleInfoList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(rolesNames)) {
            rolesNames.forEach((x, y) -> menuRoleInfoList.add(new MenuRoleInfo(x, y)));
        }
        return menuRoleInfoList;
    }

    @Override
    @RequestMapping(value = "create", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public CreateMenuResult createMenu(@FSUserInfo UserInfo userInfo, @RequestBody CreateMenuArg arg, @FSClientInfo ClientInfo clientInfo) {
        arg.valid();
        Integer tenantId = userInfo.getEnterpriseId();
        String ei = String.valueOf(tenantId);
        String appId = arg.getAppId();
        if (StringUtils.isEmpty(appId)) {
            appId = WebPageConstants.APP_CRM;
        }
        int appType = arg.getAppType();
        if (appType == 0) {
            appType = BizType.CRM.getType();
        }
        String menuId = arg.getMenuTempleVO().getMenuId();
        if (StringUtils.isEmpty(menuId) && !tenantMenuService.validateMenuLimit(userInfo.getEnterpriseId(), appId)) {
            logger.warn("getDefaultMenuData return null by userInfo {},arg {}", userInfo, arg);
            throw new WebPageException(InterErrorCode.MAX_MENU_LIMIT);
        }
        List<MetaMenuData> metaMenuDataList = metaMenuService.getMetaMenuList(DataSourceEnv.INNER, userInfo.getEnterpriseId(), appId, clientInfo.getLocale(), false);
        List<MenuItem> menuItems = TenantMenuDataUtil.buildMenuItemFromVos(appId, arg.getTenantMenuSimpleDataVos(), metaMenuDataList);
        //追加CRM提醒、CRM待办
        menuItems = MenuUtil.addMenuItemByConfig(menuItems, metaMenuDataList, addMenuItemConfig.getAddMenuItems(appId));
        menuItems = tenantMenuService.filterNotSupportMenuData(menuItems, metaMenuDataList);

        MenuTemplateDto menuTemplateDto = MenuTemplateDto.builder().
                menuId(menuId).
                tenantId(userInfo.getEnterpriseId()).
                appType(appType).
                appId(appId).
                employeeId(userInfo.getEmployeeId()).
                menuItemList(menuItems).
                name(arg.getMenuTempleVO().getName()).
                description(arg.getMenuTempleVO().getDescription()).
                roleIdList(arg.getRoleIdList()).
                scopeList(arg.getScopeList())
                .isShowMenuIcon(arg.getMenuTempleVO().getIsShowMenuIcon())
                .hiddenQuickCreate(arg.getMenuTempleVO().isHiddenQuickCreate())
                .build();

        MenuTemple tenantMenuTo = tenantMenuService.insertOrUpdateTenantMenu(DataSourceEnv.INNER, menuTemplateDto, clientInfo.getLocale());
        // 同步菜单名称多语, 只有老CRM菜单才有名称, 只有修改才需要同步名称
        if (BizType.CRM.getType() == appType && StringUtils.isNotBlank(menuId)) {
            String key = TranslateI18nUtils.getCrmMenuNametranslateKey(menuId);
            String keyDelEi = TranslateI18nUtils.delEiInKey(ei, key);
            List<String> preKeys = Lists.newArrayList(key);
            if (arg.getMenuTempleVO().getIsSystem().equals(true)) {
                TenantMenuEntity entity = menuSystemService.getTenantMenuEntityByMenuId(tenantId, menuId);
                preKeys.add(TranslateI18nUtils.delAllEi(entity.getSourceId()));
                preKeys.add(entity.getSourceId());
            }
            I18nTrans.TransArg transArg = I18nTrans.TransArg.builder()
                    .name(menuTemplateDto.getName())
                    .customKey(keyDelEi)
                    .preKeyList(preKeys)
                    .build();
            i18nService.syncTransValueIncludePreKey(tenantId, Lists.newArrayList(transArg), clientInfo.getLocale().toLanguageTag());
        }

        CreateMenuResult result = new CreateMenuResult();
        result.setResult(ObjectUtils.isNotEmpty(tenantMenuTo));
        result.setMenuId(tenantMenuTo.getMenuId());
        //创建埋点
        webPageEventService.sendCreateCRMMenu(userInfo, clientInfo, arg.getScopeList(), StringUtils.isEmpty(menuId));
        return result;
    }

    @Override
    @RequestMapping(value = "disable", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public DisableMenuResult disableMenu(@RequestBody DisableMenuArg arg, @FSClientInfo ClientInfo clientInfo, @FSUserInfo UserInfo userInfo) {
        MenuTempleAO menuTempleAO = tenantMenuService.findTenantMenuById(DataSourceEnv.INNER, arg.getMenuId(), userInfo.getEnterpriseId(), clientInfo.getLocale(), false);
        if (menuTempleAO == null) {
            logger.warn("disable null by userInfo:{}, arg:{}", userInfo, arg);
            throw new WebPageException(InterErrorCode.MENU_DATA_NOT_FUND);
        }
        MenuTemple menuTemple = menuTempleAO.getMenuTemple();
        if (menuTemple == null) {
            logger.warn("disable null by userInfo:{}, arg:{}", userInfo, arg);
            throw new WebPageException(InterErrorCode.MENU_DATA_NOT_FUND);
        }
        if (menuTemple.isSystem()) {
            logger.warn("disable error case by menu is system userInfo {},arg {}", userInfo, arg);
            throw new WebPageException(InterErrorCode.ILLEGAL_OPERATION);
        }
        boolean status = tenantMenuService.disableMenu(arg.getMenuId());
        DisableMenuResult result = new DisableMenuResult();
        result.setResult(status);
        return result;
    }

    @Override
    @RequestMapping(value = "enable", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public EnableMenuResult enableMenu(@RequestBody EnableMenuArg arg, @FSClientInfo ClientInfo clientInfo, @FSUserInfo UserInfo userInfo) {
        MenuTempleAO menuTempleAO = tenantMenuService.findTenantMenuById(DataSourceEnv.INNER, arg.getMenuId(), userInfo.getEnterpriseId(), clientInfo.getLocale(), false);
        if (ObjectUtils.isEmpty(menuTempleAO) || ObjectUtils.isEmpty(menuTempleAO.getMenuTemple())) {
            logger.warn("menu is null userInfo {},arg {}", userInfo, arg);
            throw new WebPageException(InterErrorCode.MENU_DATA_NOT_FUND);
        }
        boolean status = tenantMenuService.enableMenu(arg.getMenuId());
        EnableMenuResult result = new EnableMenuResult();
        result.setResult(status);
        return result;
    }

    @Override
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public DeleteMenuResult deleteMenu(@RequestBody DeleteMenuArg arg, @FSUserInfo UserInfo userInfo) {
        boolean status = tenantMenuService.deleteMenu(arg.getMenuId());
        DeleteMenuResult result = new DeleteMenuResult();
        result.setResult(status);
        return result;
    }

    @Override
    @RequestMapping(value = "query_tenant_config", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public QueryTenantConfigResult queryTenantConfig(@RequestBody(required = false) QueryTenantConfigArg arg, @FSUserInfo UserInfo userInfo) {
        QueryTenantConfigResult result = new QueryTenantConfigResult();
        boolean status = tenantMenuService.isHiddenSystemMenu(userInfo.getEnterpriseId());
        result.setResult(status);
        return result;
    }

    @Override
    @RequestMapping(value = "open_tenant_config", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public OpenTenantConfigResult openTenantConfig(@RequestBody(required = false) OpenTenantConfigArg arg, @FSUserInfo UserInfo userInfo) {
        OpenTenantConfigResult result = new OpenTenantConfigResult();
        tenantMenuService.setHiddenSystemMenu(userInfo.getEnterpriseId(), true);
        result.setResult(true);
        return result;
    }

    @Override
    @RequestMapping(value = "close_tenant_config", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public CloseTenantConfigResult closeTenantConfig(@RequestBody(required = false) CloseTenantConfigArg arg, @FSUserInfo UserInfo userInfo) {
        CloseTenantConfigResult result = new CloseTenantConfigResult();
        tenantMenuService.setHiddenSystemMenu(userInfo.getEnterpriseId(), false);
        result.setResult(false);
        return result;
    }

    @Override
    @RequestMapping(value = "validate_menu_apiname", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public ValidateApiNameResult validateApiName(@RequestBody ValidateApiNameArg arg, UserInfo userInfo) {
        ValidateApiNameResult result = new ValidateApiNameResult();
        // TODO 待完善
        result.setResult(true);
        return result;
    }

    @Override
    @RequestMapping(value = "get_icon_paths", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public Map<String, Map<String, String>> getIconPaths(@RequestBody(required = false) GetIconPathsArg arg, UserInfo userInfo) {
        Map<String, PreObject> preObjectMap = objectConfig.getPreObjectMap();

        Map<String, Map<String, String>> iconPathMap = new HashMap<>();
        if (MapUtils.isNotEmpty(preObjectMap)) {
            preObjectMap.forEach((x, y) -> {
                iconPathMap.put(x, new HashMap<String, String>() {{
                    if (null != y && null != y.getIcon()) {
                        put("iconHomePath", y.getIcon().getIcon_1());
                        put("iconMenuPath", y.getIcon().getIcon_1());
                    }
                }});
            });
        }

        Map<Integer, IndexIcon> indexIconMap = objectConfig.getIndexIconMap();
        if (MapUtils.isNotEmpty(indexIconMap)) {
            indexIconMap.forEach((x, y) -> {
                iconPathMap.put(String.valueOf(x), new HashMap<String, String>() {{
                    if (null != y && null != y.getIcon()) {
                        put("iconHomePath", y.getIcon().getIcon_1());
                        put("iconMenuPath", y.getIcon().getIcon_1());
                    }
                }});
            });
        }
        return iconPathMap;
    }

    @Override
    @RequestMapping(value = "queryIconData", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public List<IndexIcon> queryIconData(@RequestBody QueryIconDataArg arg, @FSUserInfo UserInfo userInfo) {
        Map<Integer, IndexIcon> indexIconMap = objectConfig.getIndexIconMap();
        List<IndexIcon> indexIcons = Lists.newArrayList();
        indexIconMap.entrySet().forEach(entry -> {
            indexIcons.add(entry.getValue());
        });
        return indexIcons.stream().sorted(Comparator.comparing(IndexIcon::getIndex)).filter(x -> {
            List<Integer> supportTenantIds = x.getSupportTenantIds();
            if (CollectionUtils.isEmpty(supportTenantIds)) {
                return true;
            }
            return supportTenantIds.contains(userInfo.getEnterpriseId());
        }).collect(Collectors.toList());
    }

    @Override
    @RequestMapping(value = "get_init_menu_items", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public GetInitMenuItemsResult getInitMenuItems(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetInitMenuItemsArg arg) {
        int enterpriseId = userInfo.getEnterpriseId();
        MenuTemple menuTemple = tenantMenuService.findTenantSystemMenuById(arg.getMenuId(), enterpriseId, WebPageConstants.APP_CRM, clientInfo.getLocale());
        List<MenuItem> menuItems = menuObjUtil.filterNeedHiddenMenuItem(menuTemple.getMenuItems());
        menuTemple.setMenuItems(menuItems);
        GetInitMenuItemsResult result = new GetInitMenuItemsResult();

        Map<String, String> languageMap = languageService.queryGroupLanguageByMenuTemple(enterpriseId, Lists.newArrayList(menuTemple), clientInfo.getLocale());

        List<String> menuNames = TenantMenuDataUtil.getMenuNames(menuItems);
        Map<String, List<String>> searchWordsMap = searchWordsService.getSearchWords(menuNames);

        result.setDetailMenuItems(TenantMenuTempleUtil.buildTenantMenuItemVOs(menuTemple.getMenuItems(), languageMap, searchWordsMap));
        return result;
    }

    @Override
    @RequestMapping(value = "getInitMenuItemsWithShow", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public GetInitMenuItemsResult getInitMenuItemsWithShow(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody GetInitMenuItemsArg arg) {
        int enterpriseId = userInfo.getEnterpriseId();
        MenuTemple menuTemple = tenantMenuService.findTenantSystemMenuById(arg.getMenuId(), enterpriseId, WebPageConstants.APP_CRM, clientInfo.getLocale());
        List<MenuItem> menuItems = menuObjUtil.filterNeedHiddenMenuItem(menuTemple.getMenuItems());
        if (CollectionUtils.isNotEmpty(menuItems)) {
            menuItems = menuItems.stream().filter(menuItem -> !menuItem.getMenuData().getIsHidden()).collect(Collectors.toList());
        }
        menuTemple.setMenuItems(menuItems);
        GetInitMenuItemsResult result = new GetInitMenuItemsResult();

        Map<String, String> languageMap = languageService.queryGroupLanguageByMenuTemple(enterpriseId, Lists.newArrayList(menuTemple), clientInfo.getLocale());

        List<String> menuNames = TenantMenuDataUtil.getMenuNames(menuItems);
        Map<String, List<String>> searchWordsMap = searchWordsService.getSearchWords(menuNames);

        result.setDetailMenuItems(TenantMenuTempleUtil.buildTenantMenuItemVOs(menuTemple.getMenuItems(), languageMap, searchWordsMap));
        return result;
    }

    @Override
    @RequestMapping(value = "insertOrUpdateTenantMenu", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.ENTERPRISE_INTERCONNECT_SETTING)
    public UpdateTenantMenuResult updateTenantMenu(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, @RequestBody UpdateTenantMenuArg arg) {
        arg.valid();
        List<String> appIds = getAppId(arg.getAppId(), arg.getTenantMenuSimpleItems());
        List<MetaMenuData> metaMenuDataList = metaMenuService.queryMetaMenuDataListByAppIds(DataSourceEnv.CROSS, appIds, userInfo.getEnterpriseId(), clientInfo.getLocale(), false);
        List<MenuItem> menuItems = TenantMenuDataUtil.buildMenuItemFromVos(arg.getAppId(), arg.getTenantMenuSimpleItems(), metaMenuDataList);

        MenuTemplateDto menuTemplateDto = MenuTemplateDto.builder().
                menuId(arg.getId()).
                tenantId(userInfo.getEnterpriseId()).
                appType(BizType.getBusinessType(arg.getAppId()).getType()).
                appId(arg.getAppId()).
                employeeId(userInfo.getEmployeeId()).
                menuItemList(menuItems).
                isShowMenuIcon(arg.getIsShowMenuIcon()).
                hiddenQuickCreate(arg.getHiddenQuickCreate()).
                build();

        MenuTemple tenantMenuTo = tenantMenuService.insertOrUpdateTenantMenu(DataSourceEnv.CROSS, menuTemplateDto, clientInfo.getLocale());
        UpdateTenantMenuResult result = new UpdateTenantMenuResult();
        result.setWebMenuId(tenantMenuTo.getMenuId());
        return result;
    }

    private List<String> getAppId(String appId, List<TenantMenuSimpleDataVo> tenantMenuSimpleItems) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tenantMenuSimpleItems)) {
            return Lists.newArrayList();
        }
        Set<String> appIds = Sets.newHashSet();
        tenantMenuSimpleItems.stream().forEach(x -> {
            if (x.getType().equals(MenuType.GROUP) && CollectionUtils.isNotEmpty(x.getChildren())) {
                appIds.addAll(getChildAppIds(x.getChildren()));
                return;
            }
            if (StringUtils.isNotEmpty(x.getAppId())) {
                appIds.add(x.getAppId());
            }
        });
        List<String> filterAppIds = appIds.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterAppIds)) {
            filterAppIds = Lists.newArrayList(appId);
        }
        return filterAppIds;

    }

    private Set<String> getChildAppIds(List<TenantMenuSimpleDataVo> tenantMenuSimpleItems) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tenantMenuSimpleItems)) {
            return Sets.newHashSet();
        }
        return tenantMenuSimpleItems.stream().map(x -> x.getAppId()).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    @Override
    @RequestMapping(value = "getCusMenuItems", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public GetCusPageResult getCusMenuItems(@FSUserInfo UserInfo userInfo) {
        List<PageData> pageDataList = homePageBaseService.getPageData(DataSourceEnv.INNER, userInfo.getEnterpriseId(), null);
        List<GetCusPageResult.CustomerPage> customerPageList = buildCusMenuItems(pageDataList);
        GetCusPageResult result = new GetCusPageResult();
        result.setCustomerPageList(customerPageList);
        return result;
    }

    @RequestMapping(value = "getTenantUiConfig", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Override
    @ResponseBody
    @ValidateFunctionPermission(SystemFunctionCodeEnum.CRMMANAGE_MODULE_MENUMANAGE)
    public TenantUIResult getTenantUiConfig(UserInfo userInfo, ClientInfo clientInfo) {
        String tenantUiConfig = tenantMenuService.getTenantUiConfig();
        return TenantUIResult.builder().tenantUiConfig(tenantUiConfig).build();
    }

    private List<GetCusPageResult.CustomerPage> buildCusMenuItems(List<PageData> pageDataList) {
        List<GetCusPageResult.CustomerPage> customerPageList = Lists.newArrayList();
        for (PageData pageData : pageDataList) {
            if (!pageData.isActive()) {
                continue;
            }
            GetCusPageResult.CustomerPage customerPage = new GetCusPageResult.CustomerPage();
            customerPage.setLayoutApiName(pageData.getLayoutApiName());
            customerPage.setDisplayName(pageData.getDisplayName());
            customerPageList.add(customerPage);
        }
        return customerPageList;
    }

    private List<TenantMenuItemVO> getTenantMenuItemVO(int tenantId, MenuTemple menuTemple, List<MetaMenuData> metaMenuDataList, ClientInfo clientInfo, boolean previewNewCrmFlag) {
        if (menuTemple == null) {
            logger.warn("getTenantMenuItemVO null by tenantId:{}", tenantId);
            throw new WebPageException(InterErrorCode.MENU_DATA_NOT_FUND);
        }
        String appId = menuTemple.getAppId();

        if (WebPageConstants.APP_CRM.equals(appId)) {
            metaMenuDataList = menuObjUtil.filterNeedHiddenMetaMenuData(tenantId, metaMenuDataList, previewNewCrmFlag);
        }

        List<MenuItem> menuItems = Lists.newArrayList();

        if (ObjectUtils.isNotEmpty(menuTemple) && CollectionUtils.isNotEmpty(menuTemple.getMenuItems())) {
            menuItems = tenantMenuService.filterNotSupportMenuData(menuTemple.getMenuItems(), metaMenuDataList);
        }
        if (CollectionUtils.isEmpty(menuItems)) {
            logger.warn("getTenantMenuItemVO return null by tenantId {},appId {}", tenantId, appId);
            throw new WebPageException(InterErrorCode.MENU_TEMPLE_NOT_FUND);
        }
        Map<String, MenuItem> menuItemMap = menuItems.stream().collect(Collectors.toMap(MenuItem::getApiName, Function.identity(), (o1, o2) -> o2));
        List<MetaMenuData> customerObjectMenus = new ArrayList<>();
        //针对老版crm菜单，管理后台追加自定义对象菜单
        if (WebPageConstants.APP_CRM.equals(appId) && !checkService.checkGoNewCRM(tenantId)) {
            customerObjectMenus = metaMenuDataList.stream().filter(metaMenuData -> MenuType.UD_OBJ.equals(metaMenuData.getMenuType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerObjectMenus)) {
                for (MetaMenuData metaMenuData : customerObjectMenus) {
                    String apiName = metaMenuData.getApiName();
                    if (null != menuItemMap.get(apiName)) {
                        //如果用户已经保存过了自定义对象，则不再补充
                        continue;
                    }
                    MenuItem menuItem = new MenuItem();

                    MenuData menuData = new MenuData();
                    menuData.setApiName(apiName);
                    menuData.setType(metaMenuData.getMenuType());
                    menuData.setOrderNumber(10000);
                    menuData.setIsHidden(false);
                    menuData.setName(metaMenuData.getName());

                    menuItem.setMenuData(menuData);

                    menuItem.setMetaMenuData(metaMenuData);
                    menuItem.setMenuItemType(metaMenuData.getMenuType());
                    menuItem.setApiName(metaMenuData.getApiName());
                    menuItem.setPrivilegeAction(Lists.newArrayList(WebPageConstants.FUNC_CODE_LIST));
                    menuItems.add(menuItem);
                }
            }
        }
        MenuUtil.sortMenuItem(menuItems);

        if (null == clientInfo.getLocale()) {
            clientInfo.setLocale(Locale.CHINESE);
            log.error("getTenantMenuItemVO.clientInfo.getLocale() is null, set Locale.CHINESE");
        }
        // 菜单分组名称翻译
        Map<String, String> languageMap = getGroupNameTransValue(tenantId, MenuUtil.getGroupMenuItem(menuItems), menuTemple.getMenuId(), clientInfo.getLocale().toLanguageTag());

        List<String> menuNames = TenantMenuDataUtil.getMenuNames(menuItems);

        Map<String, List<String>> searchWordsMap = searchWordsService.getSearchWords(menuNames);

        return TenantMenuTempleUtil.buildTenantMenuItemVOs(menuItems, languageMap, searchWordsMap, menuTemple.getMenuId());
    }

    private Map<String, String> getGroupNameTransValue(int tenantId, List<MenuItem> menuItems, String menuId, String lang) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(menuItems)) {
            return result;
        }
        List<I18nTrans.TransArg> transArgList = menuItems.stream()
                .map(menuItem -> {
                    MenuData menuData = menuItem.getMenuData();
                    String customKey = TranslateI18nUtils.getWebAppViewGroupName(menuId, menuItem.getApiName());
                    return TranslateI18nUtils.convertToTransArg(customKey,
                            Lists.newArrayList(UIPaaSI18NKey.GROUP_PREFIX + menuData.getApiName(), UIPaaSI18NKey.GROUP_PREFIX + menuData.getName()),
                            Lists.newArrayList(UIPaaSI18NKey.GROUP_PREFIX + menuData.getApiName(), UIPaaSI18NKey.GROUP_PREFIX + menuData.getName()),
                            menuData.getName());
                }).collect(Collectors.toList());
        //获取三个属性的多语值
        return i18nService.getTransValueIncludePreKeyV2(tenantId, transArgList, lang);
    }

    private List<TenantMenuItemVO> buildTenantMenuItemVOList(int tenantId, MenuTemple menuTemple, ClientInfo clientInfo) {
        List<MenuItem> menuItems = menuTemple.getMenuItems();
        Map<String, String> languageMap = getGroupNameTransValue(tenantId, menuItems, menuTemple.getMenuId(), clientInfo.getLocale().toLanguageTag());
        List<String> menuNames = TenantMenuDataUtil.getMenuNames(menuItems);
        Map<String, List<String>> searchWordsMap = searchWordsService.getSearchWords(menuNames);
        return TenantMenuTempleUtil.buildTenantMenuItemVOs(menuItems, languageMap, searchWordsMap, menuTemple.getMenuId());
    }
}
