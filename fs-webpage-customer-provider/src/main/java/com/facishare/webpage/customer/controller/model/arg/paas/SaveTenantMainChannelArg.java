package com.facishare.webpage.customer.controller.model.arg.paas;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.WebMainChannelMenuVO;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/13
 */
@Data
public class SaveTenantMainChannelArg extends BaseArg {

    private String apiName;
    private String name;
    private List<Scope> scopeList;
    private Integer priorityLevel;
    private List<WebMainChannelMenuVO> mainChannelMenuVOList;
    private Integer version;
    private Boolean showAppName = true;
    private Boolean canCustom = true;
    private Boolean showMoreAppEntry = true;

    @Override
    public void valid() {
        if (StringUtils.isEmpty(name)) {
            throw new WebPageException(InterErrorCode.NAME_CAN_NOT_EMPTY);
        }

        if (CollectionUtils.isEmpty(scopeList)) {
            throw new WebPageException(InterErrorCode.SCOPE_CAN_NOT_EMPTY);
        }

        if (CollectionUtils.isEmpty(mainChannelMenuVOList)) {
            throw new WebPageException(InterErrorCode.MENU_CAN_NOT_EMPTY);
        }
    }

}
