package com.facishare.webpage.customer.controller.model.result.menu;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantUIResult implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<JSONObject> tenantUiConfig;
}
