package com.facishare.webpage.customer.common;

import com.facishare.paas.I18N;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.utils.RequestContext;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeArg;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeResult;
import com.facishare.webpage.customer.common.model.QueryScopeNameArg;
import com.facishare.webpage.customer.common.model.QueryScopeNameResult;
import com.facishare.webpage.customer.core.config.PageTemplateConfig;
import com.facishare.webpage.customer.core.model.PageTemplateConfigVO;
import com.facishare.webpage.customer.util.TraceContextUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/7/2
 */
@Component
public class OrganizationCommonServiceImpl implements OrganizationCommonService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationCommonServiceImpl.class);

    private List<WebPageOrganizationService> webPageOrganizationServiceList;

    @Autowired
    public void setWebPageOrganizationServiceList(List<WebPageOrganizationService> webPageOrganizationServiceList) {
        this.webPageOrganizationServiceList = webPageOrganizationServiceList;
    }

    @Resource
    private PageTemplateConfig pageTemplateConfig;

    @Override
    public Map<Integer, Object> getScopeName(int tenantId,
                                             String appId,
                                             List<Scope> scopeList,
                                             Boolean cross) {
        if (CollectionUtils.isEmpty(scopeList)) {
            return Maps.newHashMap();
        }
        if (cross == null) {
            cross = checkCross(appId);
        }
        TraceContext traceContext = TraceContext.get();
        SlowLog slowLog = GlobalStopWatch.create("getScopeName", 100L);
        Boolean finalCross = cross;
        AtomicReference<I18N.I18NContext> contextRef = new AtomicReference<>(I18N.getContext());
        List<QueryScopeNameResult> scopeNameResultList = webPageOrganizationServiceList.parallelStream().map(webPageOrganizationService -> {
            try {
                I18N.I18NContext context = contextRef.get();
                if(Objects.nonNull(context)) {
                    I18N.setContext(String.valueOf(context.getTenantId()), context.getLanguage());
                }
                QueryScopeNameArg arg = QueryScopeNameArg.builder().
                        tenantId(tenantId).
                        appId(appId).
                        scopeList(scopeList).
                        cross(finalCross).build();
                TraceContextUtil.copyTraceContext(traceContext);
                QueryScopeNameResult result = webPageOrganizationService.queryScopeName(arg);
                slowLog.lap(webPageOrganizationService.getClass().getSimpleName() + "-" + "queryScopeName");
                return result;
            } catch (Exception e) {
                LOGGER.error(webPageOrganizationService.getClass().getSimpleName() + "-tenantId:{},appId:{},scopeList:{}", tenantId, appId, scopeList, e);
            } finally {
                I18N.clearContext();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        slowLog.lap("getAllScopeName");
        if (CollectionUtils.isEmpty(scopeNameResultList)) {
            return Maps.newHashMap();
        }

        Map<Integer, Object> map = Maps.newHashMap();

        scopeNameResultList.stream().forEach(x -> {
            Pair<Integer, Object> pair = covertResult2Object(x);
            if (pair == null){
                return;
            }
            map.put(pair.getKey(), pair.getRight());
        });
        slowLog.stop("over getScopeName");
        return map;
    }

    private Pair<Integer, Object> covertResult2Object(QueryScopeNameResult result) {
        switch (result.getScopeType()) {
            case 1:
            case 2:
                return Pair.of(result.getScopeType(), covertKey2Integer(result.getScopeNameMap()));
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
                return Pair.of(result.getScopeType(), result.getScopeNameMap());
            default:
                return null;
        }
    }

    private Map<Integer, String> covertKey2Integer(Map<String, String> map) {
        if (map == null) {
            return Maps.newHashMap();
        }
        Map<Integer, String> newMap = Maps.newHashMap();
        map.entrySet().stream().forEach(x -> {
            newMap.put(Integer.parseInt(x.getKey()), x.getValue());
        });
        return newMap;
    }

    private boolean checkCross(String appId) {
        if (StringUtils.isEmpty(appId)) {
            return false;
        }
        PageTemplateConfigVO pageTemplateConfig = this.pageTemplateConfig.getPageTemplateConfig(appId);
        if (pageTemplateConfig == null) {
            return false;
        }
        return pageTemplateConfig.isCrossApp();
    }

    @Override
    public List<Scope> queryScopeList(int tenantId,
                                      Integer employeeId,
                                      Long outTenantId,
                                      Long outUserId,
                                      String appId) {
        boolean cross = outUserId != null;
        TraceContext traceContext = TraceContext.get();
        List<QueryEmployeeScopeResult> queryEmployeeScopeResultList = webPageOrganizationServiceList.parallelStream().map(webPageOrganizationService -> {
            try {
                QueryEmployeeScopeArg arg = QueryEmployeeScopeArg.builder().
                        tenantId(tenantId).
                        employeeId(employeeId).
                        outTenantId(outTenantId).
                        outUserId(outUserId).
                        appId(appId).
                        cross(cross).build();
                TraceContextUtil.copyTraceContext(traceContext);
                return webPageOrganizationService.queryUserScope(arg);
            } catch (Exception e) {
                LOGGER.error("webPageOrganizationService : {}, tenantId:{},employeeId:{},outTenantId:{},outUserId:{},appId:{}", webPageOrganizationService.getClass().getSimpleName(),
                        tenantId, employeeId, outTenantId, outUserId, appId, e);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryEmployeeScopeResultList)) {
            return Lists.newArrayList();
        }
        List<Scope> scopeList = Lists.newArrayList();
        queryEmployeeScopeResultList.stream().forEach(x -> {
            scopeList.addAll(x.getScopeList());
        });
        return scopeList;
    }
}

