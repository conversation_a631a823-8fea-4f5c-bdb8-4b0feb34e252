package com.facishare.webpage.customer.service.impl;

import com.facishare.qixin.reference.relationship.constant.TargetType;
import com.facishare.qixin.reference.relationship.service.RelationshipService;
import com.facishare.qixin.reference.relationship.service.model.DelAndCreateRelation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/9/3
 */
@Component
public class DealRelationService {

    private static final Logger logger = LoggerFactory.getLogger(DealRelationService.class);

    @Resource
    private RelationshipService relationshipService;


    public void updateCusCompRelation(int tenantId, String sourceType, String sourceLabel, String sourceValue, List<String> targetValues) {

        if (CollectionUtils.isEmpty(targetValues)) {
            return;
        }
        try {
            List<DelAndCreateRelation> delAndCreateRelations = buildDelAndCreateRelations(sourceType, sourceLabel, sourceValue, TargetType.CustomComponent, targetValues);
            relationshipService.deleteAndCreateRelation(tenantId, delAndCreateRelations);
            logger.info("deleteAndCreateRelation by tenantId : {}, sourceType : {}, sourceLabel : {}, targetValues : {}",
                    tenantId, sourceType, sourceLabel, sourceValue, targetValues);
        } catch (Exception e) {
            logger.error("deleteAndCreateRelation error by tenantId : {}, sourceType : {}, sourceLabel : {}, targetValues : {}",
                    tenantId, sourceType, sourceLabel, sourceValue, targetValues, e);
        }
    }

    public void delCusCompRelation(int tenantId, String sourceType, String sourceValue) {
        try {
            relationshipService.deleteRelation(tenantId, sourceType, sourceValue, null);
            logger.info("deleteRelation by tenantId : {}, sourceType : {}, sourceValue : {}, targetValue : {}", tenantId, sourceType, sourceValue, null);
        } catch (Exception e) {
            logger.error("deleteRelation error by tenantId : {}, sourceType : {}, sourceValue : {}, targetValue : {}", tenantId, sourceType, sourceValue, null);
        }
    }


    private List<DelAndCreateRelation> buildDelAndCreateRelations(String sourceType, String sourceLabel, String sourceValue, TargetType targetType, List<String> targetValues) {

        return targetValues.stream().map(x -> {
            DelAndCreateRelation delAndCreateRelation = new DelAndCreateRelation();
            {
                delAndCreateRelation.setSourceType(sourceType);
                delAndCreateRelation.setSourceValue(sourceValue);
                delAndCreateRelation.setSourceLabel(sourceLabel);
                delAndCreateRelation.setTargetType(targetType);
                delAndCreateRelation.setTargetValue(x);
            }
            return delAndCreateRelation;
        }).collect(Collectors.toList());
    }

}
