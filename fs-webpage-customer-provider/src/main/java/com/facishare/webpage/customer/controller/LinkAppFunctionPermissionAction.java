package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.er.api.model.vo.AllocatedLayoutVO;
import com.facishare.webpage.customer.api.model.LinkAppObjectAssociationVO;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.BatchAddAssociationObjectsResult;
import com.facishare.webpage.customer.api.model.result.GetAllowedAddObjectListResult;
import com.facishare.webpage.customer.api.model.result.GetLinkAppAssociationObjectListResult;
import com.facishare.webpage.customer.model.AllocatedRecordTypePojo;

import java.util.List;

/**
 * 互联应用action
 */
//@FcpService("fcp/linkAppFunctionPermission")
public interface LinkAppFunctionPermissionAction {
    /**
     * 根据appId列表出所有已添加的对象列表
     */
    //@FcpMethod("listAssociationObjects")
    GetLinkAppAssociationObjectListResult listAssociationObjects(UserInfo userInfo, ClientInfo clientInfo, GetLinkAppAssociationObjectListArg arg);

    /**
     * 根据appId列表出所有允许被添加的对象列表
     */
    //@FcpMethod("listAllowedAddObjects")
    GetAllowedAddObjectListResult listAllowedAddObjects(UserInfo userInfo, ClientInfo clientInfo, GetAllowedAddObjectListArg arg);

    /**
     * 增加关联业务对象
     */
    //@FcpMethod("batchAddAssociationObjects")
    BatchAddAssociationObjectsResult batchAddAssociationObjects(UserInfo userInfo, ClientInfo clientInfo, BatchAddAssociationObjectsArg batchAddAssociationObjectsArg);

    /**
     * 列出所有分配的业务类型
     */
    //@FcpMethod("listAllocatedRecordTypeByObject")
    AllocatedRecordTypePojo listAllocatedRecordTypeByObject(UserInfo userInfo, ClientInfo clientInfo, ListAllocatedRecordTypeByObjectArg listAllocatedRecordTypeByObjectArg);

    /**
     * 分配业务类型
     */
    //@FcpMethod("allocateRecordTypeByObject")
    void allocateRecordTypeByObject(UserInfo userInfo, ClientInfo clientInfo, AllocateRecordTypeByObjectArg allocateRecordTypeByObjectArg);

    /**
     * 列出所有分配的布局
     */
    //@FcpMethod("listAllocatedLayoutByObject")
    AllocatedLayoutVO listAllocatedLayoutByObject(UserInfo userInfo, ClientInfo clientInfo, ListAllocatedLayoutByObjectArg listAllocatedLayoutByObjectArg);

    /**
     * 分配布局
     */
    //@FcpMethod("allocateLayoutByObject")
    void allocateLayoutByObject(UserInfo userInfo, ClientInfo clientInfo, AllocateLayoutByObjectArg allocateLayoutByObjectArg);

    /**
     * 移除关联业务对象
     */
    //@FcpMethod("removeAssociationObject")
    List<LinkAppObjectAssociationVO> removeAssociationObject(UserInfo userInfo, ClientInfo clientInfo, RemoveAssociationObjectArg removeAssociationObjectArg);
}
