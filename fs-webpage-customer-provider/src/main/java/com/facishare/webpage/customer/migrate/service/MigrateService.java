package com.facishare.webpage.customer.migrate.service;

import com.facishare.webpage.customer.migrate.action.QueryMigrateDataList;
import com.facishare.webpage.customer.migrate.action.SaveMigrateDataList;

import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2022/2/23 3:26 PM
 */
public interface MigrateService {

    QueryMigrateDataList.Result queryMigrateDataList(int tenantId, Locale locale);

    void saveMigrateDataList(SaveMigrateDataList.Arg arg);

}
