package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.dao.entity.EmployeeConfigEntity;
import com.facishare.webpage.customer.api.model.EmployeeConfig;

import java.util.List;

/**
 * Created by <PERSON><PERSON>yu on 2019/10/29
 */
public interface EmployeeConfigDao {

    EmployeeConfigEntity upsertEmployeeConfig(int tenantId, int employeeId, String layoutId, EmployeeConfig employeeConfig);

    EmployeeConfigEntity getEmployeeConfig(String layoutId, int tenantId, int employeeId, int key);

    EmployeeConfigEntity upsertEmployeeConfigByApiName(int tenantId, int employeeId, String apiName, EmployeeConfig employeeConfig);

    List<EmployeeConfigEntity> getEmployeeConfigForConsole(List<String> layoutIds, int layoutType, int employeeId);
}
