package com.facishare.webpage.customer.system.datasource;

import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.qixin.sysdb.model.Data;
import com.facishare.qixin.sysdb.serivce.DataSourceService;
import com.facishare.webpage.customer.dao.PaaSAppDao;
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Yu
 */
public class PaasAppDataServiceImpl implements DataSourceService {

    @Resource
    private PaaSAppDao paaSAppDao;


    @Override
    public Data getDataByDataId(Integer tenantId, String appId, Filter filter) {
        return paaSAppDao.getPaaSAppByAppId(tenantId, appId);
    }

    @Override
    public List<? extends Data> batchGetDataByDataId(Integer tenantId, List appIds, Filter filter) {
        return paaSAppDao.getPaaSAppByAppIds(tenantId, appIds, filter);
    }

    @Override
    public Data findAndModify(int tenantId, Data data) {
        return paaSAppDao.findAndModifyPaaSAppEntity(tenantId, (PaaSAppEntity) data);
    }

    @Override
    public List<? extends Data> queryDataList(int tenantId, Filter filter) {
        return paaSAppDao.getPaaSAppList(tenantId, filter);

    }

    @Override
    public List<? extends Data> queryUserDataList(int tenantId, int userId, Filter filter) {
        return paaSAppDao.getUserPaaSAppList(tenantId, filter);
    }
}
