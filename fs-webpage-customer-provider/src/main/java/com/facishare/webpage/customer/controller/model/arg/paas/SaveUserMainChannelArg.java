package com.facishare.webpage.customer.controller.model.arg.paas;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.api.model.core.WebMainChannelMenuVO;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/13
 */
@Data
public class SaveUserMainChannelArg extends BaseArg {

    private String apiName;
    private List<WebMainChannelMenuVO> webMainChannelMenuVOList;
    private Boolean showAppName = true;
    private Boolean showMoreAppEntry = true;
    private Integer version = WebPageConstants.MAIN_CHANNEL_DEFAULT_VERSION;

    @Override
    public void valid() {
        if (CollectionUtils.isEmpty(webMainChannelMenuVOList)) {
            throw new WebPageException(InterErrorCode.MENU_CAN_NOT_EMPTY);
        }
    }
}
