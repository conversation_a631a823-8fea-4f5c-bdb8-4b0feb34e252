package com.facishare.webpage.customer.service.impl;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.customelayout.Filter;
import com.facishare.webpage.customer.common.EmployeeConfigCommonService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.api.constant.EmployeeConstant;
import com.facishare.webpage.customer.api.model.EmployeeConfig;
import com.facishare.webpage.customer.service.EmployeeConfigBaseService;
import com.facishare.webpage.customer.service.HomePageCommonService;
import com.facishare.webpage.customer.util.EmployeeConfigUtil;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhangyu on 2020/2/13
 */
public class HomePageCommonServiceImpl implements HomePageCommonService {

    @Resource
    private EmployeeConfigBaseService employeeConfigBaseService;
    @Autowired
    private EmployeeConfigCommonService employeeConfigCommonService;
    @Resource
    private OrganizationCommonService organizationCommonService;

    @Override
    public List<String> getScopeList(int tenantId, int employeeId, String appId) {

        List<Scope> scopeList = organizationCommonService.queryScopeList(tenantId, employeeId, null, null, appId);
        List<String> scopesToString = ScopesUtil.buildScopesToString(scopeList);

        return scopesToString;
    }

    @Override
    public List<Filter> convertFilter(UserInfo userInfo, OuterUserInfo outerUserInfo, String apiName) {
        int tenantId = userInfo.getEnterpriseId();
        int employeeId ;

        List<EmployeeConfig> employeeConfigValueByKeys;

        if (outerUserInfo != null){
            employeeId = outerUserInfo.getUpstreamOwnerId() == null ? 0 : outerUserInfo.getUpstreamOwnerId();
        }else {
            employeeId = userInfo.getEmployeeId();
        }
        employeeConfigValueByKeys = employeeConfigBaseService.getEmployeeConfigValueByKeys(apiName, tenantId, employeeId, Lists.newArrayList(EmployeeConstant.HomePageDefault, EmployeeConstant.UserDefindSelect));
        employeeConfigValueByKeys = employeeConfigCommonService.buildEmployeeConfig(employeeConfigValueByKeys, tenantId, employeeId, outerUserInfo, 4);

        List<Filter> filters = Lists.newArrayList();
        employeeConfigValueByKeys.stream().forEach(employeeConfig -> {

            if (employeeConfig.getKey() == EmployeeConstant.UserDefindSelect) {
                Filter filter = new Filter();
                filter.setFilterData(employeeConfig.getValue());
                filter.setFilterType(ComponentConstant.filterDefaultType);
                filters.add(filter);
            } else {
                filters.addAll(EmployeeConfigUtil.covertPageEmployeeConfig(employeeConfig));
            }
        });
        return filters;
    }

}
