package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.constant.TenantConfigKey;
import com.facishare.webpage.customer.constant.UserMenuStatus;
import com.facishare.webpage.customer.dao.TenantConfigDao;
import com.facishare.webpage.customer.dao.UserMenuDao;
import com.facishare.webpage.customer.dao.entity.TenantConfigEntity;
import com.facishare.webpage.customer.dao.entity.UserMenuEntity;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.MenuItem;
import com.facishare.webpage.customer.service.UserMenuService;
import com.facishare.webpage.customer.util.TenantMenuDataUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Created by zhangyi on 2019/12/4.
 */
@Slf4j
public class UserMenuServiceImpl implements UserMenuService {

    @Resource
    private UserMenuDao userMenuDao;
    @Resource
    private TenantConfigDao tenantConfigDao;

    @Override
    public List<MenuItem> insertOrUpdateUserMenu(String menuId, List<MenuItem> menuItems, int enterpriseId, int employeeId) {
        UserMenuEntity entity = userMenuDao.findUserMenuByMenuId(enterpriseId, employeeId, menuId);
        if (entity == null) {
            entity = new UserMenuEntity();
            entity.setCreateTime(System.currentTimeMillis());
        }
        entity.setStatus(UserMenuStatus.NORMAL);
        entity.setTenantId(enterpriseId);
        entity.setEmployeeId(employeeId);
        entity.setTenantMenuId(menuId);
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setMenuDataEntities(TenantMenuDataUtil.buildMenuDataEntityFromMenuItems(menuItems));
        userMenuDao.save(entity);
        tenantConfigDao.setUserValue(enterpriseId, employeeId, TenantConfigKey.USER_DEFAULT_MENU, menuId);
        return menuItems;
    }

    @Override
    public boolean delete(String menuId, int enterpriseId, int employeeId) {
        userMenuDao.deleteUserMenu(menuId, enterpriseId, employeeId);
        tenantConfigDao.deleteUserKey(enterpriseId, employeeId, TenantConfigKey.USER_DEFAULT_MENU);
        return true;
    }

    @Override
    public boolean hasUserMenu(String menuId, int enterpriseId, int employeeId) {
        UserMenuEntity entity = userMenuDao.findUserMenuByMenuId(enterpriseId, employeeId, menuId);
        if (entity != null && entity.getStatus() == UserMenuStatus.NORMAL) {
            return true;
        }
        return false;
    }

    @Override
    public List<String> queryCommonlyUserMenuItem(int enterpriseId, int employeeId) {
        TenantConfigEntity entity = tenantConfigDao.getUserValueByKey(enterpriseId, employeeId, TenantConfigKey.USER_COMMONLY_MENU);
        if (entity != null && entity.getValue() != null) {
            return JSON.parseArray(entity.getValue(), String.class);
        }
        return null;
    }

    @Override
    public String getUserDefaultMenu(int enterpriseId, int employeeId) {
        TenantConfigEntity entity = tenantConfigDao.getUserValueByKey(enterpriseId, employeeId, TenantConfigKey.USER_DEFAULT_MENU);
        if (entity == null) {
            return null;
        }
        return entity.getValue();
    }

    @Override
    public List<String> insertOrUpdateCommonlyUsedMenu(int enterpriseId, int employeeId, List<String> menuApiNames) {
        TenantConfigEntity entity = tenantConfigDao.setUserValue(enterpriseId, employeeId, TenantConfigKey.USER_COMMONLY_MENU, JSON.toJSONString(menuApiNames));
        return JSON.parseArray(entity.getValue(), String.class);
    }

    @Override
    public boolean setUserDefaultMenu(String menuId, int enterpriseId, int employeeId) {
        TenantConfigEntity entity = tenantConfigDao.setUserValue(enterpriseId, employeeId, TenantConfigKey.USER_DEFAULT_MENU, menuId);
        if (entity != null) {
            return true;
        }
        return false;
    }

    @Override
    public Map<String, List<MenuItem>> getUserMenus(int enterpriseId, int employeeId, List<MetaMenuData> metaMenuDataList) {
        List<UserMenuEntity> userMenuList = userMenuDao.getUserMenuList(enterpriseId, employeeId);
        if(log.isDebugEnabled()) {
            log.debug("userMenuDao.getUserMenuList:enterpriseId={} employeeId={} userMenuList ={}", enterpriseId, employeeId, JSONObject.toJSONString(userMenuList));
        }
        Map<String, List<MenuItem>> userMenuMap = Maps.newHashMap();

        userMenuList.stream().forEach(userMenuEntity -> {
            List<MenuItem> menuItemList = TenantMenuDataUtil.buildUserMenuItems(userMenuEntity.getMenuDataEntities(), metaMenuDataList);
            userMenuMap.put(userMenuEntity.getTenantMenuId(), menuItemList);
        });

        return userMenuMap;
    }

    @Override
    public List<MenuItem> queryUserMenuById(DataSourceEnv env,
                                            int enterpriseId,
                                            int employeeId,
                                            String menuId,
                                            List<MetaMenuData> metaMenuDataList,
                                            Locale locale) {
        UserMenuEntity userMenuEntity = userMenuDao.findUserMenuByMenuId(enterpriseId, employeeId, menuId);
        if (userMenuEntity == null) {
            return Lists.newArrayList();
        }
        return TenantMenuDataUtil.buildUserMenuItems(userMenuEntity.getMenuDataEntities(), metaMenuDataList);
    }
}
