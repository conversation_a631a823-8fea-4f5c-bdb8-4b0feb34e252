package com.facishare.webpage.customer.metadata.model;

import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.webpage.customer.api.constant.CustomMenuType;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.api.model.core.Url;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.model.MenuSourceTypeConst;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.facishare.webpage.customer.model.ObjectRecordData;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * Created by zhangyu on 2020/11/4
 */
@Data
public class CustomerMenuData implements MetaMenuData {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerMenuData.class);
    private static final String url = "crm/customer?apiname=%s&thirdapprecordtype=%s";
    private static final String DASHBOARD_URL = "bi/customizeboard/=/dashboardId-%s";
    private static final String CROSS_DASHBOARD_URL = "bi/customizeboard/=/dashboardId-%s/appid-%s";
    private static final String DASHBOARDS_URL = "bi/customizeboard/=/dashboardIds-%s";
    private static final String CROSS_DASHBOARDS_URL = "bi/customizeboard/=/dashboardIds-%s/appid-%s";

    private ObjectRecordData objectRecordData;
    private String appId;
    private CustomerMenu customerMenu;
    private List<String> i18nKeyList;

    @Override
    public String getApiName() {
        return customerMenu.getMenuApiName();
    }

    @Override
    public String getAppId() {
        return StringUtils.isEmpty(customerMenu.getAppId()) ? appId : customerMenu.getAppId();
    }

    @Override
    public String getObjectApiName() {
        if (MenuSourceTypeConst.CRM_MENU_TYPE == customerMenu.getMenuType()) {
            return objectRecordData.getApiName();
        }
        return null;
    }

    @Override
    public String getObjectRecordTypeApiName() {
        if (MenuSourceTypeConst.CRM_MENU_TYPE == customerMenu.getMenuType()) {
            return objectRecordData.getRecordTypeApiName();
        }
        return null;
    }

    @Override
    public boolean isActive() {
        if (MenuSourceTypeConst.CRM_MENU_TYPE == customerMenu.getMenuType()) {
            return objectRecordData.isActive();
        }
        return true;
    }

    @Override
    public String getName() {
        return customerMenu.getName();
    }

    @Override
    public Icon getIcon() {
        if (MenuSourceTypeConst.CRM_MENU_TYPE == customerMenu.getMenuType()) {
            return objectRecordData.getIcon();
        }
        return null;
    }

    @Override
    public int getIconIndex() {
        if (MenuSourceTypeConst.CRM_MENU_TYPE == customerMenu.getMenuType()
                && objectRecordData != null
                && objectRecordData.getIconIndex() != null) {
            return objectRecordData.getIconIndex();
        }else{
            if(this.getCustomerMenu() != null &&this.getCustomerMenu().getIconIndex() != null){
                return this.getCustomerMenu().getIconIndex();
            }
            return 0;
        }
    }

    @Override
    public Integer getIconSlot() {
        return 0;
    }

    @Override
    public Url getUrl() {
        if (CustomMenuType.CRM_MENU_TYPE == customerMenu.getMenuType() &&
                (MenuType.PRE_OBJ.equals(objectRecordData.getObjectType())
                        || MenuType.UD_OBJ.equals(objectRecordData.getObjectType()))) {
            String parameterUrl = String.format(url, getObjectApiName(), getObjectRecordTypeApiName());
            Url url = new Url();
            url.setParameterUrl(parameterUrl);
            url.setUseServerUrl(false);
            return url;
        }
        if (CustomMenuType.PAGE_MENU_TYPE == customerMenu.getMenuType()) {
            Url url = new Url();
            url.setWebUrl(WebPageConstants.CUS_PAGE_PREFIX_URL + customerMenu.getApiName());
            url.setUseServerUrl(true);
            return url;
        }
        if (CustomMenuType.DASHBOARD == customerMenu.getMenuType()) {
            Url url = new Url();
            url.setUseServerUrl(true);
            if (CollectionUtils.isNotEmpty(customerMenu.getApiNameList())) {
                //序列化成json串
                String apiNamesJson = JSON.toJSONString(customerMenu.getApiNameList());
                //转义
                if (StringUtils.isNotEmpty(appId)) {
                    url.setWebGoJumpUrl(String.format(CROSS_DASHBOARDS_URL, UrlEncode(apiNamesJson), getAppId()));
                } else {
                    url.setWebGoJumpUrl(String.format(DASHBOARDS_URL, UrlEncode(apiNamesJson)));
                }
            } else {
                if (StringUtils.isNotEmpty(appId)) {
                    url.setWebGoJumpUrl(String.format(CROSS_DASHBOARD_URL, customerMenu.getApiName(), getAppId()));
                } else {
                    url.setWebGoJumpUrl(String.format(DASHBOARD_URL, customerMenu.getApiName()));
                }
            }
            return url;
        }
        return null;
    }

    private String UrlEncode(String url) {
        String encoded = "";
        try {
            //用URLEncoder.encode方法会把空格变成加号（+）,encode之后在替换一下
            encoded = URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("encode url failed:{}", url, e);
        }
        return encoded;
    }

    @Override
    public List<String> getDeviceTypes() {
        return Lists.newArrayList(ClientTypeEnum.Web.getValue(), ClientTypeEnum.Mac.getValue(), ClientTypeEnum.Windows.getValue());
    }

    @Override
    public String getMenuType() {
        return MenuType.CUSTOMER_MENU;
    }

    @Override
    public List<String> getFunctions() {
        return Lists.newArrayList();
    }

    @Override
    public List<String> getRoleCodes() {
        return null;
    }

    @Override
    public List<Scope> getScopeList() {
        return customerMenu.getScopeList();
    }

    @Override
    public boolean hidden() {
        return true;
    }

    @Override
    public boolean customerMenuHidden() {
        return customerMenu.isNeedHidden();
    }

    @Override
    public boolean checkDetailObjectButton() {
        return false;
    }

    @Override
    public String getTarget() {
        return null;
    }

    /**
     * 自定义菜单暂不支持fxIcon
     * @return
     */
    @Override
    public String getFxIcon() {
        if (MenuSourceTypeConst.CRM_MENU_TYPE == customerMenu.getMenuType()
                && objectRecordData != null
                && objectRecordData.getIconIndex() != null) {
            return "fx-icon-obj-app"+ (objectRecordData.getIconIndex() + 1);
        }else{
            if(this.getCustomerMenu() != null &&this.getCustomerMenu().getIconIndex() != null){
                return "fx-icon-obj-app"+ (this.getCustomerMenu().getIconIndex()+1);
            }
            return "fx-icon-obj-app1";
        }
    }


}
