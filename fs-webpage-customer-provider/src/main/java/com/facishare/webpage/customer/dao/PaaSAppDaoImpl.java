package com.facishare.webpage.customer.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.facishare.webpage.customer.api.model.ScopeForCross;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppListArg;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppRoleListArg;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.api.constant.PaaSStatus;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.dao.entity.PaaSAppEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.google.common.collect.Lists;
import com.mongodb.DuplicateKeyException;
import com.mongodb.MongoCommandException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.aggregation.AggregationPipeline;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * Created by zhangyu on 2020/11/12
 */
@Component
@Slf4j
public class PaaSAppDaoImpl implements PaaSAppDao {

    @Resource
    private Datastore datastore;


    @Override
    public void savePaaSApp(PaaSAppEntity paaSAppEntity) {
        paaSAppEntity.setAppType(0 == paaSAppEntity.getAppType() ? AppTypeEnum.PAAS_APP.getAppType() : paaSAppEntity.getAppType());
        try {
            datastore.save(paaSAppEntity);
        } catch (RuntimeException e) {
            Throwable cause = e;
            while ((cause.getCause() != null)) {
                cause = cause.getCause();
            }
            if (cause instanceof MongoCommandException) {
                MongoCommandException mongoException = (MongoCommandException) cause;
                if (mongoException.getErrorCode() == 11000) {
                    log.warn("there is error when savePaaSApp:{}", mongoException.getErrorMessage());
                } else {
                    throw e;
                }
            } else if (cause instanceof DuplicateKeyException) {
                DuplicateKeyException mongoException = (DuplicateKeyException) cause;
                log.warn("there is error when savePaaSApp:{}", mongoException.getErrorMessage());
            } else {
                throw e;
            }
        }
    }

    @Override
    public PaaSAppEntity findAndModifyPreApp(int tenantId, int employeeId, PaaSAppVO paaSAppVO) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(paaSAppVO.getAppId());

        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.setOnInsert("tenantId", tenantId);
        updateOperations.setOnInsert("appId", paaSAppVO.getAppId());
        if (paaSAppVO.getAppType() > 0) {
            updateOperations.setOnInsert("appType", paaSAppVO.getAppType());
        }
        if (CollectionUtils.isNotEmpty(paaSAppVO.getScopeList())) {
            updateOperations.set("scopeList", ScopesUtil.buildScopesToString(paaSAppVO.getScopeList()));
        }
        //预制应用保存适用范围时传状态是PaaSStatus.SYS_STATUS ，新建数据时将状态设置为启用，编辑适用范围不修改状态，只有预制应用才走这个逻辑
        if (paaSAppVO.getStatus() == PaaSStatus.SYS_STATUS) {
            updateOperations.setOnInsert("status", PaaSStatus.enable);
        } else {
            updateOperations.set("status", paaSAppVO.getStatus());
        }
        if (StringUtils.isNotBlank(paaSAppVO.getSourceType())) {
            updateOperations.set("sourceType", paaSAppVO.getSourceType());
        }
        if (StringUtils.isNotBlank(paaSAppVO.getAccessType())) {
            updateOperations.set("accessType", paaSAppVO.getAccessType());
        }
        updateOperations.setOnInsert("creatorId", employeeId);
        updateOperations.setOnInsert("createTime", System.currentTimeMillis());
        updateOperations.set("updaterId", employeeId);
        updateOperations.set("updateTime", System.currentTimeMillis());
        if (StringUtils.isNotBlank(paaSAppVO.getParentAppId())) {
            updateOperations.set("parentAppId", paaSAppVO.getParentAppId());
        }

        return datastore.findAndModify(query, updateOperations, false, true);
    }

    @Override
    public PaaSAppEntity findAndModifyPaaSAppEntity(Integer tenantId, PaaSAppEntity paaSAppEntity) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(paaSAppEntity.getAppId());
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.setOnInsert("tenantId", tenantId);
        if (paaSAppEntity.getAppType() > 0) {
            updateOperations.set("appType", paaSAppEntity.getAppType());
        }
        updateOperations.setOnInsert("appId", paaSAppEntity.getAppId());
        updateOperations.set("scopeList", paaSAppEntity.getScopeList());
        if (CollectionUtils.isNotEmpty(paaSAppEntity.getScopeListForCross())) {
            updateOperations.set("scopeListForCross", paaSAppEntity.getScopeListForCross());
        }
        updateOperations.set("plugins", StringUtils.isEmpty(paaSAppEntity.getPlugins()) ? "" : paaSAppEntity.getPlugins());

        updateOperations.set("scopeList", paaSAppEntity.getScopeList());
        updateOperations.set("name", paaSAppEntity.getName());
        updateOperations.set("description", StringUtils.isEmpty(paaSAppEntity.getDescription()) ? "" : paaSAppEntity.getDescription());
        updateOperations.set("iconIndex", StringUtils.isEmpty(paaSAppEntity.getIconIndex()) ? "" : paaSAppEntity.getIconIndex());
        updateOperations.set("icon", StringUtils.isEmpty(paaSAppEntity.getIcon()) ? "" : paaSAppEntity.getIcon());
        updateOperations.set("uploadType", StringUtils.isEmpty(paaSAppEntity.getUploadImgType()) ? "" : paaSAppEntity.getUploadImgType());
        updateOperations.set("status", paaSAppEntity.getStatus());
        updateOperations.set("sourceType", StringUtils.isBlank(paaSAppEntity.getSourceType()) ? "" : paaSAppEntity.getSourceType());
        updateOperations.setOnInsert("creatorId", paaSAppEntity.getCreatorId());
        updateOperations.setOnInsert("createTime", paaSAppEntity.getCreateTime());
        updateOperations.set("updaterId", paaSAppEntity.getUpdaterId());
        updateOperations.set("updateTime", System.currentTimeMillis());
        updateOperations.set("useUserPageTempleFlag", paaSAppEntity.isUseUserPageTempleFlag());
        try {
            return datastore.findAndModify(query, updateOperations, false, true);
        } catch (Exception e) {
            log.error("findAndModifyPaaSAppEntity error tenantId={}, paaSAppEntity={}", tenantId, JSONObject.toJSONString(paaSAppEntity), e);
            log.error("findAndModifyPaaSAppEntity error query={}, query.count = {}, updateOperations={}, ", JSONObject.toJSONString(query), query.countAll(), JSONObject.toJSONString(updateOperations), e);
            throw e;
        }
    }

    @Override
    public PaaSAppEntity findAndModifyPaaSAppEntityForLinkApp(Integer tenantId, PaaSAppEntity paaSAppEntity) {
        if (paaSAppEntity.getAppType() != AppTypeEnum.LINK_APP.getAppType() && paaSAppEntity.getAppType() != AppTypeEnum.CUSTOMER_LINK_APP.getAppType()) {
            return new PaaSAppEntity();
        }
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        if (null != tenantId && 0 != tenantId) {
            query.field("tenantId").equal(tenantId);
        }
        query.field("appId").equal(paaSAppEntity.getAppId());

        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        if (null != tenantId && 0 != tenantId) {
            updateOperations.set("tenantId", tenantId);
        }
        if (paaSAppEntity.getAppType() > 0) {
            updateOperations.set("appType", paaSAppEntity.getAppType());
        }
        updateOperations.setOnInsert("appId", paaSAppEntity.getAppId());
        if (CollectionUtils.isNotEmpty(paaSAppEntity.getScopeListForCross())) {
            updateOperations.set("scopeListForCross", paaSAppEntity.getScopeListForCross());
        }

        if (CollectionUtils.isNotEmpty(paaSAppEntity.getScopeList())) {
            updateOperations.set("scopeList", paaSAppEntity.getScopeList());
        }
        updateOperations.setOnInsert("name", paaSAppEntity.getName());
        updateOperations.setOnInsert("description", StringUtils.isEmpty(paaSAppEntity.getDescription()) ? "" : paaSAppEntity.getDescription());
        updateOperations.setOnInsert("iconIndex", StringUtils.isEmpty(paaSAppEntity.getIconIndex()) ? "" : paaSAppEntity.getIconIndex());
        updateOperations.setOnInsert("icon", StringUtils.isEmpty(paaSAppEntity.getIcon()) ? "" : paaSAppEntity.getIcon());
        updateOperations.setOnInsert("uploadType", StringUtils.isEmpty(paaSAppEntity.getUploadImgType()) ? "" : paaSAppEntity.getUploadImgType());
        updateOperations.setOnInsert("status", paaSAppEntity.getStatus());
        updateOperations.set("sourceType", paaSAppEntity.getSourceType());
        updateOperations.set("creatorId", paaSAppEntity.getCreatorId());
        updateOperations.set("createTime", paaSAppEntity.getCreateTime());
        updateOperations.set("updaterId", paaSAppEntity.getUpdaterId());
        updateOperations.set("updateTime", paaSAppEntity.getUpdateTime());
        updateOperations.set("useUserPageTempleFlag", paaSAppEntity.isUseUserPageTempleFlag());

        return datastore.findAndModify(query, updateOperations, false, true);
    }


    @Override
    public PaaSAppEntity getPaaSAppByAppId(int tenantId, String appId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(appId);

        return query.get();
    }

    @Override
    public List<PaaSAppEntity> getPaaSAppByAppIds(int tenantId, List<String> appIds, Filter filter) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").in(appIds);

        if (filter != null) {
            filter.addFilter(query);
        }

        List<PaaSAppEntity> list = query.order("-createTime").asList();
        return filterMultiCrossPortal(tenantId, list);
    }

    @Override
    public List<PaaSAppEntity> getPaaSAppList(int tenantId, Filter filter) {
        return getPaaSAppList(tenantId, filter, true);
    }

    @Override
    public List<PaaSAppEntity> getPaaSAppList(int tenantId, Filter filter, boolean statusNotEqualsDeleted) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        if (statusNotEqualsDeleted) {
            query.field("status").notEqual(PaaSStatus.delete);
        }
        if (filter != null) {
            filter.addFilter(query);
        }
        List<PaaSAppEntity> list = query.order("-createTime").asList();
        return filterMultiCrossPortal(tenantId, list);
    }

    @Override
    public void updatePaaSAppStatus(int tenantId, int employeeId, String appId, int status) {
        batchUpdateStatus(tenantId, employeeId, Lists.newArrayList(appId), status);
    }

    @Override
    public List<PaaSAppEntity> getUserPaaSAppList(int tenantId, Filter filter) {// todo
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").notEqual(PaaSStatus.delete);

        if (filter != null) {
            filter.addFilter(query);
        }

        List<PaaSAppEntity> list = query.order("-createTime").asList();
        return filterMultiCrossPortal(tenantId, list);
    }

    @Override
    public PaaSAppEntity queryPaaSAppEntityByName(int tenantId, String name, int appType) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").notEqual(PaaSStatus.delete);
        query.field("name").equal(name);
        if (appType > 0) {
            query.field("appType").equal(appType);
        } else {
            query.field("appType").doesNotExist();
        }
        return query.get();
    }

    @Override
    public List<PaaSAppEntity> queryPaaSAppEntityByAppType(int tenantId, int appType) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").notEqual(PaaSStatus.delete);

        List<PaaSAppEntity> list = query.asList();
        return filterMultiCrossPortal(tenantId, list);
    }

    @Override
    public List<PaaSAppEntity> getLinkAppVOListByRoleId(Integer tenantId, String roleId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("status").notEqual(PaaSStatus.delete);
        query.field("scopeList").hasAnyOf(Lists.newArrayList(roleId));
        query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        return query.asList();
    }

    @Override
    public void updatePaaSAppScopeList(Integer tenantId, String appId, List<String> scopeList, List<ScopeForCross> scopeListForCross) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").equal(appId);
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.set("scopeList", scopeList);
        updateOperations.set("scopeListForCross", scopeListForCross);
        updateOperations.set("updateTime", System.currentTimeMillis());
        datastore.findAndModify(query, updateOperations, false, false);
    }

    @Override
    public List<Integer> listByStatusAndType(Integer status, int appType) {
        //SELECT DISTINCT(upstream_ea) as upstream_ea FROM upstream_enterprise_link_app_relation WHERE `status` =#{status}"
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appType").equal(appType);
        query.field("status").equal(status);
        query.retrievedFields(true, "tenantId");
        AggregationPipeline pipeline = datastore.createAggregation(TenantMenuEntity.class);
        pipeline.match(query);
        pipeline.group("tenantId");


        List<Integer> eiList = Lists.newArrayList();
        Iterator<PaaSAppEntity> resultIterator = pipeline.aggregate(PaaSAppEntity.class);
        while (resultIterator.hasNext()) {
            eiList.add(resultIterator.next().getTenantId());
        }
        return eiList;
    }

    @Override
    public Integer getFirstUpstreamEaByApp(String appId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(appId);
        query.offset(0);
        query.limit(1);
        query.order("createTime");
        PaaSAppEntity paaSAppEntity = query.get();
        if (null != paaSAppEntity) {
            return paaSAppEntity.getTenantId();
        } else {
            return null;
        }
    }

    @Override
    public void batchUpdateStatus(Integer tenantId, int userId, List<String> linkAppIds, int status) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appId").in(linkAppIds);

        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updaterId", userId);
        updateOperations.set("updateTime", System.currentTimeMillis());
        datastore.update(query, updateOperations);
    }

    @Override
    public List<PaaSAppEntity> getLinkAppVOList(GetlinkAppListArg arg) {
        Query<PaaSAppEntity> query = getLinkAppVOListQuery(arg);
        query.offset(0);
        if (null != arg.getPageSize() && null != arg.getPageNum()) {
            query.offset(arg.getPageSize() * (arg.getPageNum() - 1));
            query.limit(arg.getPageSize());
        }
        query.order("createTime");
        List<PaaSAppEntity> list = query.asList();
        return list;
    }

    @NotNull
    private Query<PaaSAppEntity> getLinkAppVOListQuery(GetlinkAppListArg arg) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(arg.getTenantId());
        if (null == arg.getType()) {
            query.field("appType").in(Lists.newArrayList(AppTypeEnum.LINK_APP.getAppType(), AppTypeEnum.CUSTOMER_LINK_APP.getAppType()));
        } else if (1 == arg.getType()) {
            query.field("appType").equal(AppTypeEnum.LINK_APP.getAppType());
        } else {
            query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        }
        if (null != arg.getStatus()) {
            query.field("status").equal(arg.getStatus());
        }
        query.field("status").notEqual(PaaSStatus.delete);
        if (CollectionUtils.isNotEmpty(arg.getLinkAppIds())) {
            List<String> appIds = arg.getLinkAppIds();
            appIds = appIds.stream().filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(appIds)) {
                query.field("appId").in(arg.getLinkAppIds());
            }
        }
        return query;
    }

    @Override
    public List<PaaSAppEntity> getLinkAppRoleList(GetlinkAppRoleListArg arg) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(arg.getTenantId());

        query.field("appType").in(Lists.newArrayList(AppTypeEnum.LINK_APP.getAppType(), AppTypeEnum.CUSTOMER_LINK_APP.getAppType()));
        if (CollectionUtils.isNotEmpty(arg.getLinkAppIds())) {
            query.field("appId").in(arg.getLinkAppIds());
        }
        List<PaaSAppEntity> list = query.asList();
        return list;
    }

    @Override
    public List<PaaSAppEntity> getPaasAppByType(int tenantId, int appType) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("appType").equal(appType);
        query.field("status").equal(PaaSStatus.enable);
        List<PaaSAppEntity> list = query.asList();
        return filterMultiCrossPortal(tenantId, list);
    }

    @Override
    public List<PaaSAppEntity> getPaasAppListByIconType(List<Integer> tenantIds, Boolean editType) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        if (editType) {
            query.field("tenantId").in(tenantIds);
        } else {
            query.field("tenantId").hasNoneOf(tenantIds);
        }
        query.or(query.criteria("icon").startsWith("A_"), query.criteria("icon").startsWith("N_"));
        query.field("status").notEqual(PaaSStatus.delete);

        return query.asList();
    }

    @Override
    public void updateIcon(ObjectId id, String cpath) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("_id").equal(id);
        PaaSAppEntity paasAppEntity = query.get();
        if (null != paasAppEntity) {
            paasAppEntity.setIcon(cpath);
            datastore.save(paasAppEntity);
        }
    }

    @Override
    public List<PaaSAppEntity> getAllCustomerLinkApp() {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        return query.asList();
    }

    @Override
    public PaaSAppEntity getfirstPaasAppByAppId(String appId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(appId);
        query.field("status").notEqual(PaaSStatus.delete);
        query.offset(0);
        query.limit(1);
        List<PaaSAppEntity> list = query.asList();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<PaaSAppEntity> getLinAppListGroupByAppId(List<String> linkAppIds) {
        List<PaaSAppEntity> list = new ArrayList<>();
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").in(linkAppIds);
        query.field("appType").in(Lists.newArrayList(AppTypeEnum.LINK_APP.getAppType(), AppTypeEnum.CUSTOMER_LINK_APP.getAppType()));
        AggregationPipeline pipeline = datastore.createAggregation(TenantMenuEntity.class);
        pipeline.match(query);
        pipeline.group("appId");
        Iterator<PaaSAppEntity> resultIterator = pipeline.aggregate(PaaSAppEntity.class);
        while (resultIterator.hasNext()) {
            list.add(resultIterator.next());
        }
        return list;
    }

    @Override
    public void addTenantIdForCustomerLinkApp(String linkAppId, Integer tenantId, Integer status) {
        // 构建查询条件，查询链接应用
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(linkAppId);
        query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        
        if (WebPageGraySwitch.isAllowForAddTenantIdForCustomerLinkAppGrayEi(tenantId)) {
            // 灰度流程：根据查询结果采取不同处理策略
            List<PaaSAppEntity> existingEntities = query.asList();
            // 不存在记录，直接创建新的应用
            if (CollectionUtils.isEmpty(existingEntities)) {
                log.warn("Link application not found, cannot add tenant ID: {}", linkAppId);
                return;
            }
            // 查找当前租户ID的记录和租户ID为0的记录
            PaaSAppEntity matchingTenantEntity = null;
            PaaSAppEntity zeroTenantEntity = null;
            
            for (PaaSAppEntity entity : existingEntities) {
                if (Objects.equals(entity.getTenantId(), tenantId)) {
                    // 找到匹配当前租户的记录
                    matchingTenantEntity = entity;
                    break;
                } else if (entity.getTenantId() == 0) {
                    // 找到租户ID为0的记录
                    zeroTenantEntity = entity;
                    break;
                }
            }
            
            if (Objects.nonNull(matchingTenantEntity)) {
                // 存在当前租户ID的记录，只更新状态
                updateExistingLinkApp(matchingTenantEntity, status);
                log.info("Existing tenant ID found, updating status for link application: {}", linkAppId);
            } else if (Objects.nonNull(zeroTenantEntity)) {
                // 存在租户ID为0的记录，更新租户ID和状态
                updateZeroTenantLinkApp(zeroTenantEntity, tenantId, status);
                log.info("Zero tenant ID found, updating tenant ID and status for link application: {}", linkAppId);
            } else {
                // 不存在匹配当前租户或租户ID为0的记录，创建新记录
                log.warn("No matching tenant or zero tenant ID found, creating new record for link application: {}", linkAppId);
            }
        } else {
            // 非灰度流程：原有逻辑
            UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
            updateOperations.set("tenantId", tenantId);
            updateOperations.set("status", status);
            datastore.findAndModify(query, updateOperations, true);
        }
    }
    
    /**
     * 更新已存在的租户链接应用状态
     */
    private void updateExistingLinkApp(PaaSAppEntity entity, Integer status) {
        Query<PaaSAppEntity> updateQuery = datastore.createQuery(PaaSAppEntity.class);
        updateQuery.field("_id").equal(entity.getId());
        
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updateTime", System.currentTimeMillis());
        
        datastore.findAndModify(updateQuery, updateOperations, false, false);
    }
    
    /**
     * 更新租户ID为0的链接应用
     */
    private void updateZeroTenantLinkApp(PaaSAppEntity entity, Integer tenantId, Integer status) {
        Query<PaaSAppEntity> updateQuery = datastore.createQuery(PaaSAppEntity.class);
        updateQuery.field("_id").equal(entity.getId());
        
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.set("tenantId", tenantId);
        updateOperations.set("status", status);
        updateOperations.set("updateTime", System.currentTimeMillis());
        
        datastore.findAndModify(updateQuery, updateOperations, false, false);
    }

    @Override
    public void updateTenantIdForCustomerLinkApp(String linkAppId, Integer tenantId, Integer sourceTenantId, Integer status) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(linkAppId);
        query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        query.field("tenantId").equal(sourceTenantId);
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.set("tenantId", tenantId);
        updateOperations.set("status", status);
        datastore.findAndModify(query, updateOperations);
    }

    @Override
    public void updateCustomerLinkAppUrl(String linkAppId, String weburl, String appurl) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(linkAppId);
        query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        if (StringUtils.isNotBlank(weburl)) {
            updateOperations.set("weburl", weburl);
        }
        if (StringUtils.isNotBlank(appurl)) {
            updateOperations.set("appurl", appurl);
        }
        datastore.update(query, updateOperations);
    }

    @Override
    public void updateForLinkApp(PaaSAppEntity paaSAppEntity) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(paaSAppEntity.getTenantId());
        query.field("appId").equal(paaSAppEntity.getAppId());
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        if (StringUtils.isNotBlank(paaSAppEntity.getName())) {
            updateOperations.set("name", paaSAppEntity.getName());
        }
        if (StringUtils.isNotBlank(paaSAppEntity.getDescription())) {
            updateOperations.set("description", paaSAppEntity.getDescription());
        }
        if (StringUtils.isNotBlank(paaSAppEntity.getIcon())) {
            updateOperations.set("icon", paaSAppEntity.getIcon());
        }
        if (StringUtils.isNotBlank(paaSAppEntity.getUploadImgType())) {
            updateOperations.set("uploadImgType", paaSAppEntity.getUploadImgType());

        }
        updateOperations.set("iconIndex", StringUtils.isEmpty(paaSAppEntity.getIconIndex()) ? "" : paaSAppEntity.getIconIndex());
        if(Objects.nonNull(paaSAppEntity.getCustomIcon())) {
            updateOperations.set("customIcon", paaSAppEntity.getCustomIcon());
        }
        datastore.update(query, updateOperations);
    }

    @Override
    public PaaSAppEntity getPaasAppByParentId(Integer enterpriseId, String parentAppId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(enterpriseId);
        query.field("parentAppId").equal(parentAppId);
        return query.get();
    }

    @Override
    public void deleteByAppId(String appId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(appId);
        datastore.delete(query);
    }

    @Override
    public void deleteByAppIds(Integer enterpriseId, List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("tenantId").equal(enterpriseId);
        query.field("appId").in(appIds);
        datastore.delete(query);
    }


    @Override
    public void batchUpdateStatusByAppId(String appId, int status) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(appId);
        UpdateOperations<PaaSAppEntity> updateOperations = datastore.createUpdateOperations(PaaSAppEntity.class);
        updateOperations.set("status", status);
        updateOperations.set("updateTime", System.currentTimeMillis());
        datastore.update(query, updateOperations);
    }

    @Override
    public List<PaaSAppEntity> findByAppId(String appId) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").equal(appId);
        return query.asList();
    }

    @Override
    public List<PaaSAppEntity> getCustomerLinkAppsByAppIds(List<String> customerLinkAppIds) {
        Query<PaaSAppEntity> query = datastore.createQuery(PaaSAppEntity.class);
        query.field("appId").in(customerLinkAppIds);
        query.field("appType").equal(AppTypeEnum.CUSTOMER_LINK_APP.getAppType());
        return query.asList();
    }


    @Override
    public int getLinkAppVOListCount(GetlinkAppListArg arg) {
        Query<PaaSAppEntity> query = getLinkAppVOListQuery(arg);
        return (int) query.countAll();
    }

    private List<PaaSAppEntity> filterMultiCrossPortal(int tenantId, List<PaaSAppEntity> list) {
        if (CollectionUtils.isNotEmpty(list) && !WebPageGraySwitch.isAllowMultiCrossPortalGrayEisGray(tenantId)) {
            list = list.stream().filter(x -> !"multiCrossPortal".equals(x.getAppId())).collect(Collectors.toList());
        }
        return list;
    }
}
