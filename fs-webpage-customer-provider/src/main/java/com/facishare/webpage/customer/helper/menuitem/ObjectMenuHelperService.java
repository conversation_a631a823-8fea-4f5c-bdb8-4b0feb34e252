package com.facishare.webpage.customer.helper.menuitem;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.I18N;
import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.webpage.customer.api.constant.CustomMenuType;
import com.facishare.webpage.customer.api.model.core.SimpObjectDescription;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.metadata.model.CustomerMenuData;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.facishare.webpage.customer.model.ObjectRecordData;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.service.RemoteService;
import com.facishare.webpage.customer.util.GeneralUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 * @date 2021/7/15 2:20 下午
 * 对象自定义菜单项的相关处理
 */
@Component
public class ObjectMenuHelperService implements CustomerMenuHelperService {

    @Resource
    private ObjectService objectService;
    @Resource
    private RemoteService remoteService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private QixinI18nService qixinI18nService;
    private static final String ALL_BUSS_TYPE = "全部业务类型"; //ignoreI18n
    private static String OBJECT_MENU_DELETE_KEY = "object_menu_deleted_key";
    private static String OBJECT_MENU_DELETE = "CRM对象已被删除或禁用";  // ignoreI18n

    @Override
    public void setCustomerMenu(int tenantId, CustomerMenu customerMenu, Locale locale) {
        Map<String, String> displayNameMap = objectService.getDisplayName(tenantId, Lists.newArrayList(customerMenu.getObjectApiName()), locale);
        String displayName = displayNameMap.getOrDefault(customerMenu.getObjectApiName(), "");
        customerMenu.setObjectDisplayName(displayName);

        List<ObjectRecordData> objectRecordDataList = objectService.getObjectRecordDataList(tenantId, Lists.newArrayList(customerMenu.getObjectApiName()), locale);
        Map<String, ObjectRecordData> objectRecordDataMap = objectRecordDataList.stream().collect(Collectors.toMap(ObjectRecordData::getRecordTypeApiName, x -> x, (v1, v2) -> v1));
        ObjectRecordData objectRecordData = objectRecordDataMap.getOrDefault(customerMenu.getObjectRecordTypeApiName(), null);
        if (objectRecordData != null) {
            customerMenu.setObjectRecordName(objectRecordData.getRecordTypeName());
        }
    }

    @Override
    public void setCustomerMenuContent(int tenantId, List<CustomerMenu> customerMenuList, Locale locale) {
        if (CollectionUtils.isEmpty(customerMenuList)) {
            return;
        }
        List<SimpObjectDescription> simpObjectDescriptions = objectService.getAllDescribe(tenantId, locale);
        Map<String, SimpObjectDescription> objectDescriptionMap = simpObjectDescriptions.stream().
                collect(
                        Collectors.toMap(SimpObjectDescription::getApiName, x -> x, (x1, x2) -> x1));
        customerMenuList.forEach(x -> {
            SimpObjectDescription simpObjectDescription = objectDescriptionMap.getOrDefault(x.getObjectApiName(), null);
            //x.setContent(simpObjectDescription == null ? null : simpObjectDescription.getName());
            if (simpObjectDescription == null) {
                x.setContent(qixinI18nService.getSingleI18nValue(tenantId, OBJECT_MENU_DELETE_KEY,
                        I18N.text(I18NKey.CRM_OBJECT_TO_BE_TRANSLATED_HAS_BEEN_DELETED_OR_DISABLED), locale));
                x.setNeedHidden(true);
            } else {
                x.setContent(simpObjectDescription.getName());
            }
            x.setIcon(simpObjectDescription == null ? null : simpObjectDescription.getIcon());
        });
    }

    @Override
    public List<MetaMenuData> covertMetaMenuDataList(int tenantId, String appId, Locale locale, List<CustomerMenu> customerMenus) {
        //获取自定义菜单项关于对象的apiName
        List<String> objectApiNames = customerMenus.stream().
                map(x -> x.getObjectApiName()).
                collect(Collectors.toList());
        //获取对象业务类型的相关数据
        List<ObjectRecordData> objectRecordDataList = objectService.getObjectRecordDataList(tenantId, objectApiNames, locale);
        //构建自定义菜单项的MetaMenuData
        List<MetaMenuData> customerMenuDataList = buildCustomerMenuData(appId, customerMenus, objectRecordDataList);
        return customerMenuDataList;
    }

    @Override
    public List<CustomerMenu> filterCustomerMenuForPermission(int tenantId,
                                                              String appId,
                                                              Integer employeeId,
                                                              Long outTenantId,
                                                              Long outUid,
                                                              Integer outLinkType,
                                                              List<CustomerMenu> customerMenus,
                                                              Locale locale) {
        List<String> objectApiNames = customerMenus.stream().
                map(CustomerMenu::getObjectApiName).
                collect(Collectors.toList());
        UserInfo userInfo = GeneralUtil.buildUserInfo(tenantId, eieaConverter.enterpriseIdToAccount(tenantId), employeeId);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(outTenantId, outUid, null, outLinkType);
        Map<String, List<String>> funcAccessMap = remoteService.permissionFuncAccess(
                userInfo,
                outerUserInfo,
                appId,
                objectApiNames,
                Lists.newArrayList(WebPageConstants.FUNC_CODE_LIST),
                Maps.newHashMap());
        return customerMenus.stream().filter(x -> {
            String objectApiName = x.getObjectApiName();
            List<String> functionCodes = funcAccessMap.get(objectApiName);
            if (CollectionUtils.isEmpty(functionCodes) || !functionCodes.contains(WebPageConstants.FUNC_CODE_LIST)) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 构建自定义菜单项的MetaMenuData
     *
     * @param appId
     * @param customerMenus
     * @param objectRecordDataList
     * @return
     */
    private List<MetaMenuData> buildCustomerMenuData(String appId, List<CustomerMenu> customerMenus, List<ObjectRecordData> objectRecordDataList) {
        if (CollectionUtils.isEmpty(objectRecordDataList) || CollectionUtils.isEmpty(customerMenus)) {
            return Lists.newArrayList();
        }
        Map<String, ObjectRecordData> objectRecordDataMap = getObjectRecordDataMap(objectRecordDataList);

        return customerMenus.stream().map(x -> covertObjCustomerMenu(appId, objectRecordDataMap, x)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 以apiName-recordTypeApiName作为key，ObjectRecordData作为value
     *
     * @param objectRecordDataList
     * @return
     */
    private Map<String, ObjectRecordData> getObjectRecordDataMap(List<ObjectRecordData> objectRecordDataList) {
        if (CollectionUtils.isEmpty(objectRecordDataList)) {
            return Maps.newHashMap();
        }
        Map<String, ObjectRecordData> objectRecordDataMap = Maps.newHashMap();
        for (ObjectRecordData objectRecordData : objectRecordDataList) {
            String key = objectRecordData.getApiName() + "-" + objectRecordData.getRecordTypeApiName();
            ObjectRecordData objectRecordData1 = new ObjectRecordData();
            BeanUtils.copyProperties(objectRecordData, objectRecordData1);
            objectRecordDataMap.put(key, objectRecordData);
            objectRecordData1.setActive(true);
            objectRecordDataMap.put(objectRecordData.getApiName(), objectRecordData1);
        }
        return objectRecordDataMap;
    }

    @Nullable
    private CustomerMenuData covertObjCustomerMenu(String appId,
                                                   Map<String, ObjectRecordData> objectRecordDataMap,
                                                   CustomerMenu customerMenu) {
        ObjectRecordData objectRecordData;
        if (StringUtils.isBlank(customerMenu.getObjectRecordTypeApiName())) {
            objectRecordData = objectRecordDataMap.get(customerMenu.getObjectApiName());
            if(null == objectRecordData){
                return null;
            }
            objectRecordData.setRecordTypeName(I18N.text(I18NKey.ALL_BUSINESS_TYPES));
            objectRecordData.setRecordTypeApiName(null);
        } else {
            objectRecordData = objectRecordDataMap.get(customerMenu.getObjectApiName() + "-" + customerMenu.getObjectRecordTypeApiName());
        }
        if (objectRecordData == null) {
            return null;
        }
        CustomerMenuData customerMenuData = new CustomerMenuData();

        customerMenuData.setCustomerMenu(customerMenu);
        customerMenuData.setObjectRecordData(objectRecordData);
        customerMenuData.setAppId(appId);

        return customerMenuData;
    }

    @Override
    public Integer getMenuType() {
        return CustomMenuType.CRM_MENU_TYPE;
    }
}
