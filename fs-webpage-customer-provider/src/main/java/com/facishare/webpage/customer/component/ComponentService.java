package com.facishare.webpage.customer.component;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.objgroup.common.service.model.resource.QueryComponentsResult;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.DropListItem;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/6/3
 */
public interface ComponentService {

    List<DropListItem> getDropListItemList(int tenantId, int bizType, String bizId,
                                           List<DropListItem> businessComponents, List<DropListItem> cusComponents,
                                           Locale locale, String type, String linkAppId);

    boolean checkPrivilege(int appType, String appId, ComponentDto componentDto, Set<String> bizConfKeys, List<String> versionAndPackages, boolean needCheckPermission, List<String> openAppIds);

    boolean checkPrivilege(int bizType, String bizId, ComponentDto componentDto, Set<String> bizConfKeys,
                           List<String> versionAndPackages, boolean needCheckPermission, List<String> openAppIds,
                           String tenantId, String appId, String objectApiName);

    List<JSONObject> getComponents(int tenantId, int appType, String appId, Locale locale);

    List<DropListItem> getBusinessComponents(int tenantId, int appType, String appId, Locale locale);

    /**
     * 获取通用组件的数据
     *
     * @param tenantId
     * @param appType
     * @param appId
     * @param componentDtoList
     * @param componentLanguage
     * @return
     */
    List<DropListItem> getGeneralAndTabsComponents(int tenantId,
                                                   int appType,
                                                   String appId,
                                                   List<ComponentDto> componentDtoList,
                                                   Map<String, String> componentLanguage);

    List<DropListItem> getGeneralAndTabsComponents(int tenantId,
                                                   int bizType,
                                                   String bizId,
                                                   String appId,
                                                   List<ComponentDto> componentDtoList,
                                                   Map<String, String> componentLanguage);

    /**
     * 获取自定义组件
     *
     * @param enterpriseId
     * @param appType
     * @param appId
     * @param componentDataList
     * @param clientType
     * @return
     */
    List<DropListItem> getCusComponents(Integer enterpriseId,
                                        Integer appType,
                                        String appId,
                                        List<QueryComponentsResult.ComponentData> componentDataList,
                                        String clientType);

    /**
     * 过滤没有父节点的数据
     *
     * @param dropListItemList
     * @return
     */
    List<DropListItem> filterNoChildDropList(List<DropListItem> dropListItemList);
}
