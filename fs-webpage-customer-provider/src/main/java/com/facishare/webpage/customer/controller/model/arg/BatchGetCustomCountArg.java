package com.facishare.webpage.customer.controller.model.arg;

import com.alibaba.fastjson.annotation.JSONField;
import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * Created by shecheng on 19/10/21.
 */
@Data
public class BatchGetCustomCountArg extends BaseArg {

    @Tag(1)
    @JSONField(name = "M1")
    private String appId;

    @Tag(2)
    @JSONField(name = "M2")
    private List<String> countKey;


    @Tag(3)
    @JSONField(name = "M3")
    private List<String> countSourceData;


    @Override
    public void valid() throws Exception {

    }
}
