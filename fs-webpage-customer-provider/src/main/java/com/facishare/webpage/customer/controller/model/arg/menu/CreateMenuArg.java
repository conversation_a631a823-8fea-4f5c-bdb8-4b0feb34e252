package com.facishare.webpage.customer.controller.model.arg.menu;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.controller.model.MenuTempleVO;
import com.facishare.webpage.customer.controller.model.TenantMenuSimpleDataVo;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/24.
 */
@Data
public class CreateMenuArg extends BaseArg {
    @SerializedName("object_data")
    private MenuTempleVO menuTempleVO;
    @SerializedName("detail_menu_items")
    private List<TenantMenuSimpleDataVo> tenantMenuSimpleDataVos;
    @SerializedName("role_id_list")
    private List<String> roleIdList;
    @SerializedName("scopeList")
    private List<Scope> scopeList;
    @SerializedName("appType")
    private int appType = BizType.CRM.getType();
    @SerializedName("appId")
    private String appId;

    @Override
    public void valid() throws WebPageException {
        if (BizType.CRM.getType() != appType){
            return;
        }
        if (ObjectUtils.isEmpty(menuTempleVO)
                || CollectionUtils.isEmpty(tenantMenuSimpleDataVos)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        // 可能存在脏数据
        if(CollectionUtils.isNotEmpty(tenantMenuSimpleDataVos)){
            tenantMenuSimpleDataVos.removeAll(Collections.singletonList(null));
        }
    }
}
