package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.qixin.i18n.QixinI18nService;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.common.WebPageCommonService;
import com.facishare.webpage.customer.config.AddMenuItemConfig;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.config.TenantUiConfig;
import com.facishare.webpage.customer.common.model.TenantUiInfoExt;
import org.apache.commons.lang3.StringUtils;
import com.facishare.webpage.customer.constant.MenuStatus;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.constant.TenantConfigKey;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.model.MenuTemplateDto;
import com.facishare.webpage.customer.core.config.GroupMetaConfig;
import com.facishare.webpage.customer.core.model.GroupMetaData;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.facishare.webpage.customer.dao.TenantConfigDao;
import com.facishare.webpage.customer.dao.TenantMenuDao;
import com.facishare.webpage.customer.dao.UserMenuDao;
import com.facishare.webpage.customer.dao.entity.MenuDataEntity;
import com.facishare.webpage.customer.dao.entity.TenantConfigEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.facishare.webpage.customer.metadata.MetaMenuService;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.MenuData;
import com.facishare.webpage.customer.model.MenuItem;
import com.facishare.webpage.customer.model.MenuTemple;
import com.facishare.webpage.customer.model.MenuTempleAO;
import com.facishare.webpage.customer.service.TenantMenuService;
import com.facishare.webpage.customer.system.MenuSystemService;
import com.facishare.webpage.customer.util.MenuUtil;
import com.facishare.webpage.customer.util.TempleIdUtil;
import com.facishare.webpage.customer.util.TenantMenuDataUtil;
import com.facishare.webpage.customer.util.TenantMenuTempleUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangyi on 2019/9/16.
 */
public class TenantMenuServiceImpl implements TenantMenuService {

    private static final Logger logger = LoggerFactory.getLogger(TenantMenuServiceImpl.class);

    @Resource
    private TenantMenuDao tenantMenuDao;
    @Resource
    private TenantConfigDao tenantConfigDao;
    @Resource
    private MenuSystemService menuSystemService;
    @Resource
    private UserMenuDao userMenuDao;
    @Resource
    private MetaMenuService metaMenuService;
    @Resource
    private AddMenuItemConfig addMenuItemConfig;
    @Resource
    private AppMenuConfig appMenuConfig;
    @Resource
    private GroupMetaConfig groupMetaConfig;
    @Resource
    private WebPageCommonService webPageCommonService;
    @Resource
    private LanguageService languageService;
    @Resource
    private CheckService checkService;

    @Resource
    private QixinI18nService qixinI18nService;

    @Autowired
    private I18nService i18nService;

    @Override
    public MenuTempleAO findTenantMenuById(DataSourceEnv env, String menuId, int enterpriseId, Locale locale, boolean previewNewCrmFlag) {
        SlowLog slowLog = GlobalStopWatch.create("findTenantMenuById", 100L);
        TenantMenuEntity tenantMenuEntity = menuSystemService.getTenantMenuEntityByMenuId(enterpriseId, menuId);    // 查数据库/模板企业的菜单设置
        if (null == tenantMenuEntity) {
            throw new WebPageException(InterErrorCode.MENU_DATA_NOT_FUND);
        }
        slowLog.lap("getDataByDataId");
        if (ObjectUtils.isEmpty(tenantMenuEntity)) {
            return null;
        }
        if (checkIsSystem(tenantMenuEntity)) {
            tenantMenuEntity = changeSystemMenu(tenantMenuEntity);
        }
        slowLog.lap("changeSystemMenu");
        if (tenantMenuEntity == null) {
            return null;
        }
        List<MetaMenuData> metaMenuDataList = webPageCommonService.queryAllMetaMenuDataList(env, enterpriseId,
                tenantMenuEntity.getAppId(), tenantMenuEntity.getMenuDataEntities(), locale, slowLog, previewNewCrmFlag);
        slowLog.lap("queryAllMetaMenuDataList");
        List<GroupMetaData> groupMetaDataList = groupMetaConfig.getGroupMetaDataListByAppId(tenantMenuEntity.getAppId());
        slowLog.lap("getGroupMetaDataListByAppId");
        //预置模板追加菜单
        if (SourceType.SYSTEM.equals(tenantMenuEntity.getSourceType())) {
            List<MenuDataEntity> menuDataEntities = addMenuDataEntities(tenantMenuEntity.getAppId(), metaMenuDataList, groupMetaDataList, tenantMenuEntity.getMenuDataEntities());
            tenantMenuEntity.setMenuDataEntities(menuDataEntities);
        }
        slowLog.lap("addMenuDataEntities");
        //用户态接口调用，使用非实时查询接口, 老版crm菜单视图名称
        setMenuTranslateName(enterpriseId, Lists.newArrayList(tenantMenuEntity), locale);
        //修改  预制模版才能追加菜单
        MenuTemple menuTemple = TenantMenuTempleUtil.buildMenuTemple(tenantMenuEntity,
                metaMenuDataList,
                addMenuItemConfig.getAddMenuItems(tenantMenuEntity.getAppId()),
                webPageCommonService.checkNeedAddMenuApp(tenantMenuEntity.getAppId()) && SourceType.SYSTEM.equals(tenantMenuEntity.getSourceType()),
                checkService.checkGoNewCRM(getRealTenantId(enterpriseId)));
        languageService.setCustomerGroupLanguageByMenuTemple(enterpriseId, menuTemple, locale);
        slowLog.lap("buildMenuTemple");
        //赋值名称

        slowLog.stop("over findTenantMenuById");
        return MenuTempleAO.builder().menuTemple(menuTemple).metaMenuDataList(metaMenuDataList).build();
    }

    private List<MenuDataEntity> addMenuDataEntities(String appId, List<MetaMenuData> metaMenuDataList, List<GroupMetaData> groupMetaDataList, List<MenuDataEntity> menuDataEntityList) {
        if (CollectionUtils.isEmpty(metaMenuDataList) || CollectionUtils.isEmpty(groupMetaDataList) || CollectionUtils.isEmpty(menuDataEntityList)) {
            return menuDataEntityList;
        }
        Map<String, MetaMenuData> metaMenuDataMap = MenuUtil.getMetaMenuDataMap(metaMenuDataList);
        Map<String, MenuDataEntity> menuDataEities = MenuUtil.getMenuDataEntityMap(appId, menuDataEntityList);
        Set<String> metaMenuDataApiNames = metaMenuDataMap.keySet();
        Set<String> entityApiNames = menuDataEities.keySet();

        Map<String, MenuDataEntity> groupMenuDataEntities = menuDataEntityList.stream().
                filter(x -> MenuType.GROUP.equals(x.getType())).
                collect(Collectors.toMap(MenuDataEntity::getName, x -> x, (v1, v2) -> v1));

        for (GroupMetaData groupMetaData : groupMetaDataList) {
            //过滤分组元数据中要追加的菜单
            List<String> addMenus = groupMetaData.getMenus().stream().
                    filter(y -> metaMenuDataApiNames.contains(MenuUtil.getUniqueKey(groupMetaData.getMenuAppId(), y))
                            && !entityApiNames.contains(MenuUtil.getUniqueKey(groupMetaData.getMenuAppId(), y))).
                    collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(addMenus)) {
                continue;
            }
            MenuDataEntity targetMenuDataEntity = groupMenuDataEntities.get(groupMetaData.getGroupName());
            if (targetMenuDataEntity != null && MenuType.GROUP.equals(targetMenuDataEntity.getType())) {
                continue;
            }
            //重新set分组元数据
            groupMetaData.setMenus(addMenus);
            //过滤出要追加分组里菜单的元数据
            List<String> addMenuUniqueList = MenuUtil.getUniqueApiName(groupMetaData.getMenuAppId(), addMenus);

            List<MetaMenuData> addMetaMenuDataList = addMenuUniqueList.stream().
                    filter(x -> metaMenuDataMap.containsKey(x)).
                    map(x -> metaMenuDataMap.get(x)).
                    filter(Objects::nonNull).collect(Collectors.toList());

            MenuDataEntity afterMenuDataEntity;
            if (MenuType.GROUP.equals(groupMetaData.getAfterType())) {
                afterMenuDataEntity = groupMenuDataEntities.get(groupMetaData.getAfterApiName());
            } else {
                //获取该分组追加在哪个对象或分组后边
                afterMenuDataEntity = menuDataEities.get(MenuUtil.getUniqueKey(groupMetaData.getAfterAppId(), groupMetaData.getAfterApiName()));
            }
            if (afterMenuDataEntity == null) {
                List<MenuDataEntity> notContainGroupMenu = menuDataEntityList.stream().filter(x -> StringUtils.isEmpty(x.getGroupApiName())).collect(Collectors.toList());
                int index = notContainGroupMenu.size() == 0 ? 0 : notContainGroupMenu.size() - 1;
                afterMenuDataEntity = notContainGroupMenu.get(index);
            }
            int nextNumber = 0;
            if (afterMenuDataEntity != null) {
                nextNumber = afterMenuDataEntity.getOrderNumber() + 1;
            }
            menuDataEntityList = addGroupMenuDataEntity(groupMetaData, addMetaMenuDataList, menuDataEntityList, nextNumber);
        }
        return menuDataEntityList;
    }

    private List<MenuDataEntity> addGroupMenuDataEntity(GroupMetaData groupMetaData, List<MetaMenuData> addMetaMenuDataList, List<MenuDataEntity> menuDataEntityList, int nextNumber) {
        if (CollectionUtils.isEmpty(menuDataEntityList)) {
            return Lists.newArrayList();
        }
        for (MenuDataEntity menuDataEntity : menuDataEntityList) {
            if (StringUtils.isEmpty(menuDataEntity.getGroupApiName()) && menuDataEntity.getOrderNumber() >= nextNumber) {
                int orderNumber = menuDataEntity.getOrderNumber();
                orderNumber++;
                menuDataEntity.setOrderNumber(orderNumber);
            }
        }
        //增加分组
        menuDataEntityList.add(addGroupMenuDataEntity(nextNumber, groupMetaData));
        //增加菜单
        menuDataEntityList.addAll(addMenuDataEntities(groupMetaData.getMenuAppId(), groupMetaData.getGroupApiName(), addMetaMenuDataList));
        return menuDataEntityList;
    }

    private MenuDataEntity addGroupMenuDataEntity(int nextNumber, GroupMetaData groupMetaData) {
        MenuDataEntity groupMenuDataEntity = new MenuDataEntity();
        groupMenuDataEntity.setApiName(groupMetaData.getGroupApiName());
        groupMenuDataEntity.setType(MenuType.GROUP);
        groupMenuDataEntity.setOrderNumber(nextNumber);
        groupMenuDataEntity.setName(groupMetaData.getGroupName());
        groupMenuDataEntity.setIsHidden(false);
        return groupMenuDataEntity;
    }

    private List<MenuDataEntity> addMenuDataEntities(String menuAppId, String groupApiName, List<MetaMenuData> addMetaMenuDataList) {
        List<MenuDataEntity> menuDataEntityList = Lists.newArrayList();

        for (int i = 0; i < addMetaMenuDataList.size(); i++) {
            MetaMenuData metaMenuData = addMetaMenuDataList.get(i);

            MenuDataEntity menuDataEntity = new MenuDataEntity();
            menuDataEntity.setApiName(metaMenuData.getApiName());
            menuDataEntity.setName(metaMenuData.getName());
            menuDataEntity.setIsHidden(false);
            menuDataEntity.setOrderNumber(i);
            menuDataEntity.setAppId(menuAppId);
            menuDataEntity.setType(metaMenuData.getMenuType());
            menuDataEntity.setGroupApiName(groupApiName);

            menuDataEntityList.add(menuDataEntity);
        }

        return menuDataEntityList;
    }

    @Override
    public boolean checkIsSystem(TenantMenuEntity tenantMenuEntity) {
        if (StringUtils.equals(tenantMenuEntity.getSourceType(), SourceType.SYSTEM) && !tenantMenuEntity.isChange()) {
            return true;
        }
        return false;
    }

    @Override
    public TenantMenuEntity changeSystemMenu(TenantMenuEntity tenantMenuEntity) {
        TenantMenuEntity tenantMenuTempleEntity = tenantMenuDao.findTenantMenuById(tenantMenuEntity.getSourceId());
        if (tenantMenuTempleEntity == null) {
            return null;
        }
        tenantMenuEntity.setName(tenantMenuTempleEntity.getName());
        tenantMenuEntity.setDescription(tenantMenuTempleEntity.getDescription());
        tenantMenuEntity.setScopes(tenantMenuTempleEntity.getScopes());
        tenantMenuEntity.setMenuDataEntities(tenantMenuTempleEntity.getMenuDataEntities());
        tenantMenuEntity.setSourceType(SourceType.SYSTEM);
        return tenantMenuEntity;
    }

    public List<TenantMenuEntity> batchGetOldCRMMenuEntity(int enterpriseId, int appType, String appId) {
        List<TenantMenuEntity> tenantMenuEntities = menuSystemService.queryTenantMenuEntitiesByAppId(enterpriseId, appType, appId);
        List<String> sourceIds = tenantMenuEntities.stream().filter(this::checkIsSystem)
                .map(TenantMenuEntity::getSourceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, TenantMenuEntity> sourceEntityMap = tenantMenuDao.getBatchTenantMenuByIds(sourceIds, null)
                .stream().collect(Collectors.toMap(TenantMenuEntity::getId, x -> x, (o1, o2) -> o1));
        tenantMenuEntities.stream().filter(this::checkIsSystem).forEach(x -> {
            TenantMenuEntity sourceEntity = sourceEntityMap.getOrDefault(x.getSourceId(), null);
            if (Objects.nonNull(sourceEntity)) {
                x.setName(sourceEntity.getName());
                x.setDescription(sourceEntity.getDescription());
                x.setScopes(sourceEntity.getScopes());
                x.setMenuDataEntities(sourceEntity.getMenuDataEntities());
                x.setSourceType(SourceType.SYSTEM);
            }
        });

        return tenantMenuEntities;
    }

    @Override
    public MenuTemple insertOrUpdateTenantMenu(DataSourceEnv env, MenuTemplateDto menuTemplateDto, Locale locale) {

        if (menuTemplateDto == null) {
            return null;
        }

        String menuId = menuTemplateDto.getMenuId();
        String appId = menuTemplateDto.getAppId();
        int tenantId = menuTemplateDto.getTenantId();
        int employeeId = menuTemplateDto.getEmployeeId();

        TenantMenuEntity tenantMenuEntity;
        if (ObjectUtils.isEmpty(menuId)) {
            //设置新的菜单的不变的属性
            tenantMenuEntity = setNewMenuProperty(menuTemplateDto);
        } else {
            tenantMenuEntity = menuSystemService.getTenantMenuEntityByMenuId(tenantId, menuId);
        }

        if (tenantMenuEntity == null) {
            return null;
        }
        tenantMenuEntity.setChange(true);
        tenantMenuEntity.setDealHomeFlag(true);

        List<MenuItem> menuItems = menuTemplateDto.getMenuItemList();
        tenantMenuEntity.setMenuDataEntities(TenantMenuDataUtil.buildMenuDataEntityFromMenuItems(menuItems));
        tenantMenuEntity.setUpdaterId(employeeId);
        tenantMenuEntity.setUpdateTime(System.currentTimeMillis());
        tenantMenuEntity.setIsShowMenuIcon(menuTemplateDto.getIsShowMenuIcon());
        tenantMenuEntity.setHiddenQuickCreate(menuTemplateDto.getHiddenQuickCreate());
        if (StringUtils.isNotEmpty(menuTemplateDto.getSourceType())) {
            tenantMenuEntity.setSourceType(menuTemplateDto.getSourceType());
        }

        String name = menuTemplateDto.getName();
        if (StringUtils.isNotEmpty(name)) {
            tenantMenuEntity.setName(name);
        }

        String description = menuTemplateDto.getDescription();
        if (StringUtils.isNotEmpty(description)) {
            tenantMenuEntity.setDescription(description);
        }

        setMenuScope(appId, tenantMenuEntity, menuTemplateDto.getRoleIdList(), menuTemplateDto.getScopeList());

        int appType = menuTemplateDto.getAppType();
        if (BizType.CRM.getType() == appType) {
            tenantMenuEntity = tenantMenuDao.findAndModify(tenantId, tenantMenuEntity);
        } else {
            tenantMenuEntity = menuSystemService.createOrUpdate(tenantId, menuId, tenantMenuEntity);
        }
        List<MetaMenuData> metaMenuDataList = metaMenuService.getMetaMenuList(env, tenantId, appId, locale, false);
        syncMultiLanguageWithMenu(tenantId, menuId, menuItems, tenantMenuEntity, locale);
        return TenantMenuTempleUtil.buildMenuTemple(tenantMenuEntity,
                metaMenuDataList,
                addMenuItemConfig.getAddMenuItems(appId),
                webPageCommonService.checkNeedAddMenuApp(tenantMenuEntity.getAppId()),
                checkService.checkGoNewCRM(getRealTenantId(tenantId)));
    }

    private void syncMultiLanguageWithMenu(int tenantId, String menuId, List<MenuItem> menuItems, TenantMenuEntity tenantMenuEntity, Locale locale) {
        languageService.save4Translate(tenantId, TranslateI18nUtils.getCrmMenuNametranslateKey(tenantMenuEntity.getId()), locale, tenantMenuEntity.getName());
        syncMenuGroupTransValue(menuId, MenuUtil.getGroupMenuItem(menuItems), locale, tenantId);
    }

    private void syncMenuGroupTransValue(String menuId, List<MenuItem> groupMenuItems, Locale locale, Integer enterpriseId) {
        if (CollectionUtils.isEmpty(groupMenuItems)) {
            return;
        }
        List<I18nTrans.TransArg> i18nTrans = groupMenuItems.stream()
                .map(it -> {
                    String customKey = TranslateI18nUtils.getWebAppViewGroupName(menuId, it.getApiName());
                    String name = it.getMenuData().getName();
                    return I18nTrans.TransArg.builder()
                            .name(name)
                            .customKey(customKey)
                            .oldKeyList(Lists.newArrayList(UIPaaSI18NKey.GROUP_PREFIX + it.getApiName(),
                                    UIPaaSI18NKey.GROUP_PREFIX + name))
                            .preKeyList(Lists.newArrayList(UIPaaSI18NKey.GROUP_PREFIX + it.getApiName(),
                                    UIPaaSI18NKey.GROUP_PREFIX + name))
                            .build();
                }).collect(Collectors.toList());
        i18nService.syncTransValueIncludePreKeyV2(enterpriseId, i18nTrans, locale.toLanguageTag());
    }


    //设置新的菜单的不变的属性
    private TenantMenuEntity setNewMenuProperty(MenuTemplateDto menuTemplateDto) {
        TenantMenuEntity tenantMenuEntity = new TenantMenuEntity();
        tenantMenuEntity.setAppType(menuTemplateDto.getAppType());
        tenantMenuEntity.setAppId(menuTemplateDto.getAppId());
        tenantMenuEntity.setId(TempleIdUtil.buildId(menuTemplateDto.getTenantId()));
        tenantMenuEntity.setSourceType(SourceType.CUSTOMER);
        //临时状态
        if (StringUtils.equals(WebPageConstants.APP_CRM, menuTemplateDto.getAppId()) || menuTemplateDto.getAppType() == BizType.PAAS.getType()) {
            tenantMenuEntity.setStatus(MenuStatus.enableStatus);
        } else {
            tenantMenuEntity.setStatus(MenuStatus.tempStatus);
        }
        tenantMenuEntity.setTenantId(menuTemplateDto.getTenantId());
        tenantMenuEntity.setCreatorId(menuTemplateDto.getEmployeeId());
        tenantMenuEntity.setCreateTime(System.currentTimeMillis());

        return tenantMenuEntity;
    }

    //设置菜单的适用范围；roleIdList为老的角色的适用范围/scopeList为新版的适用范围
    private void setMenuScope(String appId, TenantMenuEntity tenantMenuEntity, List<String> roleIdList, List<Scope> scopeList) {

        if (tenantMenuEntity == null || (CollectionUtils.isEmpty(roleIdList) && CollectionUtils.isEmpty(scopeList))) {
            return;
        }

        if (CollectionUtils.isNotEmpty(scopeList)) {
            tenantMenuEntity.setScopes(ScopesUtil.buildScopesToString(scopeList));
            return;
        }

        if (CollectionUtils.isNotEmpty(roleIdList)) {
            List<Scope> scopes = roleIdList.stream().map(x -> {
                Scope scope = new Scope();
                scope.setDataId(x);
                scope.setDataType(StringUtils.equals(appId, WebPageConstants.APP_CRM) ? ScopeType.Role.getType() : ScopeType.OutRole.getType());
                return scope;
            }).collect(Collectors.toList());
            tenantMenuEntity.setScopes(ScopesUtil.buildScopesToString(scopes));
            return;
        }

    }

    @Override
    public MenuTemple temp2Normal(DataSourceEnv env, String webMenuId, String templeId, int appType, Locale locale) {
        TenantMenuEntity tenantMenuEntity = tenantMenuDao.temp2Normal(webMenuId, templeId, appType);
        List<MetaMenuData> metaMenuDataList = metaMenuService.getMetaMenuList(env, tenantMenuEntity.getTenantId(), tenantMenuEntity.getAppId(), locale, false);
        return TenantMenuTempleUtil.buildMenuTemple(tenantMenuEntity,
                metaMenuDataList,
                addMenuItemConfig.getAddMenuItems(tenantMenuEntity.getAppId()),
                webPageCommonService.checkNeedAddMenuApp(tenantMenuEntity.getAppId()),
                checkService.checkGoNewCRM(getRealTenantId(tenantMenuEntity.getTenantId())));
    }

    @Override
    public List<MenuItem> filterNotSupportMenuData(List<MenuItem> menuItems, List<MetaMenuData> metaMenuDataList) {

        List<String> metaMenuApiNames = metaMenuDataList.stream().
                map(metaMenuData -> MenuUtil.getUniqueKey(metaMenuData.getAppId(), metaMenuData.getApiName())).
                collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(menuItems) && CollectionUtils.isNotEmpty(metaMenuApiNames)) {
            return menuItems.stream().
                    filter(x -> (
                            x.getMenuItemType().equals(MenuType.GROUP) ||
                                    metaMenuApiNames.contains(MenuUtil.getUniqueKey(x.getMetaMenuData().getAppId(), x.getApiName())))).
                    collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }


    @Override
    public List<MenuTemple> getMenuTempleList(int enterpriseId, String appId, List<MetaMenuData> metaMenuDataList, Locale locale) {
        Integer appType = BizType.CRM.getType();
        if (WebPageUtils.checkPaaSApp(appId)) {
            appType = BizType.PAAS.getType();
        }
        logger.info("queryTenantMenuEntitiesByAppId.param enterpriseId={},appType = {},appId={}", enterpriseId, appType, appId);
        List<TenantMenuEntity> tenantMenuEntities = menuSystemService.queryTenantMenuEntitiesByAppId(enterpriseId, appType, appId);
        logger.info("queryTenantMenuEntitiesByAppId.result menuIds={}", JSONObject.toJSONString(tenantMenuEntities.stream().map(x -> x.getId()).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(tenantMenuEntities)) {
            return Lists.newArrayList();
        }

        tenantMenuEntities = changeSystemMenusAndSorted(tenantMenuEntities);
        List<TenantMenuEntity> menuEntityList = changeTenantMenuEntities(appId, tenantMenuEntities, metaMenuDataList);
        setMenuTranslateName(enterpriseId, menuEntityList, locale);
        List<MenuTemple> menuTemples = TenantMenuTempleUtil.buildMenuTemples(menuEntityList,
                metaMenuDataList,
                addMenuItemConfig.getAddMenuItems(appId),
                webPageCommonService.checkNeedAddMenuApp(appId),
                checkService.checkGoNewCRM(getRealTenantId(enterpriseId)));
        menuTemples.forEach(x -> setHiddenGroupMenu2Hidden(x));
        return menuTemples;
    }

    @Override
    public MenuTemple getMenuTempleListById(int enterpriseId, String appId, List<MetaMenuData> metaMenuDataList, Locale locale, String menuId) {
        TenantMenuEntity tenantMenuEntity = menuSystemService.getTenantMenuEntityByMenuId(enterpriseId, menuId);    // 查数据库/模板企业的菜单设置
        List<TenantMenuEntity> tenantMenuEntities = changeSystemMenusAndSorted(Lists.newArrayList(tenantMenuEntity));
        if (CollectionUtils.isEmpty(tenantMenuEntities)) {
            return null;
        }
        changeTenantMenuEntities(appId, tenantMenuEntities, metaMenuDataList);
        setMenuTranslateName(enterpriseId, tenantMenuEntities, locale);

        MenuTemple menuTemple = TenantMenuTempleUtil.buildMenuTemple(tenantMenuEntity,
                metaMenuDataList,
                addMenuItemConfig.getAddMenuItems(appId),
                webPageCommonService.checkNeedAddMenuApp(appId),
                checkService.checkGoNewCRM(getRealTenantId(enterpriseId)));
        setHiddenGroupMenu2Hidden(menuTemple);
        return menuTemple;
    }

    private List<TenantMenuEntity> changeSystemMenusAndSorted(List<TenantMenuEntity> tenantMenuEntities) {
        tenantMenuEntities = tenantMenuEntities.stream().
                filter(x -> MenuStatus.deleteStatus != x.getStatus()).
                map(x -> checkIsSystem(x) ? changeSystemMenu(x) : x).   // 获取预置菜单视图
                        filter(Objects::nonNull).
                collect(Collectors.toList());
        //对租户级的菜单做排序
        //MenuUtil.sortTenantMenuEntities(tenantMenuEntities);
        tenantMenuEntities = tenantMenuEntities.stream().
                sorted(
                        Comparator.comparing(TenantMenuEntity::getSourceType).reversed().
                                thenComparing(Comparator.comparing(TenantMenuEntity::getPriorityLevel).reversed())
                ).
                collect(Collectors.toList());
        return tenantMenuEntities;
    }

    @Override
    public List<MenuTemple> getSimpleMenuTempleList(int enterpriseId, String appId, Locale locale) {
        int appType = WebPageUtils.checkPaaSApp(appId) ? BizType.PAAS.getType() : BizType.CRM.getType();
        List<TenantMenuEntity> tenantMenuEntities = menuSystemService.querySimpleTenantMenuEntitiesByAppId(enterpriseId, appType, appId);
        if (CollectionUtils.isEmpty(tenantMenuEntities)) {
            return Lists.newArrayList();
        }
        List<TenantMenuEntity> menuEntities = changeSystemMenusAndSorted(tenantMenuEntities);
        setMenuTranslateName(enterpriseId, menuEntities, locale);
        return TenantMenuTempleUtil.buildMenuTemples(menuEntities,
                null,
                addMenuItemConfig.getAddMenuItems(appId),
                webPageCommonService.checkNeedAddMenuApp(appId),
                checkService.checkGoNewCRM(getRealTenantId(enterpriseId)));
    }

    private int getRealTenantId(int enterpriseId) {
        if (Objects.isNull(RequestContextManager.getUser())) {
            return enterpriseId;
        }
        return RequestContextManager.getUser().getTenantId();
    }

    private void setMenuTranslateName(int tenantId, List<TenantMenuEntity> menuTempleList, Locale locale) {
        List<I18nTrans.TransArg> transArgList = menuTempleList.stream().map(x -> {
            String key = TranslateI18nUtils.getCrmMenuNametranslateKey(x.getId());
            String keyDelEi = TranslateI18nUtils.delEiInKey(String.valueOf(tenantId), key);
            List<String> preKeys = Lists.newArrayList(key);
            if (StringUtils.equals(x.getSourceType(), SourceType.SYSTEM)) {
                preKeys.add(TranslateI18nUtils.delAllEi(x.getSourceId()));
                preKeys.add(x.getSourceId());
            }
            return I18nTrans.TransArg.builder()
                    .name(x.getName())
                    .customKey(keyDelEi)
                    .preKeyList(preKeys)
                    .build();
        }).collect(Collectors.toList());
        Map<String, String> transValue = i18nService.getTransValueIncludePreKey(tenantId, transArgList, locale.toLanguageTag());
        menuTempleList.forEach(x -> {
            String key = TranslateI18nUtils.getCrmMenuNametranslateKey(x.getId());
            String keyDelEi = TranslateI18nUtils.delEiInKey(String.valueOf(tenantId), key);
            String translateName = transValue.get(keyDelEi);
            if (StringUtils.isNotBlank(translateName)) {
                x.setName(translateName);
                x.setI18nKeyList(Lists.newArrayList(keyDelEi, key, TranslateI18nUtils.delAllEi(x.getSourceId()), x.getSourceId()));
            }
        });
    }

    /**
     * 对分组隐藏，分组下的菜单没有隐藏的，统一设置成隐藏
     *
     * @param menuTemple
     */
    private void setHiddenGroupMenu2Hidden(MenuTemple menuTemple) {
        List<MenuItem> menuItems = menuTemple.getMenuItems();
        if (CollectionUtils.isEmpty(menuItems)) {
            return;
        }
        List<String> groupApiNames = menuItems.stream().
                filter(x -> null != x && MenuType.GROUP.equals(x.getMenuItemType()) && null != x.getMenuData() && null != x.getMenuData().getIsHidden() && !x.getMenuData().getIsHidden()).
                map(MenuItem::getApiName).
                collect(Collectors.toList());

        List<MenuItem> newMenuItems = menuItems.stream().map(x -> {
            if (MenuType.GROUP.equals(x.getMenuItemType())) {
                return x;
            }
            MenuData menuData = x.getMenuData();
            if (StringUtils.isEmpty(menuData.getGroupApiName()) || groupApiNames.contains(menuData.getGroupApiName())) {
                return x;
            }
            menuData.setIsHidden(true);
            return x;
        }).collect(Collectors.toList());
        menuTemple.setMenuItems(newMenuItems);
    }

    //对系统级菜单进行添加分组
    private List<TenantMenuEntity> changeTenantMenuEntities(String appId, List<TenantMenuEntity> tenantMenuEntities, List<MetaMenuData> metaMenuDataList) {

        if (CollectionUtils.isEmpty(tenantMenuEntities)) {
            return Lists.newArrayList();
        }
        List<GroupMetaData> groupMetaDataList = groupMetaConfig.getGroupMetaDataListByAppId(appId);

        tenantMenuEntities.stream().filter(x -> SourceType.SYSTEM.equals(x.getSourceType())).forEach(x -> {
            List<MenuDataEntity> menuDataEntities = x.getMenuDataEntities();
            List<MenuDataEntity> addMenuDataEntities = addMenuDataEntities(appId, metaMenuDataList, groupMetaDataList, menuDataEntities);
            x.setMenuDataEntities(addMenuDataEntities);
        });
        return tenantMenuEntities;
    }

    @Override
    public boolean disableMenu(String menuId) {
        tenantMenuDao.updateStatus(menuId, MenuStatus.disableStatus);
        return true;
    }

    @Override
    public boolean enableMenu(String menuId) {
        tenantMenuDao.updateStatus(menuId, MenuStatus.enableStatus);
        return true;
    }

    @Override
    public boolean deleteMenu(String menuId) {
        tenantMenuDao.updateStatus(menuId, MenuStatus.deleteStatus);
        return true;
    }

    @Override
    public boolean isHiddenSystemMenu(int enterpriseId) {
        TenantConfigEntity configEntity = tenantConfigDao.getTenantValueByKey(enterpriseId, TenantConfigKey.HIDDEN_SYSTEM_MENU);
        if (configEntity == null) {
            return false;
        }
        return StringUtils.equals(configEntity.getValue(), Boolean.TRUE.toString());
    }

    @Override
    public boolean setHiddenSystemMenu(int enterpriseId, boolean value) {
        tenantConfigDao.setTenantValue(enterpriseId, TenantConfigKey.HIDDEN_SYSTEM_MENU, value ? Boolean.TRUE.toString() : Boolean.FALSE.toString());
        return true;
    }

    @Override
    public boolean validateMenuLimit(int enterpriseId, String appId) {
        if (!BizType.CRM.getDefaultAppId().equals(appId)) {
            return true;
        }
        if (checkService.checkGoNewCRM(enterpriseId)) {
            return true;
        }
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuDao.findAllTenantMenuByAppId(enterpriseId, appId);
        if (CollectionUtils.isEmpty(tenantMenuEntities)) {
            return true;
        }
        tenantMenuEntities = tenantMenuEntities.stream().
                filter(tenantMenuEntity -> {
                    if (MenuStatus.deleteStatus != tenantMenuEntity.getStatus() && MenuStatus.tempStatus != tenantMenuEntity.getStatus()) {
                        return true;
                    }
                    if (SourceType.USER_CUSTOMER.equals(tenantMenuEntity.getSourceType())) {
                        return true;
                    }
                    return false;
                }).
                collect(Collectors.toList());
        return tenantMenuEntities.size() < appMenuConfig.getMaxMenuLimit(enterpriseId, appId);
    }

    @Override
    public boolean validateMenuName(int enterpriseId, String appId, String name) {
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuDao.findTenantMenuByName(enterpriseId, appId, name);
        return CollectionUtils.isEmpty(tenantMenuEntities);
    }

    @Override
    public MenuTemple findTenantSystemMenuById(String menuId, int enterpriseId, String appId, Locale locale) {
        TenantMenuEntity tenantMenuEntity = tenantMenuDao.findTenantMenuById(menuId);
        if (ObjectUtils.isEmpty(tenantMenuEntity)) {
            return null;
        }
        if (StringUtils.equals(tenantMenuEntity.getSourceType(), SourceType.SYSTEM)) {
            tenantMenuEntity = tenantMenuDao.findTenantMenuById(tenantMenuEntity.getSourceId());
        }
        List<MetaMenuData> metaMenuDataList = metaMenuService.getMetaMenuList(DataSourceEnv.INNER, enterpriseId, appId, locale, false);
        return TenantMenuTempleUtil.buildMenuTemple(tenantMenuEntity,
                metaMenuDataList,
                addMenuItemConfig.getAddMenuItems(appId),
                webPageCommonService.checkNeedAddMenuApp(tenantMenuEntity.getAppId()),
                checkService.checkGoNewCRM(getRealTenantId(enterpriseId)));
    }

    @Override
    public List<TenantMenuEntity> getTenantMenuList(int tenantId, String appId, boolean onlySystem) {
        List<TenantMenuEntity> tenantMenuList = tenantMenuDao.getTenantMenuList(tenantId, appId, onlySystem);
        if (CollectionUtils.isEmpty(tenantMenuList)) {
            return Lists.newArrayList();
        }
        List<TenantMenuEntity> tenantMenuEntities = Lists.newArrayList();
        tenantMenuList.stream().forEach(tenantMenuEntity -> {
            if (StringUtils.equals(tenantMenuEntity.getSourceType(), SourceType.SYSTEM) && !tenantMenuEntity.isChange()) {
                TenantMenuEntity tenantMenuById = tenantMenuDao.findTenantMenuById(tenantMenuEntity.getSourceId());
                tenantMenuEntity.setScopes(tenantMenuById.getScopes());
                tenantMenuEntity.setName(tenantMenuById.getName());
                tenantMenuEntity.setDescription(tenantMenuById.getDescription());
                tenantMenuEntity.setMenuDataEntities(tenantMenuById.getMenuDataEntities());
            }
            tenantMenuEntities.add(tenantMenuEntity);
        });
        return tenantMenuEntities.stream().filter(tenantMenuEntity -> tenantMenuEntity.getStatus() != MenuStatus.deleteStatus).collect(Collectors.toList());
    }

    @Override
    public boolean updateTenantMenuEntity(TenantMenuEntity tenantMenuEntity) {
        TenantMenuEntity menuEntity = tenantMenuDao.updateTenantMenuEntity(tenantMenuEntity);
        if (menuEntity == null) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public List<TenantMenuEntity> copyTenantMenu(int fromTenantId, int toTenantId, int appType) {
        //先删除toTenantId的数据
        List<TenantMenuEntity> tenantMenuList = tenantMenuDao.getTenantMenuByAppType(toTenantId, appType);

        List<String> tenantMenuIds = tenantMenuList.stream().map(tenantMenuEntity -> tenantMenuEntity.getId()).collect(Collectors.toList());
        int deleteMenus = deleteMenus(tenantMenuIds);
        userMenuDao.deleteAllUserMenu(toTenantId);
        logger.info("deleteMenus count:{}", deleteMenus);

        //进行copy
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuDao.getTenantMenuByAppType(fromTenantId, appType);
        List<TenantMenuEntity> tenantMenuEntityList = Lists.newArrayList();
        for (TenantMenuEntity tenantMenuEntity : tenantMenuEntities) {
            tenantMenuEntity.setId(TempleIdUtil.generateId(toTenantId, tenantMenuEntity.getId(), TempleIdUtil.SEPARATOR));
            tenantMenuEntity.setTenantId(toTenantId);
            TenantMenuEntity menuEntity = tenantMenuDao.save(tenantMenuEntity);
            tenantMenuEntityList.add(menuEntity);
        }
        //复制是否隐藏租户级菜单
        TenantConfigEntity tenantConfigEntity = tenantConfigDao.getTenantValueByKey(fromTenantId, TenantConfigKey.HIDDEN_SYSTEM_MENU);
        if (tenantConfigEntity != null) {
            tenantConfigDao.setTenantValue(toTenantId, TenantConfigKey.HIDDEN_SYSTEM_MENU, tenantConfigEntity.getValue());
        }
        return tenantMenuEntityList;
    }

    @Override
    public int destroyTenantMenu(int tenantId) {
        List<TenantMenuEntity> tenantMenuList = tenantMenuDao.getTenantMenuList(tenantId, WebPageConstants.APP_CRM, false);
        List<String> deleteTenantMenuIds = Lists.newArrayList();
        userMenuDao.deleteAllUserMenu(tenantId);
        List<TenantMenuEntity> recoverTenantMenus = Lists.newArrayList();
        tenantMenuList.stream().forEach(tenantMenuEntity -> {
            if (SourceType.SYSTEM.equals(tenantMenuEntity.getSourceType())) {
                recoverTenantMenus.add(tenantMenuEntity);
            } else {
                deleteTenantMenuIds.add(tenantMenuEntity.getId());
            }
        });
        //删除的租户级菜单
        int deleteMenus = deleteMenus(deleteTenantMenuIds);
        logger.info("destroyTenantMenu deleteMenus:{}", deleteMenus);
        //恢复是否隐藏系统菜单
        tenantConfigDao.setTenantValue(tenantId, TenantConfigKey.HIDDEN_SYSTEM_MENU, Boolean.FALSE.toString());
        //需要初始化的菜单
        recoverTenantMenus.stream().forEach(tenantMenuEntity -> {
            tenantMenuEntity.setMenuDataEntities(Lists.newArrayList());
            tenantMenuEntity.setChange(false);
            tenantMenuDao.findAndModify(tenantId, tenantMenuEntity);
            logger.info("destroyTenantMenu recoverMenu:{}", tenantMenuEntity);
        });

        return recoverTenantMenus.size();
    }

    @Override
    public void deleteTenantMenusByAppId(int tenantId, int appType, String appId) {
        tenantMenuDao.deleteTenantMenusByAppId(tenantId, appType, appId);
    }

    @Override
    public List<JSONObject> getTenantUiConfig(String client) {
        JSONArray tenantUiConfigList = TenantUiConfig.getTenantUiConfig();
        List<JSONObject> resultList = Lists.newArrayList();
        for (int i = 0; i < tenantUiConfigList.size(); i++) {
            JSONObject tenantUiConfig = tenantUiConfigList.getJSONObject(i);
            TenantUiInfoExt tenantUiInfoExt = TenantUiInfoExt.of(tenantUiConfig);
            if (!filterTenantConfig(tenantUiConfig, client)) {
                continue;
            }
            String nameI18nKey = tenantUiInfoExt.getNameI18nKey();
            if (StringUtils.isNotBlank(nameI18nKey)) {
                String translatedText = I18NExt.getOrDefault(nameI18nKey, tenantUiInfoExt.getTitle());
                tenantUiInfoExt.setTitle(translatedText);
            }
            resultList.add(tenantUiConfig);
        }
        return resultList;
    }

    private boolean filterTenantConfig(JSONObject tenantUiConfig, String client) {
        if (StringUtils.isEmpty(client)) {
            return true;
        }
        TenantUiInfoExt tenantUiInfoExt = TenantUiInfoExt.of(tenantUiConfig);
        List<String> supportClient = tenantUiInfoExt.getSupportClient();
        if (CollectionUtils.isEmpty(supportClient)) {
            return true;
        }
        return supportClient.contains(client);

    }

    private int deleteMenus(List<String> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return 0;
        }
        menuIds.stream().forEach(menuId -> {
            TenantMenuEntity menuEntity = tenantMenuDao.updateStatus(menuId, MenuStatus.deleteStatus);
            logger.info("deleteMenus menuEntity:{}", menuEntity);
        });
        return menuIds.size();
    }

}
