package com.facishare.webpage.customer.controller.model.arg.paas;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/9/29 3:27 下午
 */
@Data
public class GetManMainChannelByApiNameArg extends BaseArg {

    private String apiName;

    @Override
    public void valid() {
        if (StringUtils.isEmpty(apiName)){
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
