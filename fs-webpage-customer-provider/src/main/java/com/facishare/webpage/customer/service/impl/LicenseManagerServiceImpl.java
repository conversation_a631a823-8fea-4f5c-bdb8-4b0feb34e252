package com.facishare.webpage.customer.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.ProductTypeEnum;
import com.facishare.enterprise.common.util.FsGrayReleaseUtil;
import com.facishare.paas.license.Result.CheckValidityResult;
import com.facishare.paas.license.arg.CheckValidityArg;
import com.facishare.paas.license.arg.QueryAppExpiredTimeArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.AppExpiredTimePojo;
import com.facishare.paas.license.pojo.CheckValidityPojo;
import com.facishare.paas.license.pojo.ProductValidityInfoPojo;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.service.LicenseManagerService;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.facishare.webpage.customer.config.DefaultTenantConfig.isGrayEnterprise;

@Service
@Slf4j
public class LicenseManagerServiceImpl implements LicenseManagerService {
    private static final Integer GRAY_ENTERPRISE_APP_EXPIREDTIME = 365;

    @Autowired
    private LicenseClient licenseClient;

    @Autowired
    private EIEAConverter eieaConverter;

    //返回应用剩余可用时长，单位（天）
    @Override
    public Integer getLinkAppAvailableTime(int tenantId, String linkAppId) {
        try {
            if (DefaultTenantConfig.isIgnoreLicenseByAppId(linkAppId)) {
                return GRAY_ENTERPRISE_APP_EXPIREDTIME;
            }
            String ea = eieaConverter.enterpriseIdToAccount(tenantId);
            String productVersion = DefaultTenantConfig.getProductVersionByAppId(linkAppId);

            //productVersion为空说明应用免费
            if (productVersion == null) {
                return null;
            }

            if (isGrayEnterprise(tenantId)) {
                return GRAY_ENTERPRISE_APP_EXPIREDTIME;
            }
            // 使用licenseClient.queryAppExpiredTime接口查询互联应用有效期
            if (FsGrayReleaseUtil.isAllowUseAppExpiredTimeEas(ea)) {
                return queryAppExpiredTime(tenantId + "", productVersion);
            }

            LicenseContext contextData = new LicenseContext();
            contextData.setAppId("CRM");
            contextData.setTenantId(tenantId + "");
            contextData.setUserId(CrmConstants.SYSTEM_USER + "");

            CheckValidityArg checkValidityArg = new CheckValidityArg();
            checkValidityArg.setContext(contextData);
            checkValidityArg.setProductVersions(Sets.newHashSet(productVersion));
            checkValidityArg.setProductType(String.valueOf(ProductTypeEnum.APP.getType()));
            checkValidityArg.setNeedMerge(true);
            CheckValidityResult result = licenseClient.checkValidity(checkValidityArg);
            log.info("licenseClient.checkValidity arg={},result={}", checkValidityArg, result);
            if (result != null && result.getErrCode() == 0) {
                CheckValidityPojo batchCheckLicenseValidityResult = result.getResult();
                if (batchCheckLicenseValidityResult != null) {
                    Set<ProductValidityInfoPojo> validityInfoDataList = batchCheckLicenseValidityResult.getValidityInfoPojos();
                    if (validityInfoDataList == null || validityInfoDataList.isEmpty()) {
                        return 0;
                    }
                    ProductValidityInfoPojo validityInfoData = Lists.newArrayList(validityInfoDataList).get(0);
                    long availableTime = validityInfoData.getExpiredTime() - System.currentTimeMillis();
                    if (availableTime < 0) {
                        availableTime = 0;
                    }
                    return (int) Math.ceil(availableTime / 1000.0 / 3600 / 24);
                }
            }
            return 0;
        } catch (Exception e) {
            log.warn("getLinkAppAvailableTime error!", e);
            return 365;
        }
    }

    /**
     * 查询互联应用剩余可用时长
     *
     * @param tenantId       上游企业ID
     * @param productVersion 互联应用关联产品包名称
     * @return 剩余可用时长
     */
    @Override
    public Integer queryAppExpiredTime(String tenantId, String productVersion) {
        try {
            LicenseContext context = new LicenseContext();
            context.setAppId("CRM");
            context.setTenantId(tenantId + "");
            context.setUserId(CrmConstants.SYSTEM_USER + "");
            QueryAppExpiredTimeArg queryArg = new QueryAppExpiredTimeArg();
            queryArg.setContext(context);
            queryArg.setModuleCodes(Lists.newArrayList(productVersion));
            Result<Set<AppExpiredTimePojo>> result = licenseClient.queryAppExpiredTime(queryArg);
            log.info("licenseClient.queryAppExpiredTime arg={},result={}", queryArg, result);
            if (result != null && result.getErrCode() == 0 && result.getResult() != null) {
                Set<AppExpiredTimePojo> appExpiredTimePojo = result.getResult();
                AppExpiredTimePojo appExpiredTime = Lists.newArrayList(appExpiredTimePojo).get(0);
                if (appExpiredTime.getExpiredTime() != null) {
                    long availableTime = appExpiredTime.getExpiredTime() - System.currentTimeMillis();
                    if (availableTime < 0) {
                        availableTime = 0;
                    }
                    return (int) Math.ceil(availableTime / 1000.0 / 3600 / 24);
                }
            }
            return 0;
        } catch (Exception e) {
            log.warn("queryAppExpiredTime-->error:", e);
            return 365;
        }
    }


    //返回应用剩余可用时长，单位（天）
    public Map<String, Integer> batchGetLinkAppAvailableTime(String ea, List<String> linkAppIds) {
        Map<String, Integer> appIdToAvailableTimeMap = new HashMap<>();
        try {
            int tenantId = eieaConverter.enterpriseAccountToId(ea);
            if (isGrayEnterprise(tenantId)) {
                // 灰度企业应用过期时间默一年
                for (String linkAppId : linkAppIds) {
                    String productVersion = DefaultTenantConfig.getProductVersionByAppId(linkAppId);
                    if (productVersion != null) {
                        // 有productVersion,应用收费，设置过期时间一年
                        appIdToAvailableTimeMap.put(linkAppId, GRAY_ENTERPRISE_APP_EXPIREDTIME);
                    }
                }
                return appIdToAvailableTimeMap;
            }

            Set<String> productVersions = new HashSet<>();
            for (String linkAppId : linkAppIds) {
                if (!DefaultTenantConfig.isIgnoreLicenseByAppId(linkAppId)) {
                    String productVersion = DefaultTenantConfig.getProductVersionByAppId(linkAppId);
                    if (productVersion != null) {
                        productVersions.add(productVersion);
                    }
                }
            }

            LicenseContext contextData = new LicenseContext();
            contextData.setAppId("CRM");
            contextData.setTenantId(tenantId + "");
            contextData.setUserId(CrmConstants.SYSTEM_USER + "");
            CheckValidityArg checkValidityArg = new CheckValidityArg();
            checkValidityArg.setContext(contextData);
            checkValidityArg.setProductVersions(productVersions);
            checkValidityArg.setProductType(String.valueOf(ProductTypeEnum.APP.getType()));
            checkValidityArg.setNeedMerge(true);
            CheckValidityResult result = licenseClient.checkValidity(checkValidityArg);
            if (result != null && result.getErrCode() == 0) {
                CheckValidityPojo batchCheckLicenseValidityResult = result.getResult();
                if (batchCheckLicenseValidityResult != null) {
                    Set<ProductValidityInfoPojo> validityInfoDataList = batchCheckLicenseValidityResult.getValidityInfoPojos();
                    if (validityInfoDataList == null) {
                        validityInfoDataList = new HashSet<>();
                    }

                    Map<String, Integer> productVersionToAvailableTimeMap = new HashMap<>();
                    for (ProductValidityInfoPojo validityInfoData : validityInfoDataList) {
                        long availableTime = validityInfoData.getExpiredTime() - System.currentTimeMillis();
                        if (availableTime < 0) {
                            availableTime = 0;
                        }
                        int intAvailableTime = (int) Math.ceil(availableTime / 1000.0 / 3600 / 24);
                        productVersionToAvailableTimeMap.put(validityInfoData.getProductVersion(), intAvailableTime);
                    }
                    for (String linkAppId : linkAppIds) {
                        if (DefaultTenantConfig.isIgnoreLicenseByAppId(linkAppId)) {
                            appIdToAvailableTimeMap.put(linkAppId, GRAY_ENTERPRISE_APP_EXPIREDTIME);
                            continue;
                        }
                        String productVersion = DefaultTenantConfig.getProductVersionByAppId(linkAppId);
                        if (productVersion != null) {
                            if (productVersionToAvailableTimeMap.containsKey(productVersion)) {
                                int intAvailableTime = productVersionToAvailableTimeMap.get(productVersion);
                                appIdToAvailableTimeMap.put(linkAppId, intAvailableTime);
                            } else {
                                appIdToAvailableTimeMap.put(linkAppId, 0);
                            }
                        }
                    }
                }
            } else {
                for (String linkAppId : linkAppIds) {
                    appIdToAvailableTimeMap.put(linkAppId, 365);
                }
            }
            return appIdToAvailableTimeMap;
        } catch (Exception e) {
            log.warn("", e);
            for (String linkAppId : linkAppIds) {
                appIdToAvailableTimeMap.put(linkAppId, 365);
            }
            return appIdToAvailableTimeMap;
        }
    }

}
