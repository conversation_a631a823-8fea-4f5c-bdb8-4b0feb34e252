package com.facishare.webpage.customer.config.model;

import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IconData implements Serializable {
    /**
     * icon的唯一标识
     */
    private String iconIndex;
    /**
     * iconfont
     */
    private String iconName;
    /**
     * icon
     */
    private String icon;
    /**
     * grayIcon
     */
    private String grayIcon;
    /**
     * 移动端Icon
     */
    private String appIcon;
    /**
     * 面性图标
     */
    private String facialIcon;
    /**
     * iconAddress
     */
    private  String iconAddress = this.icon;
    /**
     * icon适用范围
     */
    private List<String> scope = Lists.newArrayList("MainChannel");
}
