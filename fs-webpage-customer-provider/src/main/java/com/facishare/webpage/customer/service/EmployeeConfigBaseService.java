package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.EmployeeConfig;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/10/29
 */
public interface EmployeeConfigBaseService {

    List<EmployeeConfig> getEmployeeConfigValueByKeys(String layoutId, int tenantId, int employeeId, List<Integer> keys);

    boolean setEmployeeConfigValue(String layoutId, int tenantId, int employeeId, EmployeeConfig employeeConfig);

    boolean setEmployeeConfigByApiName(String apiName, int tenantId, int employeeId, EmployeeConfig employeeConfig);

    /**
     * 获取用户菜单配置
     * @param layoutId
     * @param tenantId
     * @param employeeId
     * @param key
     * @return
     */
    EmployeeConfig getEmployeeConfigStatusByKey (String layoutId, int tenantId, int employeeId, int key);

    List<EmployeeConfig> getEmployeeConfigValueByApiNameHasNotDefault(String apiName, int enterpriseId, int employeeId);
}
