package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class DefaultTenantConfig {

    private static Logger logger = LoggerFactory.getLogger(DefaultTenantConfig.class);


    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();


    private List<String> defaultTenantIds = Lists.newArrayList();

    private static String defaultTenantConfig = "fs-user-extension-biz-properties";

    private static String presetEnterpriseIdSet = "presetEnterpriseIdSet";

    private static String defaultTenantBrandColor = "defaultTenantBrandColor";
    private static String imgFullPathRuleKey = "imgFullPathRule";
    private static String imgFullPathRule = "";

    private static String tenantBrandColor = null;

    private static String linkAppAddObjectBlackList = "linkapp.objectAssociation.blacklist";
    public static Map<String, Set<String>> objectAssociationBlacklistMaps = Maps.newHashMap();
    private static String crmSystemObjects = "crm_system_objects";
    private static String synLinkAppDataIP = "";
    private static String synLinkAppDataIPKey = "synLinkAppDataIPKey";
    private static String webpageApibusUrl = "";
    //渠道门户appId
    private static String qudaomenhuAppIdKey = "qudaomenhuAppId";
    private static String qudaomenhuAppId = "";
    private static String CDN_ALL_URL_TEMPLATE = "https://a9.fspage.com/image/%s/%s";

    //同步互联应用数据的企业ea
    private static String synLinkAppGrayEaByShuxianKey = "synLinkAppGrayEaByShuxian";
    private static String synLinkAppGrayEaByShuxian = "";

    //互联应用模板企业id
    private static String linkAppModelTenantIdKey = "linkAppModelTenantId";
    private static String linkAppModelTenantId = "";
    //qixin AppId
    public static String qixinAppID = "FSAID_989a96";

    private static Set<String> IGNORE_LICENSE_APP_IDS = Sets.newHashSet();
    private static Set<String> ENTERPRISE_EDITION_TENANT_IDS = Sets.newHashSet();
    private static Map<String, String> APP_ID_AND_LICENSE_MAP = Maps.newHashMap();

    private static Map<String, Object> CUSTOM_COMPONENT_SCOPE_CONFIG = Maps.newHashMap();

    private static Set<String> TRANS_WORK_BENCH_SUPPORT_PRE_CUSTOM_PAGE_APP_IDS = Sets.newHashSet();


    /**
     * 系统所有老对象列表
     */
    public static List<String> systemObjs = Lists.newArrayList();

    private static String linkAppAssociateObjectApiValidateConfig = "linkAppAssociateObjectApiValidateConfig";
    public static Map<String, LinkAppAssociateObjectApiValidateConfig> linkAppAssociateObjectApiValidateConfigMap = new HashMap<>();

    public static List<Integer> grayTenantIds = Lists.newArrayList();
    private static String objectIcon1Url;
    private static String objectIcon2Url;
    private static String objectFxIconUrl;

    public static List<String> webTransComponents = new ArrayList<>();
    public static List<String> oldCrmTransComponents = new ArrayList<>();
    public static List<String> oldCrmComponentsNoTrans = new ArrayList<>();
    public static Map<String, Integer> FIND_META_MENU_MORE_TIME_BY_EI = Maps.newHashMap();
    public static Map<String, Integer> WEB_MAIN_CHANNEL_MORE_TIME_BY_EI = Maps.newHashMap();

    private static String openSitePermission = "";

    public static Set<String> getTransWorkBenchSupportPreCustomPageAppIds() {
        return TRANS_WORK_BENCH_SUPPORT_PRE_CUSTOM_PAGE_APP_IDS;
    }

    @PostConstruct
    public void init() {
        IChangeListener listener = config -> loadConfig();
        ConfigFactory.getConfig(defaultTenantConfig, listener, true);
    }

    private void loadConfig() {

        IConfig config = ConfigFactory.getConfig(defaultTenantConfig);
        imgFullPathRule = config.get(imgFullPathRuleKey);
        synLinkAppDataIP = config.get(synLinkAppDataIPKey);
        webpageApibusUrl = config.get("webpageApibusUrl", "http:http://fs-apibus-global:8887/fs-webpage-customer");
        qudaomenhuAppId = config.get(qudaomenhuAppIdKey, "FSAID_11491084");
        synLinkAppGrayEaByShuxian = config.get(synLinkAppGrayEaByShuxianKey, "");
        linkAppModelTenantId = config.get(linkAppModelTenantIdKey, "754728");
        qixinAppID = config.get("qixinAppID", "FSAID_989a96");

        objectIcon1Url = config.get("objectIcon1Url", "https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/facial/fx-icon-obj-app%s.svg");
        objectIcon2Url = config.get("objectIcon2Url", "https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/facial/fx-icon-obj-app%s.svg");
        objectFxIconUrl = config.get("objectFxIconUrl", "fx-icon-obj-app%s");

        String defaultTenantId = config.get(presetEnterpriseIdSet);
        List<String> list = (List<String>) JSONArray.parse(defaultTenantId);
        tenantBrandColor = config.get(defaultTenantBrandColor, null);
        defaultTenantIds = list;

        //加载linkapp对象关联黑名单
        objectAssociationBlacklistMaps = JsonUtil.fromJson(config.get(linkAppAddObjectBlackList), new TypeToken<Map<String, Set<String>>>() {
        }.getType());

        //加载crm系统老对象列表
        systemObjs = Splitter.on(",").splitToList(config.get(crmSystemObjects));
        log.info("objectAssociationBlacklistMaps = {}", objectAssociationBlacklistMaps);

        String body = config.get(linkAppAssociateObjectApiValidateConfig, "{}");
        linkAppAssociateObjectApiValidateConfigMap = JsonUtil.fromJson(body, new TypeToken<Map<String, LinkAppAssociateObjectApiValidateConfig>>() {
        }.getType());
        CDN_ALL_URL_TEMPLATE = config.get("CDN_ALL_URL_TEMPLATE", CDN_ALL_URL_TEMPLATE);

        IGNORE_LICENSE_APP_IDS = getSetFromConfig(config, "ignore_license_app_ids");

        TRANS_WORK_BENCH_SUPPORT_PRE_CUSTOM_PAGE_APP_IDS = getSetFromConfig(config, "trans_work_bench_support_pre_custom_page_app_ids");

        APP_ID_AND_LICENSE_MAP = buildAppIdAndLicenseMap(parseMapFromConfig(config, "er_license_app_ids"));

        ENTERPRISE_EDITION_TENANT_IDS = getSetFromConfig(config, "enterprise_edition_tenant_ids");

        FIND_META_MENU_MORE_TIME_BY_EI = parseMapFromConfig(config, "find_meta_menu_more_time");

        WEB_MAIN_CHANNEL_MORE_TIME_BY_EI = parseMapFromConfig(config, "web_main_channel_more_time_by_ei");

        CUSTOM_COMPONENT_SCOPE_CONFIG = parseMapFromConfig(config, "custom_component_scope_config");

        log.info("linkAppAssociateObjectApiValidateConfigMap = {}", linkAppAssociateObjectApiValidateConfigMap);
        String grayTenantIdString = config.get("grayTenantId", "78810");
        List<String> tenantIdList = Splitter.on(",").splitToList(grayTenantIdString);
        grayTenantIds = tenantIdList.stream()
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        webTransComponents = Stream.of(config.get("WebTransComponents", "").split(",")).collect(Collectors.toList());
        oldCrmTransComponents = Stream.of(config.get("OldCrmTransComponents", "").split(",")).collect(Collectors.toList());
        oldCrmComponentsNoTrans = Stream.of(config.get("OldCrmComponentsNoTrans", "").split(",")).collect(Collectors.toList());
        openSitePermission = config.get("openSitePermission");

        logger.info("init DefualtTenantConfig config defaultTenantIds:{};", defaultTenantIds);
    }

    public static boolean closeSitePermission() {
        return !"open".equals(openSitePermission);
    }

    private Integer getIntFromConfig(IConfig config, String key, int defaultValue) {
        return config.getInt(key, defaultValue);
    }

    private Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
        try {
            Map map = JSON.parseObject(data, Map.class);
            return Collections.unmodifiableMap(map);
        } catch (Exception e) {
            log.error("parseMapFromConfig failed,config:{},key:{},data:{}", config.getName(), key, data, e);
            return Collections.unmodifiableMap(Maps.newHashMap());
        }
    }

    public List<String> getDefaultTenantConfig() {
        return defaultTenantIds;
    }

    public String getImgFullPathRule() {
        return imgFullPathRule;
    }

    public String getDefaultTenantBrandColor() {
        return tenantBrandColor;
    }

    public String getSynLinkAppDataIP() {
        return synLinkAppDataIP;
    }

    public String getWebpageApibusUrl() {
        return webpageApibusUrl;
    }

    public static String getQudaomenhuAppId() {
        return qudaomenhuAppId;
    }

    public static boolean isIgnoreLicenseByAppId(String appId) {
        return IGNORE_LICENSE_APP_IDS.contains(appId);
    }

    public static boolean isGrayEnterprise(int tenantId) {
        return ENTERPRISE_EDITION_TENANT_IDS.contains(tenantId);
    }

    public static int getFindMetaMenuMoreTimeByEi(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return 10;
        }
        Integer moreTime = FIND_META_MENU_MORE_TIME_BY_EI.get(tenantId);
        return Objects.isNull(moreTime) ? 10 : moreTime;
    }

    public static int getGetWebMainChannelMoreTimeByEi(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return 12;
        }

        Integer moreTime = WEB_MAIN_CHANNEL_MORE_TIME_BY_EI.get(tenantId);
        if (Objects.nonNull(moreTime)) {
            return moreTime;
        }

        Integer allTenantMoreTime = WEB_MAIN_CHANNEL_MORE_TIME_BY_EI.get("*");
        if (Objects.nonNull(allTenantMoreTime)) {
            return allTenantMoreTime;
        }
        return 12;
    }

    private static Map<String, String> buildAppIdAndLicenseMap(Map<String, String> licenseAndAppIdMap) {
        Map<String, String> licenseMap = new HashMap<>();
        for (String erLicense : licenseAndAppIdMap.keySet()) {
            String appIds = licenseAndAppIdMap.get(erLicense);
            List<String> appIdList = Lists.newArrayList(appIds.split(","));
            for (String appId : appIdList) {
                licenseMap.put(appId, erLicense);
            }
        }
        return licenseMap;
    }

    public static String getProductVersionByAppId(String appId) {
        return APP_ID_AND_LICENSE_MAP.get(appId);
    }

    public String getLinkAppModelTenantId() {
        return linkAppModelTenantId;
    }

    public static String getObjectIcon1Url(Integer iconSlot) {
        return String.format(objectIcon1Url, iconSlot);
    }

    public static String getObjectIcon2Url(Integer iconSlot) {
        return String.format(objectIcon2Url, iconSlot);
    }

    public static String getObjectFxIconUrl(Integer iconSlot) {
        return String.format(objectFxIconUrl, iconSlot);
    }

    public String getSynLinkAppGrayEaByShuxian() {
        return synLinkAppGrayEaByShuxian;
    }

    public static String getCdnAllUrlTemplate() {
        return CDN_ALL_URL_TEMPLATE;
    }

    public static Map<String, String> getCustomComponentScopeByAppId(String appId) {
        Object scopeMap = CUSTOM_COMPONENT_SCOPE_CONFIG.get(appId);
        if (Objects.isNull(scopeMap) || !(scopeMap instanceof Map)) {
            return Maps.newHashMap();
        }
        return (Map<String, String>) scopeMap;
    }


    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        DefaultTenantConfig defaultTenantConfig = new DefaultTenantConfig();
        defaultTenantConfig.init();
        String tenantConfig = defaultTenantConfig.getSynLinkAppDataIP();
        System.out.println(tenantConfig);
    }


}
