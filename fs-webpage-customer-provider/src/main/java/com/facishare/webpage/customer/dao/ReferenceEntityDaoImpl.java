package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.dao.entity.ReferenceEntity;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 引用关系DAO实现类
 */
@Setter
@Slf4j
public class ReferenceEntityDaoImpl implements ReferenceEntityDao {

    @Resource
    private Datastore datastore;

    @PostConstruct
    public void init() {
        datastore.ensureIndexes(ReferenceEntity.class, true);
    }

    @Override
    public List<ReferenceEntity> findBySiteApiName(Integer tenantId, String siteApiName) {
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("indexName").equal("siteApiName");
        query.field("indexValue").equal(siteApiName);
        return query.asList();
    }

    @Override
    public List<ReferenceEntity> findByTargetId(Integer tenantId, String targetType, String targetId) {
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("targetType").equal(targetType);
        query.field("targetId").equal(targetId);
        return query.asList();
    }

    @Override
    public ReferenceEntity findBySourceAndTarget(Integer tenantId, String sourceType, String sourceId,
                                                 String targetType, String targetId) {
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("sourceType").equal(sourceType);
        query.field("sourceId").equal(sourceId);
        query.field("targetType").equal(targetType);
        query.field("targetId").equal(targetId);
        return query.get();
    }

    public List<ReferenceEntity> findByTarget(Integer tenantId, String targetType, String targetId) {
        return findByTargetId(tenantId, targetType, targetId);
    }

    @Override
    public List<ReferenceEntity> findBySource(Integer tenantId, String sourceType, String sourceId) {
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("sourceType").equal(sourceType);
        query.field("sourceId").equal(sourceId);
        return query.asList();
    }

    @Override
    public void batchSave(List<ReferenceEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        for (ReferenceEntity reference : entities) {
            save(reference);
        }
    }

    @Override
    public List<ReferenceEntity> findReferenceByCondition(ReferenceEntity referenceEntity) {
        if (Objects.isNull(referenceEntity)) {
            return Lists.newArrayList();
        }
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        if (Objects.nonNull(referenceEntity.getTenantId())) {
            query.field("tenantId").equal(referenceEntity.getTenantId());
        }
        if (Objects.nonNull(referenceEntity.getSourceType())) {
            query.field("sourceType").equal(referenceEntity.getSourceType());
        }
        if (Objects.nonNull(referenceEntity.getSourceId())) {
            query.field("sourceId").equal(referenceEntity.getSourceId());
        }
        if (Objects.nonNull(referenceEntity.getTargetType())) {
            query.field("targetType").equal(referenceEntity.getTargetType());
        }
        if (Objects.nonNull(referenceEntity.getTargetId())) {
            query.field("targetId").equal(referenceEntity.getTargetId());
        }
        if (Objects.nonNull(referenceEntity.getIndexValue())) {
            query.field("indexValue").equal(referenceEntity.getIndexValue());
        }
        if (Objects.nonNull(referenceEntity.getIndexName())) {
            query.field("indexName").equal(referenceEntity.getIndexName());
        }
        return query.asList();
    }

    @Override
    public void save(ReferenceEntity entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setId(ObjectId.get().toHexString());
        }
        if (Objects.isNull(entity.getCreateTime())) {
            entity.setCreateTime(System.currentTimeMillis());
        }
        datastore.save(entity);
    }

    @Override
    public void delete(String id) {
        if (id == null) {
            return;
        }

        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("_id").equal(id);
        datastore.delete(query);
    }

    @Override
    public void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("_id").in(ids);
        datastore.delete(query);
    }

    @Override
    public List<String> findWorkSpaceApiNamesByResourceId(Integer tenantId, String resourceType, String resourceId) {
        // 查询资源所在的工作区
        // 1. 查找以resource为target的引用关系
        List<ReferenceEntity> references = findByTargetId(tenantId, resourceType, resourceId);
        if (CollectionUtils.isEmpty(references)) {
            return Collections.emptyList();
        }

        // 2. 过滤出sourceType为workspace的引用关系
        return references.stream()
                .filter(ref -> "workspace".equals(ref.getSourceType()))
                .map(ReferenceEntity::getSourceId)
                .distinct()
                .collect(Collectors.toList());
    }


    @Override
    public boolean hasRelatedResources(String tenantId, ReferenceTargetType
                                               targetType, Set<String> targetIdList,
                                       String sourceType, Set<String> sourceIdList) {
        if (StringUtils.isEmpty(tenantId)) {
            log.warn("tenantId is null! targetType:{},targetIdList:{},sourceType:{},sourceIdList:{}",
                    targetType, targetIdList, sourceType, sourceIdList);
            return false;
        }
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(Integer.valueOf(tenantId));

        if (Objects.nonNull(targetType)) {
            query.field("targetType").equal(targetType.getCode());
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            query.field("targetId").in(targetIdList);
        }
        if (StringUtils.isNotBlank(sourceType)) {
            query.field("sourceType").equal(sourceType);
        }
        if (CollectionUtils.isNotEmpty(sourceIdList)) {
            query.field("sourceId").in(sourceIdList);
        }
        // 只需查询数量，不需要返回具体数据
        return !query.asList().isEmpty();
    }

    @Override
    public List<String> filterHasRelatedTargetIds(String tenantId, ReferenceTargetType
            targetType, Set<String> targetIdList, String sourceType, Set<String> sourceIdList) {
        if (StringUtils.isBlank(tenantId)) {
            log.warn("tenantId is null! targetType:{},targetIdList:{},sourceType:{},sourceIdList:{}",
                    targetType, targetIdList, sourceType, sourceIdList);
            return Lists.newArrayList();
        }

        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(Integer.valueOf(tenantId));

        if (Objects.nonNull(targetType)) {
            query.field("targetType").equal(targetType.getCode());
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            query.field("targetId").in(targetIdList);
        }
        if (StringUtils.isNotBlank(sourceType)) {
            query.field("sourceType").equal(sourceType);
        }
        if (CollectionUtils.isNotEmpty(sourceIdList)) {
            query.field("sourceId").in(sourceIdList);
        }

        return query.asList().stream().map(ReferenceEntity::getTargetId).collect(Collectors.toList());
    }


    @Override
    public void batchUpdate(User user, List<ReferenceEntity> referenceEntities) {
        if (CollectionUtils.isEmpty(referenceEntities)) {
            return;
        }
        for (ReferenceEntity referenceEntity : referenceEntities) {
            Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
            query.field("_id").equal(referenceEntity.getId());
            query.field("tenantId").equal(user.getTenantId());

            UpdateOperations<ReferenceEntity> updateOperations = datastore.createUpdateOperations(ReferenceEntity.class);

            if (Objects.nonNull(referenceEntity.getIndexValue())) {
                updateOperations.set("indexValue", referenceEntity.getIndexValue());
            }
            if (Objects.nonNull(referenceEntity.getIndexName())) {
                updateOperations.set("indexName", referenceEntity.getIndexName());
            }
            if (Objects.nonNull(referenceEntity.getTargetType())) {
                updateOperations.set("targetType", referenceEntity.getTargetType());
            }
            if (Objects.nonNull(referenceEntity.getTargetId())) {
                updateOperations.set("targetId", referenceEntity.getTargetId());
            }
            if (Objects.nonNull(referenceEntity.getSourceType())) {
                updateOperations.set("sourceType", referenceEntity.getSourceType());
            }
            if (Objects.nonNull(referenceEntity.getSourceId())) {
                updateOperations.set("sourceId", referenceEntity.getSourceId());
            }
            datastore.update(query, updateOperations);
        }

    }


    @Override
    public void batchDeleteByTarget(Integer tenantId, String targetType, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return;
        }

        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("targetType").equal(targetType);
        query.field("targetId").in(targetIds);
        datastore.delete(query);
    }

    @Override
    public void deleteByTarget(Integer tenantId, String targetType, String targetId) {
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("targetType").equal(targetType);
        query.field("targetId").equal(targetId);
        datastore.delete(query);
    }

    @Override
    public void deleteBySource(Integer tenantId, String sourceType, String sourceId) {
        Query<ReferenceEntity> query = datastore.createQuery(ReferenceEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("sourceType").equal(sourceType);
        query.field("sourceId").equal(sourceId);
        datastore.delete(query);
    }
}
