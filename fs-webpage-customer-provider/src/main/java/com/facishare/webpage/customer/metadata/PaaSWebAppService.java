package com.facishare.webpage.customer.metadata;

import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.qixin.permission.unified.common.CheckHasPermissionService;
import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.LanguageService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.config.AppTypeConfig;
import com.facishare.webpage.customer.constant.ComponentAppType;
import com.facishare.webpage.customer.metadata.model.WebMainChannelMetaData;
import com.facishare.webpage.customer.service.PaaSAppService;
import com.fxiaoke.appcenter.restapi.arg.CanAccessComponentArg;
import com.fxiaoke.appcenter.restapi.common.BaseResult;
import com.fxiaoke.appcenter.restapi.common.HeaderObj;
import com.fxiaoke.appcenter.restapi.service.OpenFsUserAppViewService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/11/16
 */
@Service("paaSWebAppService")
@Slf4j
public class PaaSWebAppService implements WebAppService {

    private static final String paaSAppUrl = "#paasapp/index/=/appId_";

    @Resource
    private PaaSAppService paaSAppService;
    @Resource
    private CheckHasPermissionService checkHasPermissionService;
    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Resource
    private AppMenuConfig appMenuConfig;
    @Resource
    private CheckService checkService;
    @Resource
    private AppTypeConfig appTypeConfig;
    @Resource
    private LanguageService languageService;

    public PaaSWebAppService() {
    }

    @Override
    public List<WebMainChannelMetaData> getTenantMainChannelMetaDataList(int tenantId, String enterpriseAccount, int employeeId, Locale locale) {
        boolean openCRM = checkService.checkOpenCRM(tenantId, enterpriseAccount, employeeId);
        if (!openCRM) {
            return Lists.newArrayList();
        }

        List<PaaSAppVO> paaSAppList = paaSAppService.getPaaSAppList(tenantId);
        if (CollectionUtils.isEmpty(paaSAppList)) {
            return Lists.newArrayList();
        }
        boolean checkGoNewCRM = checkService.checkGoNewCRM(tenantId);
        return paaSAppList.stream().
                filter(x -> (checkGoNewCRM || !"CRM".equals(x.getAppId()))).
                map(x -> {
                    WebMainChannelMetaData webMainChannelMetaData = WebMainChannelMetaData.builder().
                            appId(x.getAppId()).
                            name(x.getName()).
                            description(x.getDescription()).
                            icon(x.getIcon()).build();
                    return webMainChannelMetaData;
                }).collect(Collectors.toList());
    }

    @Override
    public List<WebMainChannelMetaData> getUserMainChannelMetaDataList(int tenantId,
                                                                       String enterpriseAccount,
                                                                       int employeeId,
                                                                       Locale locale,
                                                                       SlowLog stopWatch) {
        boolean hasPermission = hasPermission(tenantId, enterpriseAccount, employeeId);
        stopWatch.lap("over hasPermission");
        if (!hasPermission) {
            return Lists.newArrayList();
        }

        List<PaaSAppVO> userPaaSAppList = paaSAppService.getUserPaaSAppList(tenantId, employeeId, locale);

        stopWatch.lap("over getUserPaaSAppList");
        if (CollectionUtils.isEmpty(userPaaSAppList)) {
            return Lists.newArrayList();
        }
        List<String> languageKeys = Lists.newArrayList();
        Map<String, String> appTypeTransKeyMap = appTypeConfig.getAppTypeTransKeyMap();
        appTypeTransKeyMap.forEach((k, v) -> {
            languageKeys.add(v);
        });
        Map<String, String> menuLanguages = languageService.queryLanguageByNameI18nKeys(tenantId, languageKeys, locale);

        boolean checkGoNewCRM = checkService.checkGoNewCRM(tenantId);
        return userPaaSAppList.stream().filter(x -> (checkGoNewCRM || !"CRM".equals(x.getAppId()))).map(x -> {
            WebMainChannelMetaData webMainChannelMetaData = WebMainChannelMetaData.builder().
                    appId(x.getAppId()).
                    appType(ComponentAppType.paaSAppType).
                    name(x.getName()).
                    description(x.getDescription()).
                    iconIndex(x.getIconIndex()).
                    icon(x.getIcon()).
                    iconType(x.getUploadIconType()).
                    appTypeName("//" + menuLanguages.get(appTypeTransKeyMap.get("innerApp")) + "//" + menuLanguages.get(appTypeTransKeyMap.get("other"))).
                    url(paaSAppUrl + x.getAppId()).build();
            return webMainChannelMetaData;
        }).collect(Collectors.toList());
    }

    private boolean hasPermission(int tenantId, String enterpriseAccount, int employeeId) {
        //这里很重要  现在必调应用中心，强依赖接口返回，需要加缓存  或者返回默认值
        String componentId = appMenuConfig.getCrmPermissionAppId();
        try {
            // 创建HeaderObj
            HeaderObj header = HeaderObj.newInstance(Integer.toString(tenantId));
            // 创建CanAccessComponentArg
            CanAccessComponentArg canAccessComponentArg = new CanAccessComponentArg();
            canAccessComponentArg.setFsUserVO(enterpriseAccount, employeeId, "");
            canAccessComponentArg.setComponentId(componentId);
            // 调用openFsUserAppViewService.canAccessComponent
            BaseResult<Boolean> result = openFsUserAppViewService.canAccessComponent(header, canAccessComponentArg);
            if (result.isSuccess()) {
                return result.getResult();
            }
            log.warn("openFsUserAppViewService.canAccessComponent fail! canAccessComponentArg:{}, result:{}", canAccessComponentArg, result);
            // 如果调用不成功，返回默认值true
            return true;
        } catch (Exception e) {
            log.error("查询CRM应用权限异常，tenantId:{}, enterriseAccount:{}, employeeId:{}, appId:{}", tenantId, enterpriseAccount, employeeId, componentId, e);
            // 降级处理：当查询openCenter接口报错时，默认可以漏出crm应用
            return true;
        }
    }
}
