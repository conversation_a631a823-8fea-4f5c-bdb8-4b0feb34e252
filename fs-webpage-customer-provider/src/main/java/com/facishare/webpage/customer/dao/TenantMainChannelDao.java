package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.dao.entity.TenantMainChannelEntity;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/18
 */
public interface TenantMainChannelDao {

    /**
     * 保存租户级主导航
     *
     * @param tenantMainChannelEntity 租户级主导航数据
     * @return
     */
    TenantMainChannelEntity saveTenantMainChannelEntity(TenantMainChannelEntity tenantMainChannelEntity);

    /**
     * 根据apiName获取租户级主导航
     *
     * @param tenantId 租户Id
     * @param apiName  主导航apiName
     * @return
     */
    TenantMainChannelEntity getTenantMainChannelEntityByApiName(int tenantId, String apiName);

    /**
     * 获取所有的主导航数据
     *
     * @param tenantId  租户Id
     * @param scopeList 适用范围
     * @return
     */
    List<TenantMainChannelEntity> getTenantMainChannelEntities(int tenantId, List<String> scopeList);

    /**
     * 根据企业Id删除掉所有的租户主导航数据
     *
     * @param tenantId 租户id
     */
    void deleteTenantMainChannelEntities(int tenantId);

    /**
     * 根据apiName删除租户级主导航
     *
     * @param tenantId 租户Id
     * @param apiName  主导航apiName
     */
    void deleteTenantMainChannelByApiName(int tenantId, String apiName);

}
