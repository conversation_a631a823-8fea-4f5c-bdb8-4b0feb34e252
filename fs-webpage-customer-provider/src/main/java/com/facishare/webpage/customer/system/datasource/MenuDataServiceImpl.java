package com.facishare.webpage.customer.system.datasource;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.sysdb.filter.Filter;
import com.facishare.qixin.sysdb.model.Data;
import com.facishare.qixin.sysdb.serivce.DataSourceService;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.dao.TenantMenuDao;
import com.facishare.webpage.customer.dao.entity.MenuDataEntity;
import com.facishare.webpage.customer.dao.entity.TenantMenuEntity;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/3/8
 */
@Slf4j
public class MenuDataServiceImpl implements DataSourceService {

    @Resource
    private TenantMenuDao tenantMenuDao;

    @Override
    public Data getDataByDataId(Integer tenantId, String menuId, Filter filter) {
        TenantMenuEntity tenantMenuById = tenantMenuDao.getTenantMenuByFilter(menuId, filter);
        setMenuEntitiesDefaultAppId(tenantMenuById.getAppId(), tenantMenuById.getMenuDataEntities());
        return tenantMenuById;
    }

    @Override
    public List<? extends Data> batchGetDataByDataId(Integer tenantId, List menuIds, Filter filter) {
        log.info("batchGetDataByDataId.param tenantId={},menuIds = {},filter={}", tenantId, JSONObject.toJSONString(menuIds), JSONObject.toJSONString(filter));
        if (CollectionUtils.isEmpty(menuIds)) {
            return Lists.newArrayList();
        }
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuDao.getBatchTenantMenuByIds((List<String>) menuIds, filter);
        log.info("batchGetDataByDataId.result menuIds={}", JSONObject.toJSONString(tenantMenuEntities.stream().map(x -> x.getId()).collect(Collectors.toList())));
        for (TenantMenuEntity x : tenantMenuEntities) {
            setMenuEntitiesDefaultAppId(x.getAppId(), x.getMenuDataEntities());
        }
        return tenantMenuEntities;
    }

    @Override
    public Data findAndModify(int tenantId, Data data) {
        TenantMenuEntity tenantMenuEntity = tenantMenuDao.findAndModify(tenantId, (TenantMenuEntity) data);
        return tenantMenuEntity;
    }

    @Override
    public List<? extends Data> queryDataList(int tenantId, Filter filter) {
        log.info("queryDataList.param tenantId={},filter={}", tenantId, JSONObject.toJSONString(filter));
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuDao.queryTenantMenuList(tenantId, filter);
        log.info("queryDataList.result menuIds={}", JSONObject.toJSONString(tenantMenuEntities.stream().map(x -> x.getId()).collect(Collectors.toList())));
        for (TenantMenuEntity x : tenantMenuEntities) {
            setMenuEntitiesDefaultAppId(x.getAppId(), x.getMenuDataEntities());
        }
        return tenantMenuEntities;
    }

    @Override
    public List<? extends Data> queryUserDataList(int tenantId, int employeeId, Filter filter) {
        List<TenantMenuEntity> tenantMenuEntities = tenantMenuDao.queryTenantMenuList(tenantId, filter);
        for (TenantMenuEntity x : tenantMenuEntities) {
            setMenuEntitiesDefaultAppId(x.getAppId(), x.getMenuDataEntities());
        }
        return tenantMenuEntities;
    }

    private void setMenuEntitiesDefaultAppId(String appId, List<MenuDataEntity> menuDataEntities) {
        if (CollectionUtils.isEmpty(menuDataEntities)) {
            return;
        }
        menuDataEntities.stream().filter(x -> !MenuType.GROUP.equals(x.getType())).forEach(x -> {
            if (StringUtils.isEmpty(x.getAppId())) {
                x.setAppId(appId);
            }
        });
    }
}
