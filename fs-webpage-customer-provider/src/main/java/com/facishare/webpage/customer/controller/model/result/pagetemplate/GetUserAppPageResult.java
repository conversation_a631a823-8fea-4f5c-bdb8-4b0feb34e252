package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.controller.model.UserAppPageTemplate;
import com.facishare.webpage.customer.api.model.result.BaseResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.enterpriserelation2.result.EnterpriseInfoResult;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangyi on 2019/9/19.
 */
@Data
public class GetUserAppPageResult extends BaseResult {

    @JsonProperty("userAppPageTemplates")
    private List<UserAppPageTemplate> userAppPageTemplates = Lists.newArrayList();

    @JsonProperty("enterpriseInfo")
    private Map<Integer, EnterpriseInfoResult> enterpriseInfo;

    private String defaultTemplateId;
}
