package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.constant.WebMainChannelType;
import com.facishare.webpage.customer.service.RemoteService;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangyu on 2020/11/23
 */
@Component
public class WebMainChannelConfig {

    private static Logger logger = LoggerFactory.getLogger(ComponentConfig.class);

    private static final String defaultMenusKey = "default";
    /**
     * 旧版主站菜单排序
     */
    private Map<String, List<String>> webMainChannelMenusOrderMap = Maps.newHashMap();
    /**
     * 新版主站排序
     */
    private Map<String, List<String>> newWebMainChannelMenusOrderMap = Maps.newHashMap();
    /**
     * 主站默认显示多少个
     */
    private Map<String, Integer> defaultWebMainChannelMenusNumMap = Maps.newHashMap();
    /**
     * 追加主导航的菜单项
     */
    private List<AddMainChannelMenuData> addMainChannelMenuDataList = Lists.newArrayList();
    /**
     * 灰度的主导航菜单
     */
    private List<String> grayMenus = Lists.newArrayList();
    /**
     * 下线的主导航菜单
     */
    private List<String> closeMenus = Lists.newArrayList();
    /**
     * 具有票数的主导航菜单
     */
    private List<String> openStatsAppIds = Lists.newArrayList();

    private List<String> mustOpenStatsAppIds = Lists.newArrayList();
    /**
     * 主导航菜单的最大配置数
     */
    private Map<String, Integer> defaultWebMainMenuMaxMap = Maps.newHashMap();
    /**
     * 直接根据url跳转的应用
     */
    private List<String> goJumpAppIds = Lists.newArrayList();
    private final String CUSTOMER_LINK_APP_ID = "customerLinkAppId";

    @Resource
    private RemoteService remoteService;

    public Map<String, List<String>> WebMenuCountSource = Maps.newHashMap();

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-webpage-customer-web-mainChannel-config", this::loadConfig);

        ConfigFactory.getConfig("fs-ui-paas-config.ini", new IniChangeListener("webMainChannel") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadWebMainChannelSection(iniConfig);
            }
        });
    }

    private void loadConfig(IConfig config) {
        JSONObject jsonObject = JSONObject.parseObject(config.getString(), JSONObject.class);
        String webMainChannelMenuOrder = jsonObject.getString("webMainChannelMenuOrder");
        Map<String, List<String>> oldWebMainChannelMenuMap = covertMap(webMainChannelMenuOrder);
        this.webMainChannelMenusOrderMap = oldWebMainChannelMenuMap;

        String newWebMainChannelMenuOrder = jsonObject.getString("newWebMainChannelMenuOrder");
        Map<String, List<String>> newWebMainChannelMenuMap = covertMap(newWebMainChannelMenuOrder);
        this.newWebMainChannelMenusOrderMap = newWebMainChannelMenuMap;

        List<AddMainChannelMenuData> addMainChannelMenus = JSONArray.parseArray(jsonObject.getString("addMainChannelMenus"), AddMainChannelMenuData.class);
        addMainChannelMenus = addMainChannelMenus.stream().sorted((x1, x2) -> {
            if (x1.getId().equals(x2.getAfter())) {
                return -1;
            } else {
                return 1;
            }
        }).collect(Collectors.toList());
        this.addMainChannelMenuDataList = addMainChannelMenus;
        Map<String, Integer> defaultWebMainChannelMenusNumMap = Maps.newHashMap();
        String defaultWebMainChannelMenusNum = jsonObject.getString("defaultWebMainChannelMenusNum");
        if (StringUtils.isNotEmpty(defaultWebMainChannelMenusNum)) {
            defaultWebMainChannelMenusNumMap = JSON.parseObject(defaultWebMainChannelMenusNum, Map.class);
        }
        this.defaultWebMainChannelMenusNumMap = defaultWebMainChannelMenusNumMap;

        Map<String, Integer> defaultWebMainMenuMaxMap = Maps.newHashMap();
        String defaultWebMainMenuMax = jsonObject.getString("defaultWebMainMenuMax");
        if (StringUtils.isNotEmpty(defaultWebMainMenuMax)) {
            defaultWebMainMenuMaxMap = JSON.parseObject(defaultWebMainMenuMax, Map.class);
        }
        this.defaultWebMainMenuMaxMap = defaultWebMainMenuMaxMap;

        logger.info("init WebMainChannelConfig config " +
                        "webMainChannelMenusOrderMap:{}, " +
                        "newWebMainChannelMenuMap:{}, " +
                        "addMainChannelDataList:{}, " +
                        "defaultWebMainChannelMenusNumMap:{}, " +
                        "defaultWebMainMenuMaxMap:{}",
                this.webMainChannelMenusOrderMap,
                this.newWebMainChannelMenusOrderMap,
                this.addMainChannelMenuDataList,
                this.defaultWebMainChannelMenusNumMap,
                this.defaultWebMainMenuMaxMap);
    }

    private void reloadWebMainChannelSection(IniConfig iniConfig) {
        String grayMenusString = iniConfig.get("grayMenus");
        this.grayMenus = getSplitList(grayMenusString);

        String closeMenusString = iniConfig.get("closeMenus");
        this.closeMenus = getSplitList(closeMenusString);

        String openStatsAppIdsString = iniConfig.get("openStatsAppIds");
        this.openStatsAppIds = getSplitList(openStatsAppIdsString);

        String goJumpAppIdStr = iniConfig.get("goJumpAppId");
        this.goJumpAppIds = getSplitList(goJumpAppIdStr);

        String mustOpenStatsAppIdsString = iniConfig.get("mustOpenStatsAppIds");
        this.mustOpenStatsAppIds = getSplitList(mustOpenStatsAppIdsString);

        Map<String, List<String>> WebMenuCountSource = Maps.newHashMap();
        String webMenuCountSourceString = iniConfig.get("WebMenuCountSource");
        if (StringUtils.isNotEmpty(webMenuCountSourceString)) {
            WebMenuCountSource = JSON.parseObject(webMenuCountSourceString, Map.class);
        }
        this.WebMenuCountSource = WebMenuCountSource;

        logger.info("init reloadWebMainChannelSection config grayMenus:{}, closeMenus:{}, openStatsAppIds:{}, goJumpAppIds:{}, mustOpenStatsAppIds:{}",
                this.grayMenus, this.closeMenus, this.openStatsAppIds, this.goJumpAppIds, mustOpenStatsAppIds);
        logger.info("WebMenuCountSource:{}", this.WebMenuCountSource);
    }

    private List<String> getSplitList(String valueString) {
        String[] values = valueString.split(",");
        List<String> retList = Lists.newArrayList();
        for (String v : values) {
            if (StringUtils.isEmpty(v)) {
                continue;
            }
            retList.add(v.trim());
        }
        return retList;
    }

    private Map<String, List<String>> covertMap(String json) {
        Map<String, String> map = JSON.parseObject(json, Map.class);
        Map<String, List<String>> resultMap = Maps.newHashMap();
        map.entrySet().stream().forEach(x -> {
            String[] split = x.getValue().split(",");
            resultMap.put(x.getKey(), Arrays.asList(split));
        });
        return resultMap;
    }

    public List<String> getWebMainChannelMenus(int enterpriseId,
                                               String enterpriseAccount,
                                               List<ProductVersionPojo> productionVersions) {
        Map<String, List<String>> map;
        if (WebPageGraySwitch.isAllowByBusiness(WebPageGraySwitch.NewWebMainChannel, enterpriseId)) {
            map = newWebMainChannelMenusOrderMap;
        } else {
            map = webMainChannelMenusOrderMap;
        }

        List<String> menus = map.get(enterpriseAccount);
        if (CollectionUtils.isNotEmpty(menus)) {
            return menus;
        }
        Optional<ProductVersionPojo> productVersionOptional = productionVersions.stream().
                filter(versionPojo -> String.valueOf(0).equals(versionPojo.getProductType())).
                findFirst();
        String majorVersion = productVersionOptional.isPresent() ? productVersionOptional.get().getCurrentVersion() : null;
        if (StringUtils.isNotEmpty(majorVersion)) {
            menus = map.getOrDefault(majorVersion, Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(menus)) {
                return menus;
            }
        }

        for (ProductVersionPojo productVersionPojo : productionVersions) {
            List<String> versionMenus = map.get(productVersionPojo.getCurrentVersion());
            if (CollectionUtils.isNotEmpty(versionMenus)) {
                return versionMenus;
            }
        }

        return map.getOrDefault(defaultMenusKey, Lists.newArrayList());
    }

    public List<AddMainChannelMenuData> queryAddMainChannelMenuData() {
        return Collections.unmodifiableList(addMainChannelMenuDataList);
    }

    @Data
    public static class AddMainChannelMenuData implements Serializable {
        private String id;
        private String after;
    }

    public List<String> getGrayMenus() {
        return Collections.unmodifiableList(grayMenus);
    }

    public List<String> getCloseMenus() {
        return Collections.unmodifiableList(closeMenus);
    }

    public List<String> getOpenStatsAppIds() {
        return Collections.unmodifiableList(openStatsAppIds);
    }

    public boolean isOpenStatsAppId(String appId, boolean hidden, int appType) {
        if (CollectionUtils.isNotEmpty(mustOpenStatsAppIds) && mustOpenStatsAppIds.contains(appId)) {
            return true;
        }

        if (hidden) {
            return false;
        }
        // 自定义互联应用
        if (appType == WebMainChannelType.customerCrossAppType && openStatsAppIds.contains(CUSTOMER_LINK_APP_ID)) {
            return true;
        }
        if (appId.startsWith(Constant.PAAS_APPID_PRE)) {
            return true;
        }
        return openStatsAppIds.contains(appId);
    }

    public Integer getDefaultShowMenuNum(int enterpriseId, List<ProductVersionPojo> productionVersions) {
        Integer defaultShowMenuNum = defaultWebMainChannelMenusNumMap.get(String.valueOf(enterpriseId));
        if (defaultShowMenuNum != null) {
            return defaultShowMenuNum;
        }

        Optional<ProductVersionPojo> productVersionOptional = productionVersions.stream().
                filter(versionPojo -> String.valueOf(0).equals(versionPojo.getProductType())).
                findFirst();
        String majorVersion = productVersionOptional.isPresent() ? productVersionOptional.get().getCurrentVersion() : null;
        if (StringUtils.isEmpty(majorVersion)) {
            Integer num = defaultWebMainChannelMenusNumMap.get(majorVersion);
            if (num != null) {
                return num;
            }
        }

        for (ProductVersionPojo productVersionPojo : productionVersions) {
            Integer num = defaultWebMainChannelMenusNumMap.get(productVersionPojo.getCurrentVersion());
            if (num != null) {
                return num;
            }
        }
        return defaultWebMainChannelMenusNumMap.getOrDefault(defaultMenusKey, 6);
    }

    public Integer getWebMainMenuMax(int enterpriseId) {
        Integer count = defaultWebMainMenuMaxMap.get(String.valueOf(enterpriseId));
        if (count != null) {
            return count;
        }
        List<ProductVersionPojo> productionVersions = remoteService.getProductionVersion(enterpriseId);
        Optional<ProductVersionPojo> productVersionOptional = productionVersions.stream().
                filter(versionPojo -> String.valueOf(0).equals(versionPojo.getProductType())).
                findFirst();
        String majorVersion = productVersionOptional.isPresent() ? productVersionOptional.get().getCurrentVersion() : null;
        return defaultWebMainMenuMaxMap.getOrDefault(majorVersion, 8);
    }

    public boolean checkGoJumpUrl(String appId) {
        return goJumpAppIds.contains(appId);
    }

    public static void main(String[] args) {
        System.setProperty("process.profile", "fstest");
        WebMainChannelConfig webMainChannelConfig = new WebMainChannelConfig();
        webMainChannelConfig.init();
        List<String> result = webMainChannelConfig.getWebMainChannelMenus(79410, "79410", Lists.newArrayList());
        System.out.println(webMainChannelConfig.getGrayMenus());
        System.out.println(webMainChannelConfig.queryAddMainChannelMenuData());
    }


}
