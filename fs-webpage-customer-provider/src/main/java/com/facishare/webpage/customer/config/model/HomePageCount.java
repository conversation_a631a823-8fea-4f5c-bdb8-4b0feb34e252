package com.facishare.webpage.customer.config.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/2/13
 */
@Data
public class HomePageCount implements Serializable {

    /**
     * 租户级首页的最大值
     */
    private int maxTenantCount;

    /**
     * 个人级首页的最大值
     */
    private int maxPersonCount;

    /**
     * 图表组件配置的最大值
     * 890 将图表默认最大值从10调整为15·
     */
    private int chartMaxCount = 15;

    /**
     * 其他组件配置的最大值
     */
    private int otherMaxCount = 10;

    /**
     * 自定义菜单项的最大值
     */
    private int maxCusMenuCount = 20;

    /**
     * 个人级页面模板的最大值
     */
    private int maxUserPageTemplateCount = 3;

    /**
     * 单个站点的页面的最大值
     */
    private int maxSitePageCount = 20;

    /**
     * 单个站点页面的组件的最大值
     */
    private int maxSitePageComponentCount = 50;
}
