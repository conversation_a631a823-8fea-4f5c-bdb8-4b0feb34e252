package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.homepage.GetEmployeeConfigValueByKeysArg;
import com.facishare.webpage.customer.controller.model.arg.homepage.SetEmployeeConfigValueArg;
import com.facishare.webpage.customer.api.model.result.GetEmployeeConfigValueByKeysResult;
import com.facishare.webpage.customer.api.model.result.SetEmployeeConfigValueResult;

/**
 * Created by zhangyu on 2019/10/29
 */
public interface HomePageCommonAction {

    GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByKeys(UserInfo userInfo, OuterUserInfo outerUserInfo, GetEmployeeConfigValueByKeysArg arg);

    SetEmployeeConfigValueResult setEmployeeConfigValue(UserInfo userInfo, OuterUserInfo outerUserInfo,SetEmployeeConfigValueArg arg);

}
