package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/4 11:06 AM
 */
@Data
@Builder
public class SaveWebUserPageTemplateArg implements Serializable {

    private String pageTemplateId;
    private String appId;
    private String name;
    private String webMenuId;
    private String webPageId;
    private String appPageId;
    private String description;
    private int priorityLevel;
    private int pageTemplateType;
    private String type;
    /**
     * 是否已经同步至app端的标识
     */
    private boolean hasBeenSynToApp = false;

}
