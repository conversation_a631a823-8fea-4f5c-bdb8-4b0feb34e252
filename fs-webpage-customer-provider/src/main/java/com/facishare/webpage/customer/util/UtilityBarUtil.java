package com.facishare.webpage.customer.util;

import com.alibaba.fastjson.JSONObject;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.dao.entity.UtilityBarEntity;
import com.facishare.webpage.customer.model.UtilityBarVO;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> Yu
 */
public class UtilityBarUtil {

    public static UtilityBarEntity covertEntity(int tenantId, int employeeId, UtilityBarVO utilityBarVO) {
        UtilityBarEntity utilityBarEntity = new UtilityBarEntity();
        utilityBarEntity.setTenantId(tenantId);
        utilityBarEntity.setAppType(utilityBarVO.getAppType());
        utilityBarEntity.setAppId(utilityBarVO.getAppId());
        utilityBarEntity.setType(utilityBarVO.getType());
        utilityBarEntity.setPageTemplateId(utilityBarVO.getPageTemplateId());
        utilityBarEntity.setToolComponents(JSONObject.toJSONString(utilityBarVO.getUtilityBarLayout()));
        utilityBarEntity.setCreateId(employeeId);
        utilityBarEntity.setUpdateId(employeeId);

        return utilityBarEntity;
    }

    public static UtilityBarVO covertUtilityBarVO(UtilityBarEntity utilityBarEntity) {
        return UtilityBarVO.builder().
                appType(utilityBarEntity.getAppType()).
                id(Objects.isNull(utilityBarEntity.getId()) ? "" : utilityBarEntity.getId().toString()).
                appId(utilityBarEntity.getAppId()).
                type(utilityBarEntity.getType()).
                pageTemplateId(utilityBarEntity.getPageTemplateId()).
                utilityBarLayout(JSONObject.parseObject(utilityBarEntity.getToolComponents())).build();
    }

    public static UtilityBarVO covertUtilityBarVO(Integer appType,
                                                  String appId,
                                                  String pageTemplateId,
                                                  JSONObject utilityBarComponent) {
        return UtilityBarVO.builder().
                appType(appType).
                appId(appId).
                type(BizType.getBizTypeValue(appType).getPageTemplateType()).
                pageTemplateId(pageTemplateId).
                utilityBarLayout(utilityBarComponent).build();

    }
}
