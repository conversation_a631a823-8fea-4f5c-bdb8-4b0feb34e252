package com.facishare.webpage.customer.controller.model.result.pagetemplate;

import com.facishare.webpage.customer.api.model.PageTemplate;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2019/9/19
 */
@Data
public class GetPageTemplateByIdResult implements Serializable {
    @JsonProperty("pageTemplate")
    private PageTemplate pageTemplate;

}
