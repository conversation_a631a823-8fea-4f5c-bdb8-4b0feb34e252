package com.facishare.webpage.customer.controller.model.arg.paas;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.utils.UploadIcon;
import com.facishare.webpage.customer.controller.model.arg.BaseArg;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/12
 */
@Data
public class UpdatePaaSAppArg extends BaseArg {

    private String appId;

    private List<Scope> scopeList;

    private String name;

    private String description;

    private String icon;
    /**
     * 预置icon统一使用iconIndex
     */
    private String iconIndex;

    private int status;

    private String sourceType;

    /**
     * 是否允许该应用 使用个人级应用视图
     */
    private boolean useUserPageTempleFlag = false;
    /**
     * 上传图标内容
     */
    private UploadIcon uploadIcon;
    private String plugins;


    @Override
    public void valid() {
        if (StringUtils.isEmpty(appId) || CollectionUtils.isEmpty(scopeList) || StringUtils.isEmpty(name)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
