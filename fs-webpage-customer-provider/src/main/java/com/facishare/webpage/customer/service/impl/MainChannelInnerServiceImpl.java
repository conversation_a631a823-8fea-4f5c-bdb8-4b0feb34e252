package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.qixin.enterpriserelation.model.SyncEmployeeIdsEvent;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.webpage.customer.api.model.MainChannelMenu;
import com.facishare.webpage.customer.api.model.arg.QueryMainChannelMenuArg;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.config.model.MainChannelItem;
import com.facishare.webpage.customer.config.MainChannelMenuConfig;
import com.facishare.webpage.customer.core.config.MenusConfig;
import com.facishare.webpage.customer.dao.MainChannelEntityDao;
import com.facishare.webpage.customer.dao.entity.MainChannelEntity;
import com.facishare.webpage.customer.dao.entity.MainChannelMenuEntity;
import com.facishare.webpage.customer.permission.MenuPermissionService;
import com.facishare.webpage.customer.service.MainChannelInnerService;
import com.facishare.webpage.customer.util.MainChannelUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Service("mainChannelInnerService")
public class MainChannelInnerServiceImpl implements MainChannelInnerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MainChannelInnerServiceImpl.class);

    private int PRODUCT_TYPE_CRM = 0;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Resource
    private MenusConfig menusConfig;

    @Resource
    private MainChannelMenuConfig mainChannelMenuConfig;

    @Resource
    private MenuPermissionService menuPermissionService;

    @Resource
    private MainChannelMenuConvert mainChannelMenuConvert;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private MainChannelEntityDao mainChannelEntityDao;

    @Override
    public List<MainChannelMenu> queryMainChannelMenu(QueryMainChannelMenuArg arg) {

        MainChannelEntity mainChannelEntity = mainChannelEntityDao.getMainChannelEntity(arg.getTenantId(), arg.getEmployeeId());
        if (mainChannelEntity == null || CollectionUtils.isEmpty(mainChannelEntity.getMainChannelMenus())) {
            LOGGER.info("queryMainChannelMenu_arg:{},not find mainChannelEntity", JSON.toJSONString(arg));
            return Lists.newArrayList();
        }

        return queryMainChannelMenuV2(arg, mainChannelEntity.getMainChannelMenus());

    }

    @Override
    public List<MainChannelMenu> queryMainChannelMenuV2(QueryMainChannelMenuArg arg, List<MainChannelMenuEntity> mainChannelMenus) {
        LOGGER.info("queryMainChannelMenu_arg:{}", JSON.toJSONString(arg));
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(arg.getTenantId()));
        licenseContext.setUserId(String.valueOf(arg.getEmployeeId()));
        QueryProductArg queryProductArg = new QueryProductArg();
        queryProductArg.setLicenseContext(licenseContext);
        LicenseVersionResult versionResult = licenseClient.queryProductVersion(queryProductArg);
        String currentProductVersion = getMainProductVersion(versionResult);

        List<Menu> menus = mainChannelMenuConfig.getMainChannelMenuByProductCode(arg.getEnterpriseAccount(), currentProductVersion, mainChannelMenus);
        if (CollectionUtils.isEmpty(menus)) {
            return Lists.newArrayList();
        }
        Set<String> licenseVersion = versionResult.getResult().stream().map(projo -> projo.getCurrentVersion()).collect(Collectors.toSet());

        GetEnterpriseDataArg getEnterpriseDataArg = new GetEnterpriseDataArg();
        getEnterpriseDataArg.setEnterpriseId(arg.getTenantId());
        GetEnterpriseDataResult enterpriseDataRet = enterpriseEditionService.getEnterpriseData(getEnterpriseDataArg);
        EnterpriseData enterpriseData = enterpriseDataRet.getEnterpriseData();

        List<Menu> menuList = menuPermissionService.filterMenusWithPermission(arg.getTenantId(), arg.getEnterpriseAccount(), arg.getEmployeeId(), enterpriseData.getSource(), menus, licenseVersion);
        Set<String> enabledMenuIds = menuList.stream().map(menu -> menu.getId()).collect(Collectors.toSet());
        List<Menu> enabledMenus = menus.stream().filter(menu -> enabledMenuIds.contains(menu.getId())).collect(Collectors.toList());

        List<MainChannelMenu> result = mainChannelMenuConvert.convert2MainChannelMenu(arg.getTenantId(), arg.getLocale(), enabledMenus);
        LOGGER.info("queryMainChannelMenu_result:{}", JSON.toJSONString(result));
        return result;
    }

    private String getMainProductVersion(LicenseVersionResult versionResult) {
        if (versionResult == null || CollectionUtils.isEmpty(versionResult.getResult())) {
            return null;
        }
        Optional<ProductVersionPojo> productVersionOptional = versionResult.getResult().stream().filter(versionPojo -> String.valueOf(PRODUCT_TYPE_CRM).equals(versionPojo.getProductType())).findFirst();
        return productVersionOptional.isPresent() ? productVersionOptional.get().getCurrentVersion() : null;
    }

    @Override
    public void createOrUpdateMainChannelEntity(SyncEmployeeIdsEvent syncEmployeeIdsEvent) {
        LOGGER.info("createOrUpdateMainChannelEntity syncEmployeeIdsEvent:{}", syncEmployeeIdsEvent);
        MainChannelItem mainChannelItem = mainChannelMenuConfig.getMainChannelItemByEa(syncEmployeeIdsEvent.getUpStreamEa());
        if (mainChannelItem == null || CollectionUtils.isEmpty(mainChannelItem.getMenus())) {
            LOGGER.warn("not config MainChannelItem,ea:{}", syncEmployeeIdsEvent.getUpStreamEa());
            return;
        }

        String mainChannelId = mainChannelItem.getMainChannelId();
        if (Strings.isNullOrEmpty(mainChannelId)) {
            LOGGER.warn("mainChannelId is empty", mainChannelId);
            return;
        }
        List<Menu> menus = menusConfig.getMenusByIds(mainChannelItem.getMenus());

        String newMainChannelId = MainChannelUtils.buildMainChannelId(syncEmployeeIdsEvent.getDownTenantId(), mainChannelId);
        MainChannelEntity channelEntity = new MainChannelEntity();
        channelEntity.setMainChannelId(newMainChannelId);
        channelEntity.setEffectiveEmployeeIds(Lists.newArrayList(syncEmployeeIdsEvent.getDownEmployeeIds()));
        channelEntity.setMainChannelMenus(buildMainChannelMenuEntity(menus, mainChannelItem.getSelectedMenu()));
        channelEntity.setCreatorId(-10000);
        channelEntity.setCreateTime(System.currentTimeMillis());
        channelEntity.setSourceType(syncEmployeeIdsEvent.getAppId());
        channelEntity.setStatus(0);
        channelEntity.setTenantId(syncEmployeeIdsEvent.getDownTenantId());
        channelEntity.setUpdateTime(System.currentTimeMillis());
        mainChannelEntityDao.findAndModify(channelEntity);
    }

    private List<MainChannelMenuEntity> buildMainChannelMenuEntity(List<Menu> menus, String selectedMenu) {
        return menus.stream().map(menu -> {
            MainChannelMenuEntity mainChannelMenuEntity = new MainChannelMenuEntity();
            mainChannelMenuEntity.setGroupIndex(menu.getGroupIndex());
            mainChannelMenuEntity.setMenuId(menu.getId());
            mainChannelMenuEntity.setSelected(menu.getId().equals(selectedMenu));
            return mainChannelMenuEntity;
        }).collect(Collectors.toList());
    }

    @Override
    public void removeEmployeeMainChannelEntity(SyncEmployeeIdsEvent syncEmployeeIdsEvent) {
        LOGGER.info("removeEmployeeMainChannelEntity syncEmployeeIdsEvent:{}", syncEmployeeIdsEvent);
        MainChannelItem mainChannelItem = mainChannelMenuConfig.getMainChannelItemByEa(syncEmployeeIdsEvent.getUpStreamEa());
        if (mainChannelItem == null || CollectionUtils.isEmpty(mainChannelItem.getMenus())) {
            LOGGER.warn("not config MainChannelItem,ea:{}", syncEmployeeIdsEvent.getUpStreamEa());
            return;
        }
        if (CollectionUtils.isEmpty(syncEmployeeIdsEvent.getDownEmployeeIds())) {
            LOGGER.warn("delete employeeIds is Empty syncEmployeeIdsEvent:{}", syncEmployeeIdsEvent);
            return;
        }
        String newMainChannelId = MainChannelUtils.buildMainChannelId(syncEmployeeIdsEvent.getDownTenantId(), mainChannelItem.getMainChannelId());
        mainChannelEntityDao.removeEmployeeIds(newMainChannelId, Lists.newArrayList(syncEmployeeIdsEvent.getDownEmployeeIds()));
    }
}
