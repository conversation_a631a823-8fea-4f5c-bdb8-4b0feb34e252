package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.dao.entity.UserPageTemplateEntity;

import java.util.List;

/**
 * <AUTHOR> Yu
 * @date 2022/3/3 8:24 PM
 */
public interface UserPageTemplateDao {

    /**
     * findAndModify pageTemplate
     *
     * @param userPageTemplateEntity
     */
    void findAndModify(UserPageTemplateEntity userPageTemplateEntity);

    /**
     * query pageTemplate by id
     *
     * @param pageTemplateId
     * @return
     */
    UserPageTemplateEntity queryUserPageTemplateById(String pageTemplateId);

    /**
     * query pageTemplate by sourceId
     *
     * @param enterpriseId
     * @param employeeId
     * @param sourceId
     * @param appId
     * @param type
     * @return
     */
    UserPageTemplateEntity queryUserPageTemplateBySourceId(int enterpriseId, int employeeId, String sourceId, String appId, String type);

    /**
     * delete user pageTemplate
     *
     * @param enterpriseId
     * @param employeeId
     * @param pageTemplateId
     * @param appId
     * @param type
     */
    void deleteUserPageTemplateEntity(int enterpriseId, int employeeId, String pageTemplateId, String appId, String type);

    /**
     * query user pageTemplate list
     *
     * @param enterpriseId
     * @param employeeId
     * @param appId
     * @param type
     * @return
     */
    List<UserPageTemplateEntity> queryUserPageTemplateEntities(int enterpriseId, int employeeId, String appId, String type);

    UserPageTemplateEntity updateUserPageTempleStatus(String templeId, Integer status, Integer employeeId);

    long getUserPageTempletCount(String appId, Integer enterpriseId, Integer employeeId, String type);

    void saveBatch(List<UserPageTemplateEntity> pageTempleEntities);

    void deleteAllUserPageTemplateByTenantId(Integer enterpriseId, String appId);
}
