package com.facishare.webpage.customer.model;

import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.WebMainChannelMenuVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/26 11:10 上午
 */
@Data
@Builder
public class TenantMainChannelAO {
    private String apiName;
    private String name;
    private List<Scope> scopeList;
    private Integer priorityLevel;
    private List<WebMainChannelMenuVO> webMainChannelMenuVOList;
    private Integer version;
    private Boolean showAppName;
    private Boolean canCustom;
    private Boolean showMoreAppEntry;
    private String sourceType;

}
