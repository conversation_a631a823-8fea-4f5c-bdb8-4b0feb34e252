package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.constant.AppRoleType;
import com.facishare.enterprise.common.constant.GlobalRoleConstants;
import com.facishare.enterprise.common.constant.GuestorIdentityInfoConstants;
import com.facishare.enterprise.common.constant.LayoutTypeEnum;
import com.facishare.enterprise.common.model.OuterRoleVo;
import com.facishare.enterprise.common.model.Pair;
import com.facishare.enterprise.common.model.paas.ObjectConfigVO;
import com.facishare.enterprise.common.model.paas.SimpleObjectLayoutVO;
import com.facishare.enterprise.common.model.paas.SimpleRecordTypeVO;
import com.facishare.enterprise.common.model.paas.SimpleRoleViewVO;
import com.facishare.er.api.model.vo.AddRoleLayoutVO;
import com.facishare.er.api.model.vo.AddRoleRecordTypeVO;
import com.facishare.er.api.model.vo.AllocatedLayoutVO;
import com.facishare.er.api.model.vo.RoleAssociationLayoutVO;
import com.facishare.er.api.util.BeanUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.LinkAppObjectAssociationType;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.LinkAppObjectAssociationVO;
import com.facishare.webpage.customer.api.model.ObjectSimpleVo;
import com.facishare.webpage.customer.api.model.RoleInfo;
import com.facishare.webpage.customer.api.model.arg.CheckLinkAppAssociateObjectApiEnableArg;
import com.facishare.webpage.customer.api.model.arg.DeleteLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.arg.GetLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.arg.UpdateLinkAppAssociationObjectListArg;
import com.facishare.webpage.customer.api.model.result.CheckLinkAppAssociateObjectApiEnableResult;
import com.facishare.webpage.customer.api.model.result.apibus.Result;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.LinkAppAssociateObjectApiValidateConfig;
import com.facishare.webpage.customer.core.config.ApplicationLayeredConfig;
import com.facishare.webpage.customer.core.config.PresetRoleRecordTypeConfig;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.LinkAppObjectAssociationDao;
import com.facishare.webpage.customer.dao.entity.LinkAppObjectAssociationEntity;
import com.facishare.webpage.customer.model.AllocatedRecordTypePojo;
import com.facishare.webpage.customer.model.RoleAssociationRecordTypePojo;
import com.facishare.webpage.customer.mq.model.AllocateRecordEvent;
import com.facishare.webpage.customer.mq.producer.AllocateRecordMqProducer;
import com.facishare.webpage.customer.rest.arg.ServiceInvokeArg;
import com.facishare.webpage.customer.service.CrmPassCacheManager;
import com.facishare.webpage.customer.service.FunctionService;
import com.facishare.webpage.customer.service.LinkAppObjectAssociationService;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.fxiaoke.common.http.HttpClient;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.crmrestapi.arg.AppAuthContext;
import com.fxiaoke.crmrestapi.arg.BatchDeleteRoleEntityRelationArg;
import com.fxiaoke.crmrestapi.arg.SupportManyAbstractLayoutFromObjApiNamesArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.SupportManyAbstractLayoutFromObjApiNamesResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.crmrestapi.service.RecordTypeAuthService;
import com.fxiaoke.enterpriserelation.objrest.arg.BatchGetCreateUpdateLayoutStatusArg;
import com.fxiaoke.enterpriserelation.objrest.service.EnterpriseRelationObjService;
import com.fxiaoke.paasauthrestapi.common.data.RoleViewData;
import com.fxiaoke.paasauthrestapi.result.RecordTypeData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.core.util.WebPageGraySwitch.isListLayoutToRecordGrayEi;


@Service
@Slf4j
public class LinkAppObjectAssociationServiceImpl implements LinkAppObjectAssociationService {
    @Autowired
    private LinkAppObjectAssociationDao linkAppObjectAssociationDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseRelationObjService enterpriseRelationObjService;
    public static final Integer SYSTEM_USER = -10000;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    DefaultTenantConfig defaultTenantConfig;

    @Autowired
    private CrmPassCacheManager crmPassCacheManager;
    @Autowired
    private RecordTypeAuthService recordTypeAuthService;
    @Autowired
    private RemoteCrossService remoteCrossService;

    @Autowired
    private FunctionService functionService;

    @Resource
    private PresetRoleRecordTypeConfig presetRoleRecordTypeConfig;

    @Autowired
    private AllocateRecordMqProducer allocateRecordMqProducer;

    @Autowired
    private OkHttpSupport webPageHttpClient;
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final String LIST_LAYOUT_RECORD = "list_layout_record";

    private static final List<String> FUNCTION_CODES = Lists.newArrayList("Add", "Edit", "Import", "Transform");


    @Override
    public List<LinkAppObjectAssociationVO> listByTypeAndUpstreamAndAppId(Integer type, String upstream, String appId, boolean allAppIdFlag) {
        Set<String> excludeApiNames = Sets.newHashSet();
        List<LinkAppObjectAssociationVO> associationObjects = Lists.newArrayList();
        if (null == type || (type == LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE)) {
            List<LinkAppObjectAssociationEntity> upstreamObjects = linkAppObjectAssociationDao.listByTypeAndUpstreamAndAppId(LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE, upstream, appId, null, allAppIdFlag);
            if (CollectionUtils.isNotEmpty(upstreamObjects)) {
                upstreamObjects.forEach(association -> {
                    if (!excludeApiNames.contains(association.getObjectApiName())) {
                        LinkAppObjectAssociationVO linkAppAssociationObjectVO = new LinkAppObjectAssociationVO();
                        linkAppAssociationObjectVO.setLinkAppId(association.getLinkAppId());
                        linkAppAssociationObjectVO.setInLay(Boolean.FALSE);
                        linkAppAssociationObjectVO.setObjectApiName(association.getObjectApiName());
                        linkAppAssociationObjectVO.setAllowRemove(association.getAllowRemove());
                        if (association.getNeedAllocate() != null) {
                            linkAppAssociationObjectVO.setNeedAllocate(association.getNeedAllocate());
                        }
                        associationObjects.add(linkAppAssociationObjectVO);
                        excludeApiNames.add(association.getObjectApiName());
                    }
                });
            }
        }
        if (null == type || (type == LinkAppObjectAssociationType.ASSOCIATION_TYPE)) {
            List<LinkAppObjectAssociationEntity> inlayObjects = linkAppObjectAssociationDao.listByTypeAndUpstreamAndAppId(LinkAppObjectAssociationType.ASSOCIATION_TYPE, null, appId, null, allAppIdFlag);
            if (CollectionUtils.isNotEmpty(inlayObjects)) {
                inlayObjects.forEach(inlayObject -> {
                    if (!excludeApiNames.contains(inlayObject.getObjectApiName())) {
                        LinkAppObjectAssociationVO linkAppAssociationObjectVO = new LinkAppObjectAssociationVO();
                        linkAppAssociationObjectVO.setLinkAppId(inlayObject.getLinkAppId());
                        linkAppAssociationObjectVO.setInLay(Boolean.TRUE);
                        linkAppAssociationObjectVO.setObjectApiName(inlayObject.getObjectApiName());
                        if (inlayObject.getNeedAllocate() != null) {
                            linkAppAssociationObjectVO.setNeedAllocate(inlayObject.getNeedAllocate());
                        }
                        linkAppAssociationObjectVO.setAllowRemove(Boolean.FALSE);
                        excludeApiNames.add(inlayObject.getObjectApiName());
                        associationObjects.add(linkAppAssociationObjectVO);
                    }
                });
            }
        }
        Set<String> apiNames = associationObjects.stream().map(LinkAppObjectAssociationVO::getObjectApiName).collect(Collectors.toSet());
        ServiceContext serviceContext = enterpriseRelationObjService.createServiceContext(eieaConverter.enterpriseAccountToId(upstream) + "", "-10000");
        Map<String, Boolean> apiNameCreateEditLayoutStatus;
        Map<String, Boolean> appLayeredStatusMap = Maps.newHashMap();
        //
        if (ApplicationLayeredConfig.isAllow(appId, serviceContext.getTenantId())) {
            CrmPassCacheManager.AppLayeredAndEditLayoutStatusResult statusResult = crmPassCacheManager.findAppLayeredAndEditLayoutStatus(serviceContext.getUser(), apiNames, appId);
            apiNameCreateEditLayoutStatus = statusResult.getEditLayoutStatusMap();
            appLayeredStatusMap = statusResult.getAppLayeredStatusMap();
        } else {
            BatchGetCreateUpdateLayoutStatusArg batchGetCreateUpdateLayoutStatusArg = new BatchGetCreateUpdateLayoutStatusArg();
            batchGetCreateUpdateLayoutStatusArg.setApiNames(apiNames);
            apiNameCreateEditLayoutStatus = crmPassCacheManager.batchGetCreateUpdateLayoutStatus(serviceContext, batchGetCreateUpdateLayoutStatusArg).getApiNameUpdateLayoutStatusMap();
        }
        Map<String, Boolean> mobileAbstractLayoutStatus = getSupportMobileAbstractLayoutStatus(upstream, apiNames);

        for (LinkAppObjectAssociationVO datum : associationObjects) {
            // 填充是否开启新建编辑布局
            if (apiNameCreateEditLayoutStatus.getOrDefault(datum.getObjectApiName(), Boolean.FALSE).equals(Boolean.TRUE)) {
                datum.setNeedAllocateCreateEditLayout(Boolean.TRUE);
            } else {
                datum.setNeedAllocateCreateEditLayout(Boolean.FALSE);
            }
            datum.setSupportAppLayered(BooleanUtils.isTrue(appLayeredStatusMap.get(datum.getObjectApiName())));
            // 填充是否示开启移动端摘要布局
            Boolean isNeedAllocateMobileSummaryLayout = mobileAbstractLayoutStatus.getOrDefault(datum.getObjectApiName(), Boolean.FALSE);
            datum.setNeedAllocateMobileSummaryLayout(isNeedAllocateMobileSummaryLayout);
            datum.setNeedAllocateListLayoutByRecord(Strings.isNotBlank(serviceContext.getTenantId()) && isListLayoutToRecordGrayEi(Integer.valueOf(serviceContext.getTenantId())));
            datum.setHideOwnerAllocateRule(UdobjGrayConfig.isAllow("default_value_support_owner_gray", serviceContext.getTenantId()));
        }
        return associationObjects;
    }

    private Map<String, Boolean> getSupportMobileAbstractLayoutStatus(String upstreamEa, Set<String> apiNames) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(upstreamEa);

        if (!WebPageGraySwitch.isAllowMobileAbstractLayoutGrayEasGray(tenantId)) {
            return new HashMap<>();
        }
        HeaderObj headerObj = new HeaderObj(tenantId, SYSTEM_USER);
        SupportManyAbstractLayoutFromObjApiNamesArg arg = new SupportManyAbstractLayoutFromObjApiNamesArg();
        arg.setDescribeApiNames(apiNames.stream().collect(Collectors.toList()));
        com.fxiaoke.crmrestapi.common.result.Result<SupportManyAbstractLayoutFromObjApiNamesResult> result = objectDescribeService.supportManyAbstractLayoutFromObjApiNames(headerObj, arg);
        Map<String, Boolean> dataMap = new HashMap<>();
        if (result.isSuccess()) {
            List<SupportManyAbstractLayoutFromObjApiNamesResult.ApiNameGrayInfo> data = result.getData().getGrayList();
            data.forEach(item -> {
                dataMap.put(item.getDescribeApiName(), item.getGray());
            });
        }
        return dataMap;
    }

    @Override
    public Pair<Boolean, String> checkLinkAppAssociateObjectApiEnable(String upstreamEa, int userId, String appId, List<String> objectApiNames, String action) {
        try {
            Map<String, LinkAppAssociateObjectApiValidateConfig> linkAppAssociateObjectApiValidateConfigMap = defaultTenantConfig.linkAppAssociateObjectApiValidateConfigMap;
            if (null == linkAppAssociateObjectApiValidateConfigMap) {
                return new Pair<>(true, null);
            }
            LinkAppAssociateObjectApiValidateConfig config = linkAppAssociateObjectApiValidateConfigMap.get(appId);
            if (config == null) {
                return new Pair<>(true, null);
            }

            int ei = eieaConverter.enterpriseAccountToId(upstreamEa);
            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put("x-fs-ei", String.valueOf(ei));
            headerMap.put("x-fs-userInfo", String.valueOf(userId));
            headerMap.put("X-fs-Employee-Id", String.valueOf(userId));

            CheckLinkAppAssociateObjectApiEnableArg arg = new CheckLinkAppAssociateObjectApiEnableArg();
            arg.setAppId(appId);
            arg.setAction(action);
            arg.setObjectApiName(objectApiNames);

            HttpClient httpClient = HttpClient.defaultClient();

            String response = httpClient.postJson(config.getUrl(), headerMap, gson.toJson(arg));
            log.info("checkLinkAppAssociateObjectApiEnable response:{}", response);
            Result<CheckLinkAppAssociateObjectApiEnableResult> result = gson.fromJson(response, new TypeToken<Result<CheckLinkAppAssociateObjectApiEnableResult>>() {
            }.getType());
            if (!result.isSuccess() || result.getResult() == null) {
                return new Pair<>(true, null);
            }
            return new Pair<>(result.getResult().getEnable(), result.getResult().getMessage());

        } catch (Exception e) {
            log.warn("checkLinkAppAssociateObjectApiEnable error. ea={}, userId={}, appId={}, action={}, objectApiNames={}", upstreamEa, userId, appId, action, objectApiNames, e);
        }
        return new Pair<>(true, null);
    }

    public <T> void fillObjectDescribe(int fsEi, List<T> appAssociationObjects) {
        String ea = eieaConverter.enterpriseIdToAccount(fsEi);
        List<String> apiNames = Lists.newArrayList();
        appAssociationObjects.stream().forEach(x -> {
            try {
                String apiName = BeanUtils.getProperty(x, "objectApiName");
                apiNames.add(apiName);
            } catch (Exception e) {
            }
        });
        Map<String, ObjectSimpleVo> objectMap = crmPassCacheManager.getSimpleObjectMapByApiNames(ea, apiNames);
        for (T association : appAssociationObjects) {
            try {
                String objectApiName = BeanUtils.getProperty(association, "objectApiName");
                ObjectSimpleVo objectSimpleVo = objectMap.get(objectApiName);
                BeanUtils.setProperty(association, "objectLabel", objectSimpleVo.getLabel());
                BeanUtils.setProperty(association, "ownerAllocateRule", objectSimpleVo.getOwnerAllocateRule());
                BeanUtils.setProperty(association, "masterDetail", BooleanUtils.isTrue(objectSimpleVo.getMasterDetailObj()));

                List<SimpleRecordTypeVO> recordTypes = crmPassCacheManager.listRecordTypes(ea, objectApiName);
                if (org.apache.commons.collections.CollectionUtils.isEmpty(recordTypes)) {
                    BeanUtils.setProperty(association, "needAllocate", false);
                }
            } catch (Exception e) {
                try {
                    BeanUtils.setProperty(association, "needAllocate", false);
                } catch (Exception e1) {
                    log.warn("fillObjectDescribe error fsEi={} appAssociationObjects={}", fsEi, appAssociationObjects, e1);
                }
            }
        }
    }

    @Override
    public void fillObjectMetadataForAssociations(int enterpriseId, List<LinkAppObjectAssociationVO> appAssociationObjects) {
        if (CollectionUtils.isEmpty(appAssociationObjects)) {
            log.warn("fillObjectMetadataForAssociations: empty association objects for ei={}", enterpriseId);
            return;
        }
        String ea = eieaConverter.enterpriseIdToAccount(enterpriseId);
        List<String> apiNames = appAssociationObjects.stream()
                .map(LinkAppObjectAssociationVO::getObjectApiName)
                .collect(Collectors.toList());

        Map<String, ObjectSimpleVo> objectMap = crmPassCacheManager.getObjectMetadataMapByApiNames(enterpriseId, apiNames);

        for (LinkAppObjectAssociationVO association : appAssociationObjects) {
            String objectApiName = association.getObjectApiName();
            ObjectSimpleVo objectSimpleVo = objectMap.get(objectApiName);

            if (Objects.isNull(objectSimpleVo)) {
                log.warn("Object metadata not found for apiName={} in ei={}", objectApiName, enterpriseId);
                association.setNeedAllocate(false);
                continue;
            }

            association.setObjectLabel(objectSimpleVo.getLabel());
            association.setOwnerAllocateRule(objectSimpleVo.getOwnerAllocateRule());
            association.setNeedAllocateDetailLayout(StringUtils.isEmpty(objectSimpleVo.getOriginalDescribeApiName()));
            association.setMasterDetail(BooleanUtils.isTrue(objectSimpleVo.getMasterDetailObj()));

            try {
                List<SimpleRecordTypeVO> recordTypes = crmPassCacheManager.listRecordTypes(ea, objectApiName);
                if (CollectionUtils.isEmpty(recordTypes)) {
                    log.info("No record types found for object {} in enterprise {}", objectApiName, ea);
                    association.setNeedAllocate(false);
                }
            } catch (Exception e) {
                log.error("Unexpected error while processing object {} in enterprise {}: {}", objectApiName, ea, e.getMessage(), e);
                association.setNeedAllocate(false);
            }
        }
    }

    @Override
    public List<LinkAppObjectAssociationVO> batchAddAssociationObjects(String upstream, String appId, List<String> objectApiNames, Boolean allowRemove) {
        if (CollectionUtils.isNotEmpty(objectApiNames)) {
            List<LinkAppObjectAssociationVO> sysObjects = listByTypeAndUpstreamAndAppId(LinkAppObjectAssociationType.ASSOCIATION_TYPE, upstream, appId, false);
            Set<String> sysObjectApiNameSet = sysObjects.stream().map(x -> x.getObjectApiName()).collect(Collectors.toSet());
            for (String objectApiName : objectApiNames) {
                try {
                    // 排除预制对象
                    if (sysObjectApiNameSet.contains(objectApiName)) {
                        continue;
                    }
                    LinkAppObjectAssociationEntity linkAppObjectAssociationEntity = new LinkAppObjectAssociationEntity();
                    linkAppObjectAssociationEntity.setLinkAppId(appId);
                    linkAppObjectAssociationEntity.setObjectApiName(objectApiName);
                    linkAppObjectAssociationEntity.setUpstreamEa(upstream);
                    linkAppObjectAssociationEntity.setAllowRemove(allowRemove);
                    Date now = new Date();
                    linkAppObjectAssociationEntity.setCreateTime(now);
                    linkAppObjectAssociationEntity.setUpdateTime(now);
                    linkAppObjectAssociationEntity.setType(LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE);
                    linkAppObjectAssociationEntity.setNotLayoutNames(new ArrayList<>());
                    linkAppObjectAssociationEntity.setNotRecordTypeNames(new ArrayList<>());
                    linkAppObjectAssociationEntity.setNeedAllocate(true);

                    linkAppObjectAssociationDao.insert(linkAppObjectAssociationEntity);
                    sendLinkAppObjectAssociationInnerEvent(upstream, appId, objectApiName);
                } catch (Exception e) {
                    log.warn("insert warn", e);
                }
            }
        }
        return listByTypeAndUpstreamAndAppId(LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE, upstream, appId, false);
    }

    @Override
    public AllocatedRecordTypePojo listAllocateRecordTypes(Integer fsEi, String fsEa, Integer userId, String appId, String objectApiName, Integer appType) {
        AllocatedRecordTypePojo allocatedRecordTypePojo = new AllocatedRecordTypePojo();
        //查询应用不允许使用的布局、业务类型列表
        com.facishare.enterprise.common.result.Result<ObjectConfigVO> objectInfoRet = null;
        objectInfoRet = getInlayObjectConfigInfo(appId, objectApiName);
        if (objectInfoRet.isSuccess() && objectInfoRet.getData() != null) {
            if (BooleanUtils.isFalse(objectInfoRet.getData().getNeedAllocate())) {
                throw new WebPageException(InterErrorCode.NOT_ALLOWED_ALLOCATE);
            }
        }

        //获取布局、业务类型列表
        List<SimpleRecordTypeVO> recordTypes = crmPassCacheManager.listRecordTypes(fsEa, objectApiName);
        //过滤不允许的数据
        if (objectInfoRet.isSuccess() && objectInfoRet.getData() != null) {
            List<String> noAllowedRecordTypes = Lists.newArrayList();

            if (!org.springframework.util.CollectionUtils.isEmpty(objectInfoRet.getData().getNoNeedRecordTypeNames())) {
                noAllowedRecordTypes.addAll(objectInfoRet.getData().getNoNeedRecordTypeNames());
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(recordTypes)) {
                recordTypes = recordTypes.stream().filter(x -> !noAllowedRecordTypes.contains(x.getApi_name())).collect(Collectors.toList());
            }
        }
        if (org.springframework.util.CollectionUtils.isEmpty(recordTypes)) {
            throw new WebPageException(InterErrorCode.NOT_ALLOWED_ALLOCATE);
        }
        allocatedRecordTypePojo.setRecordTypeList(recordTypes);

        //组装角色已经分配的业务类型
        List<OuterRoleVo> roles = new ArrayList<>();
        if (appType == 1) {
            roles = listLinkAppRoles(fsEi, appId);
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(roles)) {
            List<String> roleIds = roles.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toList());
            List<RecordTypeData> recordTypeDataList = crmPassCacheManager.batchFindRecordType(fsEi, objectApiName, roleIds);
            Map<String, List<String>> roleRecordTypeMap = Maps.newHashMap();
            if (!org.springframework.util.CollectionUtils.isEmpty(recordTypeDataList)) {
                recordTypeDataList.forEach(recordType -> {
                    List<String> roleRecordTypeList = roleRecordTypeMap.get(recordType.getRoleCode());
                    if (roleRecordTypeList == null) {
                        roleRecordTypeList = Lists.newArrayList();
                        roleRecordTypeMap.put(recordType.getRoleCode(), roleRecordTypeList);
                    }
                    roleRecordTypeList.add(recordType.getRecordTypeId());
                });
            }

            Map<String, Set<String>> roleFunction = functionService.queryFuncAccessByRolesAndEntity(fsEi, userId, objectApiName, Sets.newHashSet(roleIds));

            List<RoleAssociationRecordTypePojo> roleList = Lists.newArrayList();
            for (OuterRoleVo role : roles) {
                RoleAssociationRecordTypePojo roleAssociationRecordTypePojo = new RoleAssociationRecordTypePojo();
                //判断如果角色对于当前对象有
                Set<String> functionSet = roleFunction.get(role.getRoleId());
                roleAssociationRecordTypePojo.setRequired(CollectionUtils.isNotEmpty(functionSet)
                        && CollectionUtils.containsAny(functionSet, FUNCTION_CODES));
                roleAssociationRecordTypePojo.setRoleCode(role.getRoleId());
                roleAssociationRecordTypePojo.setRoleName(role.getRoleName());
                roleAssociationRecordTypePojo.setRecordTypeApiNames(roleRecordTypeMap.getOrDefault(role.getRoleId(), Lists.newArrayList()));
                roleAssociationRecordTypePojo.setReadOnlyRecordTypes(presetRoleRecordTypeConfig.getRolePresetObjRecordTypes(
                        String.valueOf(fsEi), appId, role.getRoleId(), objectApiName));
                roleList.add(roleAssociationRecordTypePojo);
            }

            allocatedRecordTypePojo.setRoleList(roleList);
        }
        return allocatedRecordTypePojo;
    }


    public com.facishare.enterprise.common.result.Result<ObjectConfigVO> getInlayObjectConfigInfo(String appId, String objectApiName) {
        List<LinkAppObjectAssociationEntity> associationList = linkAppObjectAssociationDao.listByTypeAndUpstreamAndAppId(LinkAppObjectAssociationType.ASSOCIATION_TYPE, null, appId, objectApiName, false);
        ObjectConfigVO objectConfigVO = null;
        if (CollectionUtils.isNotEmpty(associationList)) {
            objectConfigVO = new ObjectConfigVO();
            LinkAppObjectAssociationEntity linkAppObjectAssociation = associationList.get(0);

            objectConfigVO.setObjectApiName(linkAppObjectAssociation.getObjectApiName());
            objectConfigVO.setNeedAllocate(linkAppObjectAssociation.getNeedAllocate());
            if (CollectionUtils.isNotEmpty(linkAppObjectAssociation.getNotRecordTypeNames())) {
                objectConfigVO.setNoNeedRecordTypeNames(linkAppObjectAssociation.getNotRecordTypeNames());
            }
            if (CollectionUtils.isNotEmpty(linkAppObjectAssociation.getNotLayoutNames())) {
                objectConfigVO.setNoNeedLayoutnames(linkAppObjectAssociation.getNotLayoutNames());
            }
        }
        return com.facishare.enterprise.common.result.Result.newSuccess(objectConfigVO);
    }

    public List<OuterRoleVo> listLinkAppRoles(Integer upEI, String linkAppId) {
        //获取应用角色
        List<RoleInfo> appRoleList = remoteCrossService.getOuterRoleInfosByAppId(upEI, linkAppId);
        if (CollectionUtils.isEmpty(appRoleList)) {
            return new ArrayList<>();
        }
        appRoleList = appRoleList.stream().filter(x -> !("allOuterRole").equals(x.getId())
                        && !("未知").equals(x.getName())  // ignoreI18n
                //&& !("企业用户").equals(x.getName())

        ).collect(Collectors.toList());

        List<String> roleIdList = appRoleList.stream().map(RoleInfo::getId).collect(Collectors.toList());

        List<OuterRoleVo> paasRoleList = crmPassCacheManager.listOuterRoles(upEI, roleIdList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(paasRoleList)) {
            paasRoleList = paasRoleList.stream().filter(x -> !("allOuterRole").equals(x.getRoleId())
                            && !("未知").equals(x.getRoleName()) // ignoreI18n
                    //&& !("企业用户").equals(x.getRoleName())

            ).collect(Collectors.toList());
        }
        //组合信息
        return mergeRoles(appRoleList, paasRoleList);
    }

    private static List<OuterRoleVo> mergeRoles(List<RoleInfo> appRoleList, List<OuterRoleVo> paasRoleList) {
        Map<String, OuterRoleVo> paasRoleMap = new HashedMap();
        for (OuterRoleVo outerRoleVo : paasRoleList) {
            paasRoleMap.put(outerRoleVo.getRoleId(), outerRoleVo);
        }
        List<OuterRoleVo> result = new ArrayList<>();
        appRoleList.forEach(appRole -> {
            OuterRoleVo paasRole = paasRoleMap.get(appRole.getId());
            OuterRoleVo role = new OuterRoleVo();
            role.setRoleId(appRole.getId());
            role.setRoleName(appRole.getName());
            if (Objects.nonNull(paasRole)) {
                role.setRoleType(paasRole.getRoleType());
                role.setRoleName(paasRole.getRoleName());
                role.setDescription(paasRole.getDescription());
            }
            if (GuestorIdentityInfoConstants.ROLE_CODE.equals(appRole.getId())) {
                role.setIsDeletable(false);
                role.setIsAssignable(false);
            }
            if (GlobalRoleConstants.ENTERPRISE_ROLE_CODE.equals(appRole.getId())) {
                role.setRoleName(GlobalRoleConstants.ENTERPRISE_ROLE_NAME);
                role.setDescription(GlobalRoleConstants.ENTERPRISE_ROLE_DESC);
                role.setRoleType(AppRoleType.OUTER_CUSTOM_ROLE.getType());
            }
            if (GlobalRoleConstants.PERSONAL_ROLE_CODE.equals(appRole.getId())) {
                role.setRoleName(GlobalRoleConstants.PERSONAL_ROLE_NAME);
                role.setDescription(GlobalRoleConstants.PERSONAL_ROLE_DESC);
                role.setRoleType(AppRoleType.OUTER_CUSTOM_ROLE.getType());
            }
            result.add(role);
        });
        return result;
    }

    public void allocateRecordType(Integer fsEi, String fsEa, Integer employeeId, String appId, String
            objectApiName, List<AddRoleRecordTypeVO> allocateList, boolean isManual) {
        if (org.springframework.util.CollectionUtils.isEmpty(allocateList)) {
            return;
        }
        if (!isManual) {
            AllocatedRecordTypePojo allocatedRecordTypePojo = listAllocateRecordTypes(fsEi, fsEa, employeeId, appId, objectApiName, 1);
            Map<String, List<String>> rolePreRecordsMap = allocatedRecordTypePojo.getRoleList().stream()
                    .filter(entry -> entry.getRoleCode() != null && entry.getReadOnlyRecordTypes() != null)
                    .collect(Collectors.toMap(RoleAssociationRecordTypePojo::getRoleCode, RoleAssociationRecordTypePojo::getReadOnlyRecordTypes));
            Map<String, List<String>> roleOpenRecordsMap = allocatedRecordTypePojo.getRoleList().stream()
                    .filter(entry -> entry.getRoleCode() != null && entry.getRecordTypeApiNames() != null)
                    .collect(Collectors.toMap(RoleAssociationRecordTypePojo::getRoleCode, RoleAssociationRecordTypePojo::getRecordTypeApiNames));
            for (AddRoleRecordTypeVO addRoleRecordTypeVO : allocateList) {
                String roleCode = addRoleRecordTypeVO.getRoleCode();
                List<String> preRecords = rolePreRecordsMap.getOrDefault(roleCode, Lists.newArrayList());
                addRoleRecordTypeVO.getRecordTypeApiNames().removeAll(preRecords); // 预置不可编辑的
                List<String> presetOpenRecords = roleOpenRecordsMap.getOrDefault(roleCode, Lists.newArrayList()).stream()
                        .filter(preRecords::contains).collect(Collectors.toList()); // 获取不可编辑类型原来的状态
                addRoleRecordTypeVO.getRecordTypeApiNames().addAll(presetOpenRecords);
            }
        }
        List<String> toDelRoleCodes = Lists.newArrayList();
        List<RecordTypeData> roleRecordTypes = Lists.newArrayList();

        List<String> roleList = allocateList.stream().map(AddRoleRecordTypeVO::getRoleCode).collect(Collectors.toList());
        Map<String, Set<String>> roleFunction = functionService.queryFuncAccessByRolesAndEntity(fsEi, employeeId, objectApiName, Sets.newHashSet(roleList));

        List<String> errorRoleCodes = Lists.newArrayList();
        allocateList.forEach(addVO -> {
            Set<String> functionCodes = roleFunction.get(addVO.getRoleCode());
            if (CollectionUtils.isEmpty(addVO.getRecordTypeApiNames())
                    && CollectionUtils.isNotEmpty(functionCodes)
                    && CollectionUtils.containsAny(functionCodes, FUNCTION_CODES)) {
                errorRoleCodes.add(addVO.getRoleCode());
            }
            if (org.springframework.util.CollectionUtils.isEmpty(addVO.getRecordTypeApiNames())) {
                toDelRoleCodes.add(addVO.getRoleCode());
                return;
            }
            for (String recordTypeApiName : addVO.getRecordTypeApiNames()) {
                RecordTypeData recordTypeData = new RecordTypeData();
                recordTypeData.setRecordTypeId(recordTypeApiName);
                recordTypeData.setRoleCode(addVO.getRoleCode());
                roleRecordTypes.add(recordTypeData);
            }
        });
        //判断如果有角色有权限，但是没有分配，弹异常
        if (CollectionUtils.isNotEmpty(errorRoleCodes)) {
            List<OuterRoleVo> outerRoleVos = listLinkAppRoles(fsEi, appId);
            List<String> errorRoleNames = outerRoleVos.stream().filter(x -> errorRoleCodes.contains(x.getRoleId())).map(OuterRoleVo::getRoleName).collect(Collectors.toList());
            log.error("allocateRecordType error: tenantId:{},userId:{},errorRoleNames:{}", fsEi, employeeId, errorRoleCodes);
            throw new WebPageException(InterErrorCode.HAS_PRIVILEGE_ROLE_MAST_ASSIGNED_RECORD_TYPE,
                    Lists.newArrayList(errorRoleNames.stream().map(String::valueOf).collect(Collectors.joining(","))));
        }

        crmPassCacheManager.updateRoleRecordType(fsEi, objectApiName, roleRecordTypes);

        // 删除个人用户 和 企业用户业务类型
        if (!org.springframework.util.CollectionUtils.isEmpty(toDelRoleCodes)) {
            HeaderObj headerObj = new HeaderObj(fsEi, -10000);
            AppAuthContext appAuthContext = AppAuthContext.builder().tenantId(fsEi + "").userId("-10000").appId("CRM").build();

            BatchDeleteRoleEntityRelationArg batchDeleteRoleEntityRelationArg = new BatchDeleteRoleEntityRelationArg();
            batchDeleteRoleEntityRelationArg.setEntityId(objectApiName);
            batchDeleteRoleEntityRelationArg.setRoleCodes(toDelRoleCodes);
            batchDeleteRoleEntityRelationArg.setAuthContext(appAuthContext);
            recordTypeAuthService.batchDeleteRoleEntityRelation(headerObj, batchDeleteRoleEntityRelationArg);
        }

        AllocateRecordEvent allocateRecordEvent = new AllocateRecordEvent();
        allocateRecordEvent.setTenantId(fsEi);
        allocateRecordEvent.setAppId(appId);
        allocateRecordEvent.setObjectApiName(objectApiName);
        allocateRecordMqProducer.sendEvent(allocateRecordEvent);
    }

    public AllocatedLayoutVO listAllocatedLayoutByObject(String ea, String appId, String objectApiName, String
            layoutType) {

        String listLayoutType = "";
        LayoutTypeEnum layoutTypeEnum = LayoutTypeEnum.getByType(layoutType);
        if (layoutTypeEnum == null) {
            if (LIST_LAYOUT_RECORD.equals(layoutType)) {
                layoutType = LayoutTypeEnum.LIST.getType();
                listLayoutType = LIST_LAYOUT_RECORD;
            } else {
                layoutType = LayoutTypeEnum.DETAIL.getType();
            }
        }

        int ei = eieaConverter.enterpriseAccountToId(ea);
        AllocatedLayoutVO allocatedLayoutVO = new AllocatedLayoutVO();

        //查询应用不允许使用的布局、业务类型列表
        com.facishare.enterprise.common.result.Result<ObjectConfigVO> objectInfoRet = getInlayObjectConfigInfo(appId, objectApiName);
        if (objectInfoRet.isSuccess() && objectInfoRet.getData() != null) {
            if (BooleanUtils.isFalse(objectInfoRet.getData().getNeedAllocate())) {
                throw new WebPageException(InterErrorCode.NOT_ALLOWED_ALLOCATE);
            }
        }
        //获取布局、业务类型列表并且过滤不允许的数据
        if (objectInfoRet.isSuccess()) {
            fillObjectRecordTypesAndLayouts(ea, objectApiName, objectInfoRet.getData(), allocatedLayoutVO, layoutType, listLayoutType);
        }

        //填充已经分配的布局数据
        List<OuterRoleVo> roles = listLinkAppRoles(ei, appId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(roles)) {
            List<String> roleIds = new ArrayList<>(roles.stream().map(OuterRoleVo::getRoleId).collect(Collectors.toSet()));
            List<String> typeApiNames = allocatedLayoutVO.getRecordTypeList().stream().map(SimpleRecordTypeVO::getApi_name).collect(Collectors.toList());
            List<RoleViewData> roleViewDataList = crmPassCacheManager.batchQueryView(appId, ei, roleIds, objectApiName, Lists.newArrayList(layoutType), typeApiNames);
            List<SimpleRoleViewVO> allocatedViews = BeanUtil.deepCopyList(roleViewDataList, SimpleRoleViewVO.class);
            List<RoleAssociationLayoutVO> roleList = generalAllocateRoleViews(allocatedViews, roles);
            allocatedLayoutVO.setRoleList(roleList);
        }
        return allocatedLayoutVO;
    }


    public void fillObjectRecordTypesAndLayouts(String ea, String objectApiName, ObjectConfigVO
            objectConfigVO, AllocatedLayoutVO allocatedLayoutVO, String layoutType, String listLayout) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        //获取布局、业务类型列表
        List<SimpleRecordTypeVO> recordTypes = null;
        if (LayoutTypeEnum.LIST.getType().equals(layoutType) && StringUtils.isBlank(listLayout)) {
            //列表页布局不区分业务类型，使用北京提供的默认业务类型apiname
            SimpleRecordTypeVO simpleRecordTypeVO = new SimpleRecordTypeVO();
            simpleRecordTypeVO.setApi_name("default_List_layout_record_type");
            simpleRecordTypeVO.setLabel(I18N.text(I18NKey.TRANSLATE_LIST_PAGE));
            recordTypes = Lists.newArrayList(simpleRecordTypeVO);
        } else {
            recordTypes = crmPassCacheManager.listRecordTypes(ea, objectApiName);
        }

        List<SimpleObjectLayoutVO> layouts = crmPassCacheManager.listLayoutsByLayoutType(ei, objectApiName, layoutType);
        //过滤不允许的数据
        if (objectConfigVO != null) {
            List<String> noAllowedRecordTypes = Lists.newArrayList();
            List<String> noAllowedLayouts = Lists.newArrayList();
            if (!org.springframework.util.CollectionUtils.isEmpty(objectConfigVO.getNoNeedLayoutnames())) {
                noAllowedLayouts.addAll(objectConfigVO.getNoNeedLayoutnames());
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(objectConfigVO.getNoNeedRecordTypeNames())) {
                noAllowedRecordTypes.addAll(objectConfigVO.getNoNeedRecordTypeNames());
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(recordTypes)) {
                recordTypes = recordTypes.stream().filter(x -> !noAllowedRecordTypes.contains(x.getApi_name())).collect(Collectors.toList());
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(layouts)) {
                layouts = layouts.stream().filter(y -> !noAllowedLayouts.contains(y.getApi_name())).collect(Collectors.toList());
            }
            if (org.springframework.util.CollectionUtils.isEmpty(layouts) || org.springframework.util.CollectionUtils.isEmpty(recordTypes)) {
                throw new WebPageException(InterErrorCode.NOT_ALLOWED_ALLOCATE);
            }
        }

        allocatedLayoutVO.setLayoutList(layouts);
        allocatedLayoutVO.setRecordTypeList(recordTypes);
    }

    public List<RoleAssociationLayoutVO> generalAllocateRoleViews
            (List<SimpleRoleViewVO> allocatedViews, List<OuterRoleVo> roles) {
        Map<String, List<RoleAssociationLayoutVO.RecordTypeAssociationLayout>> roleViewMap = Maps.newHashMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(allocatedViews)) {
            allocatedViews.forEach(view -> {
                List<RoleAssociationLayoutVO.RecordTypeAssociationLayout> roleViewList = roleViewMap.get(view.getRoleCode());

                if (roleViewList == null) {
                    roleViewList = Lists.newArrayList();
                    roleViewMap.put(view.getRoleCode(), roleViewList);
                }

                RoleAssociationLayoutVO.RecordTypeAssociationLayout recordViewVo = new RoleAssociationLayoutVO.RecordTypeAssociationLayout();
                recordViewVo.setLayoutApiName(view.getViewId());
                recordViewVo.setRecordTypeApiName(view.getRecordTypeId());

                roleViewList.add(recordViewVo);
            });
        }

        List<RoleAssociationLayoutVO> roleList = Lists.newArrayList();
        Set<String> distinctRoleIds = new HashSet<>();
        for (OuterRoleVo role : roles) {
            if (distinctRoleIds.contains(role.getRoleId())) {
                continue;
            }
            RoleAssociationLayoutVO roleAssociationLayoutVO = new RoleAssociationLayoutVO();

            roleAssociationLayoutVO.setRoleCode(role.getRoleId());
            roleAssociationLayoutVO.setRoleName(role.getRoleName());
            roleAssociationLayoutVO.setRecordTypeAssociationLayoutList(roleViewMap.get(role.getRoleId()));
            roleList.add(roleAssociationLayoutVO);
            distinctRoleIds.add(role.getRoleId());
        }

        return roleList;
    }

    public void allocateLayout(Integer fsEi, String appId, String
            objectApiName, List<AddRoleLayoutVO> allocateList, String layoutType) {
        if (org.springframework.util.CollectionUtils.isEmpty(allocateList)) {
            return;
        }

        LayoutTypeEnum layoutTypeEnum = LayoutTypeEnum.getByType(layoutType);
        if (layoutTypeEnum == null) {
            if (LIST_LAYOUT_RECORD.equals(layoutType)) {
                layoutType = LayoutTypeEnum.LIST.getType();
            } else {
                layoutType = LayoutTypeEnum.DETAIL.getType();
            }
        }
        String finalLayoutType = layoutType;
        // ”新建编辑布局“保存
        if (crmPassCacheManager.getCreateUpdateLayoutStatus(objectApiName, fsEi)) {
            List<RoleViewPojo> roleViewPojos = new ArrayList<>();
            allocateList.forEach(addVO -> {
                RoleViewPojo roleViewPojo = new RoleViewPojo();
                roleViewPojo.setRoleCode(addVO.getRoleCode());
                roleViewPojo.setEntityId(objectApiName);
                roleViewPojo.setViewId(addVO.getLayoutApiName());
                roleViewPojo.setRecordTypeId(addVO.getRecordTypeApiName());
                roleViewPojo.setViewType(finalLayoutType);
                roleViewPojos.add(roleViewPojo);
            });
            crmPassCacheManager.addViewAccess(appId, fsEi, roleViewPojos);
        } else if (LayoutTypeEnum.ABSTRACT_LIST.getType().equalsIgnoreCase(layoutType)) {
            // 移动端摘要布局保存
            addViewAccess(fsEi, appId, objectApiName, allocateList, finalLayoutType);
        } else {
            List<RoleViewData> roleViewDatas = Lists.newArrayList();
            allocateList.forEach(addVO -> {
                RoleViewData roleViewData = new RoleViewData();
                roleViewData.setEntityId(objectApiName);
                roleViewData.setRecordTypeId(addVO.getRecordTypeApiName());
                roleViewData.setRoleCode(addVO.getRoleCode());
                roleViewData.setViewId(addVO.getLayoutApiName());
                roleViewData.setViewType(finalLayoutType);
                roleViewDatas.add(roleViewData);
            });
            crmPassCacheManager.addRoleView(appId, fsEi, roleViewDatas);
        }
    }

    private void addViewAccess(Integer fsEi, String appId, String
            objectApiName, List<AddRoleLayoutVO> allocateList, String finalLayoutType) {
        List<RoleViewPojo> roleViewPojoList = new ArrayList<>();
        allocateList.forEach(addVO -> {
            RoleViewPojo roleViewPojo = new RoleViewPojo();
            roleViewPojo.setRoleCode(addVO.getRoleCode());
            roleViewPojo.setEntityId(objectApiName);
            roleViewPojo.setViewId(addVO.getLayoutApiName());
            roleViewPojo.setRecordTypeId(addVO.getRecordTypeApiName());
            roleViewPojo.setViewType(finalLayoutType);
            roleViewPojoList.add(roleViewPojo);
        });
        crmPassCacheManager.addViewAccess(appId, fsEi, roleViewPojoList);
    }

    @Override
    public List<LinkAppObjectAssociationVO> deleteAssociationObject(String upstream, String appId, String
            objectApiName) {
        return deleteAssociationObject(upstream, appId, objectApiName, LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE);
    }

    @Override
    public List<LinkAppObjectAssociationVO> deleteAssociationObject(String upstream, String appId,
                                                                    String objectApiName, Integer type) {
        type = type == null ? LinkAppObjectAssociationType.UP_EA_ASSOCIATION_TYPE : type;
        linkAppObjectAssociationDao.deleteByUpstreamAndAppId(type, upstream, appId, objectApiName);
        deleteByPolicysAndApiName(upstream, appId, objectApiName);
        return listByTypeAndUpstreamAndAppId(null, upstream, appId, false);
    }

    @Override
    public void insertLinkAppObjectAssociationList(List<LinkAppObjectAssociationVO> linkAppObjectAssociationVOS) {
        List<LinkAppObjectAssociationEntity> linkAppObjectAssociationEntities = convertVOS2Entitys(linkAppObjectAssociationVOS);
        linkAppObjectAssociationDao.batchInsert(linkAppObjectAssociationEntities);
    }

    @Override
    public List<LinkAppObjectAssociationVO> getLinkAppObjectAssociationList(GetLinkAppAssociationObjectListArg arg) {
        List<LinkAppObjectAssociationEntity> entities = linkAppObjectAssociationDao.getLinkAppObjectAssociationList(arg);
        List<LinkAppObjectAssociationVO> VOS = convertEntitys2VOS(entities);
        return VOS;
    }

    @Override
    public void deleteLinkAppObjectAssociationList(DeleteLinkAppAssociationObjectListArg arg) {
        linkAppObjectAssociationDao.deleteLinkAppObjectAssociationList(arg);
    }

    @Override
    public void batchUpdateStatusByTypeAndUpstreamAndApiNames(UpdateLinkAppAssociationObjectListArg arg) {
        linkAppObjectAssociationDao.batchUpdateStatusByTypeAndUpstreamAndApiNames(arg.getObjectStatus(), arg.getType(), arg.getUpstreamEa(), arg.getObjectApiNames());

    }

    private List<LinkAppObjectAssociationVO> convertEntitys2VOS(List<LinkAppObjectAssociationEntity> entities) {
        List<LinkAppObjectAssociationVO> VOS = Lists.newArrayList();
        entities.forEach(item -> {
            LinkAppObjectAssociationVO VO = new LinkAppObjectAssociationVO();
            try {
                org.springframework.beans.BeanUtils.copyProperties(item, VO);
                VOS.add(VO);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return VOS;
    }

    private List<LinkAppObjectAssociationEntity> convertVOS2Entitys
            (List<LinkAppObjectAssociationVO> linkAppObjectAssociationVOS) {
        List<LinkAppObjectAssociationEntity> linkAppObjectAssociationEntities = Lists.newArrayList();
        linkAppObjectAssociationVOS.forEach(item -> {
            LinkAppObjectAssociationEntity linkAppObjectAssociationEntity = new LinkAppObjectAssociationEntity();
            try {
                org.springframework.beans.BeanUtils.copyProperties(item, linkAppObjectAssociationEntity);
                linkAppObjectAssociationEntities.add(linkAppObjectAssociationEntity);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return linkAppObjectAssociationEntities;
    }

    public <T> T postUrl(Request request, TypeReference<T> typeReference) {
        T result = webPageHttpClient.parseObject(request, typeReference);

        return result;
    }

    public void deleteByPolicysAndApiName(String upstreamEa, String appId, String objectApiName) {
        ServiceInvokeArg serviceInvokeArg = new ServiceInvokeArg();
        List<String> list = Lists.newArrayList(upstreamEa, appId, objectApiName);
        serviceInvokeArg.setArgsJson(list);
        RequestBody body = RequestBody.create(JSON_TYPE, JSON.toJSONString(serviceInvokeArg));
        Request request = new Request.Builder().url(defaultTenantConfig.getSynLinkAppDataIP() + "/fs-enterprise-linkapp-web/linkAppService/dispatch/com.facishare.linkapp.api.service.LinkAppAssociationObjectService_deleteByPolicysAndApiName").post(body).build();
        Result<String> result = null;
        try {
            result = postUrl(request, new TypeReference<Result<String>>() {
            });
        } catch (Exception e) {
            log.error("deleteByPolicysAndApiName error");
        }
    }

    public void sendLinkAppObjectAssociationInnerEvent(String upstreamEa, String appId, String objectApiName) {
        ServiceInvokeArg serviceInvokeArg = new ServiceInvokeArg();
        List<String> list = Lists.newArrayList(upstreamEa, appId, objectApiName);
        serviceInvokeArg.setArgsJson(list);
        RequestBody body = RequestBody.create(JSON_TYPE, JSON.toJSONString(serviceInvokeArg));
        Request request = new Request.Builder().url(defaultTenantConfig.getSynLinkAppDataIP() + "/fs-enterprise-linkapp-web/linkAppService/dispatch/com.facishare.linkapp.api.service.LinkAppAssociationObjectService_sendLinkAppObjectAssociationInnerEvent").post(body).build();
        Result<String> result = null;
        try {
            result = postUrl(request, new TypeReference<Result<String>>() {
            });
        } catch (Exception e) {
            log.error("sendLinkAppObjectAssociationInnerEvent error");
        }
    }
}
