package com.facishare.webpage.customer.metadata;

import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.core.Menu;
import com.facishare.webpage.customer.api.model.core.SimpObjectDescription;

import java.util.List;

/**
 * Created by zhangyu on 2020/11/3
 */
public interface MetaMenuFilterService {

    List<SimpObjectDescription> filterNoSupportCrmMenu(DataSourceEnv env, int tenantId, String appId, List<SimpObjectDescription> simpObjectDescriptionList, Integer linkType);

    List<Menu> filterNoSupportMenu(DataSourceEnv env, int tenantId, String appId, List<Menu> menuList);


}
