package com.facishare.webpage.customer.drop.model;

import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.designer.model.StandGetDropList;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Yu
 */
public interface GetHomePageDropList {

    @Data
    @ToString(callSuper = true)
    @SuperBuilder(toBuilder = true)
    class Arg extends StandGetDropList.Arg {

        private Integer bizType;
        private String linkAppId;
        private String describeApiName;
        private boolean previewNewCrmFlag;


    }

    @Data
    @Builder
    class Result implements Serializable {
        private List<DropListItem> webDropListItemList;
        private List<DropListItem> appDropListItemList;
    }

}
