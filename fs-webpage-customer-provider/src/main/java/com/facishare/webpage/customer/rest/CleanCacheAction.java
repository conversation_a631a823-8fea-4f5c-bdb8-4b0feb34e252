package com.facishare.webpage.customer.rest;

import com.facishare.webpage.customer.remote.CrossAppCacheService;
import com.facishare.webpage.customer.remote.InnerAppCacheService;
import com.facishare.webpage.customer.remote.ObjectService;
import com.facishare.webpage.customer.rest.arg.CleanCacheArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/clean")
public class CleanCacheAction {

    @Resource
    private InnerAppCacheService innerAppCacheService;
    @Resource
    private CrossAppCacheService crossAppCacheService;
    @Resource
    private ObjectService objectService;


    @RequestMapping(value = "/cleanInnerAppCache", method = RequestMethod.POST)
    public String cleanInnerAppCache(@RequestHeader("taskSecret") String taskSecret, @RequestBody CleanCacheArg arg) {
        if (StringUtils.isEmpty(taskSecret) || !"zhang".equals(taskSecret)) {
            return "fail";
        }
        arg.getTenantIds().stream().forEach(x -> {
            innerAppCacheService.removeCacheByEnterpriseId(x);
        });
        return "success";
    }

    @RequestMapping(value = "/cleanCrossAppCache", method = RequestMethod.POST)
    public String cleanCrossAppCache(@RequestHeader("taskSecret") String taskSecret, @RequestBody CleanCacheArg arg) {
        if (StringUtils.isEmpty(taskSecret) || !"zhang".equals(taskSecret)) {
            return "fail";
        }
        arg.getTenantIds().stream().forEach(x -> {
            crossAppCacheService.removeCacheByEnterpriseId(x);
        });
        return "success";
    }
    
    @RequestMapping(value = "/cleanObjectCache", method = RequestMethod.POST)
    public String cleanObjectCache(@RequestHeader("taskSecret") String taskSecret, @RequestBody CleanCacheArg arg) {
        if (StringUtils.isEmpty(taskSecret) || !"zhang".equals(taskSecret)) {
            return "fail";
        }
        arg.getTenantIds().stream().forEach(x -> {
            objectService.cleanCache(x);
        });
        return "success";
    }


}
