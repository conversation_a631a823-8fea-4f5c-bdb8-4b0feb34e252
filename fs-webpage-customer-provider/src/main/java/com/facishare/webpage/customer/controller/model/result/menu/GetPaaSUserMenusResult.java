package com.facishare.webpage.customer.controller.model.result.menu;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.controller.model.UserMenuTempleVo;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangyu on 2020/11/24
 */
@Data
public class GetPaaSUserMenusResult implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("menus")
    private List<UserMenuTempleVo> userMenuTempleVos = Lists.newLinkedList();
    @JSONField(name = "M2")
    @SerializedName("configinfo")
    private Map<String,Object> configInfo;

}
