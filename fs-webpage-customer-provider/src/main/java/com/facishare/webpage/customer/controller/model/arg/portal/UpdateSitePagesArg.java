package com.facishare.webpage.customer.controller.model.arg.portal;

import com.alibaba.fastjson.JSONArray;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.ClientType;
import com.facishare.webpage.customer.api.constant.ErrorMessageI18NKey;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.controller.model.I18nInfoDTO;
import com.facishare.webpage.customer.config.HomePageMaxConfig;
import com.facishare.webpage.customer.dao.entity.SiteLang;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2024/11/6.
 */
@Data
public class UpdateSitePagesArg {
    private String siteApiName;
    private Long version;
    private List<ThemeLayoutDTO> themeLayoutList;
    private List<SiteMenuDTO> menuList;
    private List<SitePageDTO> pageList;
    private List<I18nInfoDTO> i18nInfoList;
    private List<SiteLang> langList;
    private Boolean isPublish;
    private String clientType = ClientType.web.getValue();
    private List<ThemeStyleDTO> themeStyleList;
    private SiteConfigDTO siteConfig;

    /**
     * 文件信息列表，用于维护文件引用关系
     */
    private List<FileInfoDTO> fileInfoList;

    public void validAndFix(String tenantId) {
        clientType = StringUtils.firstNonBlank(clientType, ClientType.web.getValue());
        if (StringUtils.isBlank(siteApiName)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if (CollectionUtils.isEmpty(pageList)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if (pageList.size() > HomePageMaxConfig.getMaxSitePageCount(tenantId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        int maxSitePageComponentCount = HomePageMaxConfig.getMaxSitePageComponentCount(tenantId);
        pageList.forEach(x -> {
            if (StringUtils.isBlank(x.getApiName())) {
                throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
            }
            if (StringUtils.isNotBlank(x.getThemeLayoutApiName())) {
                if (CollectionUtils.isEmpty(themeLayoutList)) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
                if (themeLayoutList.stream().noneMatch(y -> Objects.equals(y.getApiName(), x.getThemeLayoutApiName()))) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
            }
            if (Objects.nonNull(x.getLayoutStructure())) {
                JSONArray components = x.getLayoutStructure().getJSONArray("components");
                if (Objects.nonNull(components) && components.size() > maxSitePageComponentCount) {
                    throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_PAGE_OVER_COMPONENTS_LIMIT,
                            StringUtils.firstNonEmpty(x.getName(), x.getApiName()), maxSitePageComponentCount);
                }
            }
        });
        if (CollectionUtils.isNotEmpty(themeLayoutList)) {
            themeLayoutList.forEach(x -> {
                if (StringUtils.isBlank(x.getApiName())) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
                JSONArray components = x.getLayoutStructure().getJSONArray("components");
                if (Objects.nonNull(components) && components.size() > maxSitePageComponentCount) {
                    throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_THEME_LAYOUT_OVER_COMPONENTS_LIMIT,
                            StringUtils.firstNonEmpty(x.getName(), x.getApiName()), maxSitePageComponentCount);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(menuList)) {
            menuList.forEach(x -> {
                if (StringUtils.isBlank(x.getApiName())) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(themeStyleList)) {
            themeStyleList.forEach(x -> {
                if (StringUtils.isBlank(x.getApiName())) {
                    throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
                }
            });
        }
    }
}
