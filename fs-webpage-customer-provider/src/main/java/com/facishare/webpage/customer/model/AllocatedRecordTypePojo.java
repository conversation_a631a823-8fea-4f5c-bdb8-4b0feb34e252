package com.facishare.webpage.customer.model;

import com.facishare.enterprise.common.model.paas.SimpleRecordTypeVO;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AllocatedRecordTypePojo {

    List<SimpleRecordTypeVO> recordTypeList = Lists.newArrayList();
    List<RoleAssociationRecordTypePojo> roleList = Lists.newArrayList();
}
