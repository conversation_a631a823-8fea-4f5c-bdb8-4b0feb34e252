package com.facishare.webpage.customer.service;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.model.UtilityBarVO;

import java.util.Locale;

/**
 * <AUTHOR>
 */
public interface UtilityBarService {

    /**
     * insert or update utilityBar
     *
     * @param tenantId
     * @param employeeId
     * @param utilityBarVO
     * @param clientInfo
     */
    void insertOrUpdateUtilityBar(int tenantId, int employeeId, UtilityBarVO utilityBarVO, ClientInfo clientInfo);

    /**
     * get utility bar with manager
     *
     *
     * @param userInfo
     * @param appId
     * @param appType
     * @param pageTemplateId
     * @param locale
     * @return
     */
    UtilityBarVO queryUtilityBarForManager(UserInfo userInfo, String appId, int appType, String pageTemplateId, Locale locale);

    /**
     * get utility bar with user
     *
     *
     * @param userInfo
     * @param outerUserInfo
     * @param appId
     * @param appType
     * @param pageTemplateId
     * @param locale
     * @return
     */
    UtilityBarVO queryUtilityBarForUser(UserInfo userInfo, OuterUserInfo outerUserInfo, String appId, int appType, String pageTemplateId, Locale locale);

}
