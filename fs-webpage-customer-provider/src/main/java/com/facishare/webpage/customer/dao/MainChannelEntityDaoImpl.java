package com.facishare.webpage.customer.dao;

import com.facishare.webpage.customer.dao.entity.MainChannelEntity;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository("mainChannelEntityDao")
public class MainChannelEntityDaoImpl implements MainChannelEntityDao {

    @Resource
    private Datastore datastore;

    public void setDatastore(Datastore datastore) {
        this.datastore = datastore;
    }


    @Override
    public MainChannelEntity getMainChannelEntity(int tenantId, int employeeId) {
        Query<MainChannelEntity> query = datastore.createQuery(MainChannelEntity.class);
        query.field("tenantId").equal(tenantId);
        query.field("effectiveEmployeeIds").hasThisOne(employeeId);
        return query.get();
    }

    @Override
    public MainChannelEntity findAndModify(MainChannelEntity channelEntity) {
        Query<MainChannelEntity> query = datastore.createQuery(MainChannelEntity.class);
        query.field("mainChannelId").equal(channelEntity.getMainChannelId());
        UpdateOperations<MainChannelEntity> updateOperations = datastore.createUpdateOperations(MainChannelEntity.class);
        updateOperations.setOnInsert("mainChannelId", channelEntity.getMainChannelId());
        updateOperations.setOnInsert("tenantId", channelEntity.getTenantId());
        updateOperations.setOnInsert("creatorId", channelEntity.getCreatorId());
        updateOperations.setOnInsert("createTime", channelEntity.getCreateTime());
        updateOperations.set("effectiveEmployeeIds", channelEntity.getEffectiveEmployeeIds());
        updateOperations.set("mainChannelMenus", channelEntity.getMainChannelMenus());
        updateOperations.set("status", channelEntity.getStatus());
        updateOperations.set("sourceType", channelEntity.getSourceType());
        updateOperations.set("updateId", channelEntity.getUpdateId());
        updateOperations.set("updateTime", channelEntity.getUpdateTime());
        return datastore.findAndModify(query, updateOperations, false, true);
    }

    @Override
    public void removeEmployeeIds(String mainChannelId, List<Integer> employeeIds) {
        Query<MainChannelEntity> query = datastore.createQuery(MainChannelEntity.class);
        query.field("mainChannelId").equal(mainChannelId);
        UpdateOperations<MainChannelEntity> updateOperations = datastore.createUpdateOperations(MainChannelEntity.class);
        updateOperations.removeAll("effectiveEmployeeIds", employeeIds);
        datastore.findAndModify(query, updateOperations, false, false);
    }

    @Override
    public List<MainChannelEntity> getMainChannelEntityByTenantId(int tenantId) {
        Query<MainChannelEntity> query = datastore.createQuery(MainChannelEntity.class);
        query.field("tenantId").equal(tenantId);

        return query.asList();
    }
}
