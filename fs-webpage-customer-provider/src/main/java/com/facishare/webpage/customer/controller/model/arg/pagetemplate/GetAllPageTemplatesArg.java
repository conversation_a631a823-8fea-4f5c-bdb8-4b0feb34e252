package com.facishare.webpage.customer.controller.model.arg.pagetemplate;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.Scope;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
public class GetAllPageTemplatesArg {
    private int applyType;

    private String appType;



    public void valid() {
        if (StringUtils.isEmpty(appType)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }
}
