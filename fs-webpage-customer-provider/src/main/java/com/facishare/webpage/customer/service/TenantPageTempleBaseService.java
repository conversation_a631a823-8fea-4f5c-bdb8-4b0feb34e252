package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.PageTemplate;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.dao.entity.PageTempleEntity;

import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2019/9/19
 */
public interface TenantPageTempleBaseService {

    List<PageTemplate> getCustomerPageTemples(int tenantId, String appId, String type);

    List<PageTemplate> getCustomerPageTemples(int tenantId, String appId, String type, Locale locale);

    List<PageTemplate> getPageTemples(int tenantId, String type, List<String> appIds, Locale locale);

    PageTemplate getPageTemplateById(int tenantId, String templeId);

    PageTemplate getPageTemplateBySynFromWebTemplateId(String templeId);

    PageTemplate updatePageTempleStatus(String templeId, int tenantId, int employeeId, int status);

    PageTemplate updatePageTempleHasBeenSynToApp(String templeId, int tenantId, int employeeId, boolean hasBeenSynToApp, String appTempleId);


    PageTemplate findAndModifyPageTemplate(int tenantId, PageTemplate template, Locale locale);

    List<PageTempleEntity> copyPageTemplates(int fromTenantId, int toTenantId);

    List<PageTemplate> queryPageTemplesByVendor(int tenantId, String appId, List<String> outerRoleIds, String type);

    List<PageTemplate> queryPageTempleIdsByAppIds(int tenantId, List<String> appIds, String type);

    //TODO
    List<PageTemplate> queryPageTempleIdsByScope(int tenantId, List<Scope> scopeList, String type);

}
