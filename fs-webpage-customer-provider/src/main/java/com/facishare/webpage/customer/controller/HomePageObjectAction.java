package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.arg.GetEmployeeConfigValueByApiNameArg;
import com.facishare.webpage.customer.api.model.arg.SetEmployeeConfigValueByApiNameArg;
import com.facishare.webpage.customer.api.model.result.GetEmployeeConfigValueByKeysResult;
import com.facishare.webpage.customer.api.model.result.SetEmployeeConfigValueResult;

/**
 * Created by zhangyu on 2019/11/19
 */
public interface HomePageObjectAction {

    GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByApiName(UserInfo userInfo, GetEmployeeConfigValueByApiNameArg arg);

    SetEmployeeConfigValueResult setEmployeeConfigValueByApiName(UserInfo userInfo, SetEmployeeConfigValueByApiNameArg arg);

    GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByApiNameHasNotDefault(UserInfo userInfo, GetEmployeeConfigValueByApiNameArg arg);
}
