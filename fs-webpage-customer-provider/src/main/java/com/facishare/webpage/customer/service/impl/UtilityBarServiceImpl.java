package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.CustomerLayoutField;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.model.I18nTrans;
import com.facishare.webpage.customer.component.CovertCustomerManage;
import com.facishare.webpage.customer.config.MainChannelMenuIconConfig;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.dao.UtilityBarDao;
import com.facishare.webpage.customer.dao.entity.UtilityBarEntity;
import com.facishare.webpage.customer.filter.FilterLayoutComponentService;
import com.facishare.webpage.customer.model.UtilityBarVO;
import com.facishare.webpage.customer.model.component.UtilityBarLayoutHelper;
import com.facishare.webpage.customer.service.UtilityBarService;
import com.facishare.webpage.customer.util.UtilityBarUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 */
@Component
public class UtilityBarServiceImpl implements UtilityBarService {

    @Resource
    private UtilityBarDao utilityBarDao;
    @Resource
    private FilterLayoutComponentService filterLayoutComponentService;
    @Resource
    private MainChannelMenuIconConfig mainChannelMenuIconConfig;
    @Resource
    private CovertCustomerManage covertCustomerManage;
    @Resource
    private I18nService i18nService;

    @Override
    public void insertOrUpdateUtilityBar(int tenantId, int employeeId, UtilityBarVO utilityBarVO, ClientInfo clientInfo) {
        UtilityBarEntity utilityBarEntity = UtilityBarUtil.covertEntity(tenantId, employeeId, utilityBarVO);
        UtilityBarEntity utilityBar = utilityBarDao.findAndModify(tenantId, utilityBarEntity);
        syncToolBarToolName(tenantId, Objects.isNull(utilityBar.getId()) ? "" : utilityBar.getId().toString(), utilityBar.getToolComponents(), clientInfo.getLocale());
    }


    private void syncToolBarToolName(int tenantId, String id, String toolComponents, Locale locale) {
        if (StringUtils.isEmpty(toolComponents)) {
            return;
        }
        if (Objects.isNull(locale)) {
            return;
        }
        JSONObject utilityBar = JSONObject.parseObject(toolComponents);
        JSONArray components = utilityBar.getJSONArray("components");
        if (components == null) {
            return;
        }
        List<I18nTrans.TransArg> i18nTrans = Lists.newArrayList();
        for (int i = 0; i < components.size(); i++) {
            JSONObject component = components.getJSONObject(i);
            if (Objects.isNull(component)) {
                continue;
            }
            String newHeader = component.getString("newHeader");
            String apiName = component.getString("api_name");
            if (StringUtils.isEmpty(newHeader) || StringUtils.isEmpty(apiName)) {
                continue;
            }
            List<String> preKeys = Lists.newArrayList();
            String nameI18nKey = component.getString(CustomerLayoutField.nameI18nKey);
            if (StringUtils.isNotBlank(nameI18nKey)) {
                preKeys.add(nameI18nKey);
            }
            i18nTrans.add(TranslateI18nUtils.convertToTransArg(TranslateI18nUtils.getToolBarToolNameKey(id, apiName), preKeys, newHeader));
        }
        i18nService.syncTransValueIncludePreKey(tenantId, i18nTrans, locale.toLanguageTag());

    }

    @Override
    public UtilityBarVO queryUtilityBarForManager(UserInfo userInfo, String appId, int appType, String pageTemplateId, Locale locale) {
        UtilityBarEntity utilityBarEntity = utilityBarDao.queryUtilityBarEntity(
                userInfo.getEnterpriseId(),
                appId,
                appType,
                BizType.getBizTypeValue(appType).getPageTemplateType(),
                pageTemplateId);
        if (utilityBarEntity == null) {
            return null;
        }
        UtilityBarVO utilityBarVO = UtilityBarUtil.covertUtilityBarVO(utilityBarEntity);
        JSONObject utilityBarLayout = filterAndSetLanguageComponents(userInfo,
                null, appType, appId,
                utilityBarEntity.getId(), utilityBarVO.getUtilityBarLayout(),
                locale, true);
        utilityBarVO.setUtilityBarLayout(utilityBarLayout);
        return utilityBarVO;
    }

    @Override
    public UtilityBarVO queryUtilityBarForUser(UserInfo userInfo,
                                               OuterUserInfo outerUserInfo,
                                               String appId,
                                               int appType,
                                               String pageTemplateId,
                                               Locale locale) {
        UtilityBarEntity utilityBarEntity = utilityBarDao.queryUtilityBarEntity(
                userInfo.getEnterpriseId(),
                appId,
                appType,
                BizType.getBizTypeValue(appType).getPageTemplateType(),
                pageTemplateId);
        if (utilityBarEntity == null) {
            return null;
        }
        UtilityBarVO utilityBarVO = UtilityBarUtil.covertUtilityBarVO(utilityBarEntity);
        JSONObject utilityBarLayout = filterAndSetLanguageComponents(userInfo,
                outerUserInfo, appType, appId,
                utilityBarEntity.getId(), utilityBarVO.getUtilityBarLayout(),
                locale, false);
        utilityBarVO.setUtilityBarLayout(utilityBarLayout);
        return utilityBarVO;
    }

    private JSONObject filterAndSetLanguageComponents(UserInfo userInfo,
                                                      OuterUserInfo outerUserInfo,  //
                                                      int appType,
                                                      String appId,
                                                      ObjectId toolBarIdObj, JSONObject utilityBarLayout,
                                                      Locale locale,
                                                      boolean manager) {
        UtilityBarLayoutHelper utilityBarLayoutHelper = new UtilityBarLayoutHelper(utilityBarLayout);
        List<JSONObject> components = utilityBarLayoutHelper.getComponents();
        if (CollectionUtils.isEmpty(components)) {
            return utilityBarLayout;
        }
        String toolBarId = Objects.isNull(toolBarIdObj) ? "" : toolBarIdObj.toString();
        List<I18nTrans.TransArg> transArgList = Lists.newArrayList();
        for (JSONObject component : components) {
            String customKey = TranslateI18nUtils.getToolBarToolNameKey(toolBarId, component.getString(CustomerLayoutField.apiName));
            List<String> preKeyList = Lists.newArrayList();
            String nameI18nKey = component.getString(CustomerLayoutField.nameI18nKey);
            if (StringUtils.isNotEmpty(nameI18nKey)) {
                preKeyList.add(nameI18nKey);
            }
            String value = StringUtils.firstNonBlank(component.getString(CustomerLayoutField.newHeader),
                    component.getString(CustomerLayoutField.header), component.getString(CustomerLayoutField.title));
            transArgList.add(TranslateI18nUtils.convertToTransArg(customKey, preKeyList, value));
        }
        Integer enterpriseId = userInfo.getEnterpriseId();
        Map<String, String> titleMap = i18nService.getTransValueIncludePreKey(enterpriseId, transArgList, locale.toLanguageTag());
        //对components进行过滤  TODO 场景组件丢失应该是这里的问题
        List<JSONObject> newComponents = filterLayoutComponentService.filterLayoutComponents(enterpriseId,
                userInfo.getEmployeeId(),
                outerUserInfo,
                appId,
                components,
                locale,
                manager);
        utilityBarLayoutHelper.setComponent(newComponents, titleMap, toolBarId);

        List<String> apiNames = newComponents.stream().map(x -> x.getString(CustomerLayoutField.apiName)).collect(Collectors.toList());
        utilityBarLayoutHelper.setLayoutStructure(apiNames, mainChannelMenuIconConfig.queryIconMap());

        if (manager) {
            return covertCustomerManage.covertUtilityBarTenantLayout(userInfo, appId, locale, utilityBarLayoutHelper.getUtilityBarLayout());
        }
        return covertCustomerManage.covertUtilityBarUserLayout(userInfo, outerUserInfo, appType, appId, utilityBarLayoutHelper.getUtilityBarLayout(), locale);
    }
}
