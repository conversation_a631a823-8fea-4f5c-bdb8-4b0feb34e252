package com.facishare.webpage.customer.console;

import com.facishare.webpage.customer.api.console.PageTempleService;
import com.facishare.webpage.customer.api.console.arg.QueryPageTempleArg;
import com.facishare.webpage.customer.api.console.result.QueryPageTempleResult;
import com.facishare.webpage.customer.console.util.ConsoleUtil;
import com.facishare.webpage.customer.dao.TenantPageTempleDao;
import com.facishare.webpage.customer.dao.entity.PageTempleEntity;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class PageTempleServiceImpl implements PageTempleService {
    private static final Logger logger = LoggerFactory.getLogger(PageTempleServiceImpl.class);
    @Autowired
    private TenantPageTempleDao tenantPageTempleDao;

    @Override
    public QueryPageTempleResult queryPageTemple(QueryPageTempleArg arg) {
        List<PageTempleEntity> templeList = Lists.newArrayList();
        if(StringUtils.isNotEmpty(arg.getTempleId())){
            PageTempleEntity pageTemple = tenantPageTempleDao.getPageTemplateById(arg.getTempleId(), null);
            if(pageTemple != null){
                templeList.add(pageTemple);
            }
        }else if (arg.getTenantId() > 0 && StringUtils.isNotEmpty(arg.getAppId())){
            templeList = tenantPageTempleDao.getPageTempleForConsole(arg.getTenantId(), arg.getAppId(), arg.getType());
        }else {
            return null;
        }
        QueryPageTempleResult result = ConsoleUtil.getPageTempleResult(templeList);
        logger.info("PageTempleServiceImpl queryPageTemple arg:{}, result:{}", arg, result);
        return result;
    }
}
