package com.facishare.webpage.customer.controller.model.arg.homepage;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.google.common.base.Strings;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyu on 2020/2/13
 */
@Data
public class GetHomePageByApiNameArg implements Serializable {
    @JSONField(name = "M1")
    @SerializedName("apiName")
    private String apiName;

    public void valid() {
        if (Strings.isNullOrEmpty(apiName)) {
            throw new WebPageException(InterErrorCode.HOME_DOES_NOT_EXIST);
        }
    }

}
