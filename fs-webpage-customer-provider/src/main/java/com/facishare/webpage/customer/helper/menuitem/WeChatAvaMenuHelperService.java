package com.facishare.webpage.customer.helper.menuitem;

import com.facishare.webpage.customer.api.constant.CustomMenuType;
import com.facishare.webpage.customer.api.model.core.Icon;
import com.facishare.webpage.customer.core.config.IconPathConfig;
import com.facishare.webpage.customer.metadata.model.CustomerMenuData;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Slf4j
@Service("weChatAvaMenuHelperService")
public class WeChatAvaMenuHelperService implements CustomerMenuHelperService {

    @Resource
    private IconPathConfig iconPathConfig;

    @Override
    public void setCustomerMenu(int tenantId, CustomerMenu customerMenu, Locale locale) {

    }

    @Override
    public void setCustomerMenuContent(int tenantId, List<CustomerMenu> customerMenuList, Locale locale) {
        Icon icon = new Icon();
        icon.setNormal(iconPathConfig.getSvgIconPath("5"));
        icon.setHighlight(iconPathConfig.getSvgIconPath("5"));
        icon.setIcon_1(iconPathConfig.getSvgIconPath("5"));
        icon.setIcon_2(iconPathConfig.getSvgIconPath("5"));
        customerMenuList.forEach(x -> {
            x.setContent(x.getApiName());
            x.setIcon(icon);
        });
    }

    @Override
    public List<MetaMenuData> covertMetaMenuDataList(int tenantId, String appId, Locale locale, List<CustomerMenu> customerMenus) {
        if (CollectionUtils.isEmpty(customerMenus)) {
            return Lists.newArrayList();
        }
        return customerMenus.stream().map(x -> {
            CustomerMenuData customerMenuData = new CustomerMenuData();
            customerMenuData.setCustomerMenu(x);
            customerMenuData.setAppId(appId);
            return customerMenuData;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CustomerMenu> filterCustomerMenuForPermission(int tenantId, String appId, Integer employeeId, Long outTenantId, Long outUid, Integer outLinkType, List<CustomerMenu> customerMenus, Locale locale) {
        setCustomerMenuContent(tenantId, customerMenus, locale);
        return customerMenus.stream().
                filter(x -> StringUtils.isNotEmpty(x.getContent())).
                collect(Collectors.toList());
    }

    @Override
    public Integer getMenuType() {
        return CustomMenuType.WECHATAVA;
    }
}
