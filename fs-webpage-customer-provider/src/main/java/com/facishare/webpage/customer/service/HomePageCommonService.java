package com.facishare.webpage.customer.service;

import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.core.customelayout.Filter;

import java.util.List;

/**
 * Created by zhangyu on 2020/2/13
 */
public interface HomePageCommonService {

    List<String> getScopeList(int tenantId, int employeeId, String appId);

    List<Filter> convertFilter(UserInfo userInfo, OuterUserInfo outerUserInfo, String apiName);

}
