package com.facishare.webpage.customer.common;

import com.facishare.webpage.customer.api.model.Scope;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangyu on 2020/7/2
 */
public interface OrganizationCommonService {

    Map<Integer, Object> getScopeName(int tenantId, String appId, List<Scope> scopeList, Boolean cross);

    List<Scope> queryScopeList(int tenantId, Integer employeeId, Long outTenantId, Long outUserId, String appId);

}
