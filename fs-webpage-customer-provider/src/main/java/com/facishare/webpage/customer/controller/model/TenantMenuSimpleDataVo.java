package com.facishare.webpage.customer.controller.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/14.
 */
@Data
public class TenantMenuSimpleDataVo implements Serializable {
    @SerializedName("api_name")
    private String apiName;
    @SerializedName("appId")
    private String appId;
    @SerializedName("display_name")
    private String displayName;
    @SerializedName("type")
    private String type;
    @SerializedName("number")
    private Integer number;
    @SerializedName("children")
    private List<TenantMenuSimpleDataVo> children;
    @SerializedName("is_hidden")
    private Boolean isHidden;
    @SerializedName("menu_item_id")
    private String menuItemId;
    @SerializedName("menu_id")
    private String menuId;
}
