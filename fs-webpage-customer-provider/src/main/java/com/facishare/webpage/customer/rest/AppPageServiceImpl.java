package com.facishare.webpage.customer.rest;

import com.facishare.cep.plugin.enums.ClientTypeEnum;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.crm.notify.model.stat.StatAccessInfo;
import com.facishare.crm.notify.model.stat.UserStatConfig;
import com.facishare.qixin.converter.QXEIEAConverter;
import com.facishare.webpage.customer.api.constant.TempleType;
import com.facishare.webpage.customer.api.model.AppPageTemplateVO;
import com.facishare.webpage.customer.api.model.PageTemplate;
import com.facishare.webpage.customer.api.model.UserMenuItem;
import com.facishare.webpage.customer.api.model.UserPageTemplate;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.result.*;
import com.facishare.webpage.customer.api.service.AppPageService;
import com.facishare.webpage.customer.config.WebMainChannelConfig;
import com.facishare.webpage.customer.constant.HomePageConstant;
import com.facishare.webpage.customer.controller.HomePageObjectAction;
import com.facishare.webpage.customer.controller.TenantPageTempleAction;
import com.facishare.webpage.customer.controller.UserMenuAction;
import com.facishare.webpage.customer.controller.UserPageTempleAction;
import com.facishare.webpage.customer.controller.model.UserAppPageTemplate;
import com.facishare.webpage.customer.controller.model.UserWebPageTemplate;
import com.facishare.webpage.customer.controller.model.arg.menu.GetUserMenuByAppIdArg;
import com.facishare.webpage.customer.controller.model.arg.menu.GetUserMenuByIdArg;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetAppPageTemplesArg;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetAppVendorTemplateListArg;
import com.facishare.webpage.customer.controller.model.arg.pagetemplate.GetWebVendorTemplateListArg;
import com.facishare.webpage.customer.controller.model.result.menu.GetUserMenuByAppIdResult;
import com.facishare.webpage.customer.controller.model.result.menu.GetUserMenuByIdResult;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.GetAppPageTemplesResult;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.GetAppVendorTemplateListResult;
import com.facishare.webpage.customer.controller.model.result.pagetemplate.GetWebVendorTemplateListResult;
import com.facishare.webpage.customer.service.UserPageTempleService;
import com.facishare.webpage.customer.util.GeneralUtil;
import com.fxiaoke.api.model.CountSource;
import com.fxiaoke.api.model.GetPageTemplate;
import com.fxiaoke.api.service.PageTemplateService;
import com.fxiaoke.api.util.MenuCountSourceConfig;
import com.fxiaoke.enterpriserelation2.result.GetOuterAccountByFsResult;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/webPage/AppPageService")
public class AppPageServiceImpl implements AppPageService {

    @Resource
    private UserPageTempleService userPageTempleService;

    @Resource
    private QXEIEAConverter qxEIEAConverter;

    @Resource
    private UserPageTempleAction userPageTempleAction;

    @Resource
    private TenantPageTempleAction tenantPageTempleAction;

    @Autowired
    private UserMenuAction userMenuAction;

    @Autowired
    private HomePageObjectAction homePageObjectAction;

    @Autowired
    private PageTemplateService pageTemplateService;

    @Resource
    private WebMainChannelConfig webMainChannelConfig;

    @RequestMapping(value = "/getInnerAppPageTemplate", method = RequestMethod.POST)
    @Override
    public GetInnerAppPageTemplateResult getInnerAppPageTemplate(@RequestHeader Map<String, String> headers, @RequestBody GetInnerAppPageTemplateArg arg) {
        List<PageTemplate> templateList = userPageTempleService.getUserPageTemplateList(arg.getEi(),
                arg.getEmployeeId(), arg.getAppId(), TempleType.APP);
        String defaultPageId = userPageTempleService.getUserDefaultTemplateId(arg.getEi(),
                arg.getEmployeeId(), arg.getAppId(), HomePageConstant.app);
        GetInnerAppPageTemplateResult ret = new GetInnerAppPageTemplateResult();
        if (CollectionUtils.isEmpty(templateList)) {
            return ret;
        }
        List<String> appPageIdList = Lists.newArrayList();
        String defaultAppPageId = null;
        for (PageTemplate pageTemplate : templateList) {
            appPageIdList.add(pageTemplate.getAppPageId());
            if (StringUtils.isNotBlank(defaultPageId) && defaultPageId.equals(pageTemplate.getAppPageId())) {
                defaultAppPageId = pageTemplate.getAppPageId();
            }
        }
        ret.setAppPageIdList(appPageIdList);
        ret.setDefaultAppPageId(defaultAppPageId);
        return ret;
    }

    @RequestMapping(value = "/getCrossAppPageTemplate", method = RequestMethod.POST)
    @Override
    public GetCrossAppPageTemplateResult getCrossAppPageTemplate(@RequestHeader Map<String, String> headers, @RequestBody GetCrossAppPageTemplateArg arg) {
        List<UserPageTemplate> userPageTemples = userPageTempleService.getUserPage(
                arg.getOuterEi(), arg.getOuterId(), arg.getUpEi(), arg.getAppId(), TempleType.APP);

        String defaultPageId = userPageTempleService.getUserDefaultTemplateId(arg.getUpEi(),
                arg.getOuterId(), arg.getAppId(), HomePageConstant.app);
        GetCrossAppPageTemplateResult ret = new GetCrossAppPageTemplateResult();
        if (CollectionUtils.isEmpty(userPageTemples)) {
            return ret;
        }
        List<String> appPageIdList = Lists.newArrayList();
        String defaultAppPageId = null;
        for (UserPageTemplate pageTemplate : userPageTemples) {
            appPageIdList.add(pageTemplate.getAppPageId());
            if (StringUtils.isNotBlank(defaultPageId) && defaultPageId.equals(pageTemplate.getAppPageId())) {
                defaultAppPageId = pageTemplate.getAppPageId();
            }
        }
        ret.setAppPageIdList(appPageIdList);
        ret.setDefaultAppPageId(defaultAppPageId);
        return ret;
    }

    @RequestMapping(value = "/getVendorAppPageTemplate", method = RequestMethod.POST)
    @Override
    public GetVendorAppPageTemplateResult getVendorAppPageTemplate(@RequestHeader Map<String, String> headers,
                                                                   @RequestBody GetVendorAppPageTemplateArg arg) {
        String ea = qxEIEAConverter.enterpriseIdToEa(arg.getEi());
        UserInfo userInfo = new UserInfo();
        userInfo.setEnterpriseId(arg.getEi());
        userInfo.setEmployeeId(arg.getEmployeeId());
        userInfo.setEnterpriseAccount(ea);
        GetAppVendorTemplateListArg getArg = new GetAppVendorTemplateListArg();
        getArg.setAppId(arg.getAppId());
        ClientInfo clientInfo = new ClientInfo();
        if (TraceContext.get() != null && StringUtils.isNotBlank(TraceContext.get().getLocale())) {
            clientInfo.setLocale(Locale.forLanguageTag(TraceContext.get().getLocale()));
        } else {
            clientInfo.setLocale(Locale.CHINESE);
        }
        clientInfo.setType(ClientTypeEnum.Android);
        GetAppVendorTemplateListResult listResult = userPageTempleAction.getAppVendorTemplateList(userInfo, clientInfo, getArg);
        String defaultPageId = userPageTempleService.getUserDefaultTemplateId(arg.getEi(),
                arg.getEmployeeId(), arg.getAppId(), HomePageConstant.app);

        GetVendorAppPageTemplateResult ret = new GetVendorAppPageTemplateResult();
        if (listResult == null
                || CollectionUtils.isEmpty(listResult.getUserAppPageTemplateList())) {
            return ret;
        }
        List<UserAppPageTemplate> userAppPageTemplateList = listResult.getUserAppPageTemplateList();
        Map<Integer, List<String>> templateListMap = new HashMap<>();
        String defaultAppPageId = null;
        for (UserAppPageTemplate template : userAppPageTemplateList) {
            if (templateListMap.containsKey(template.getUpTenantId())) {
                templateListMap.get(template.getUpTenantId()).add(template.getAppPageId());
            } else {
                templateListMap.put(template.getUpTenantId(), Lists.newArrayList(template.getAppPageId()));
            }
            if (StringUtils.isNotBlank(defaultPageId) && defaultPageId.equals(template.getAppPageId())) {
                defaultAppPageId = template.getAppPageId();
            }
        }
        ret.setTemplateListMap(templateListMap);
        ret.setDefaultAppPageId(defaultAppPageId);
        return ret;
    }

    @RequestMapping(value = "/getCrossAppPageTemples", method = RequestMethod.POST)
    @Override
    public GetCrossAppPageTemplesResult getCrossAppPageTemples(@RequestHeader Map<String, String> headers,
                                                               @RequestBody GetCrossAppPageTemplesArg arg) {
        UserInfo userInfo = new UserInfo();
        userInfo.setEmployeeId(arg.getEmployeeId());
        userInfo.setEnterpriseId(arg.getTenantId());
        GetAppPageTemplesArg getAppPageTemplesArg = new GetAppPageTemplesArg();
        getAppPageTemplesArg.setAppId(arg.getAppId());
        GetAppPageTemplesResult result = tenantPageTempleAction.getAppPageTemples(userInfo, getAppPageTemplesArg);
        return GetCrossAppPageTemplesResult.builder().
                appPageTempleList(result.getAppPageTempleList())
                .build();
    }

    @RequestMapping(value = "/queryInnerWebStatAccessInfo", method = RequestMethod.POST)
    @Override
    public QueryAppStatAccessInfoResult queryInnerWebStatAccessInfo(@RequestHeader Map<String, String> headers,
                                                                    @RequestBody GetInnerAppPageTemplateArg arg) {
        QueryAppStatAccessInfoResult ret = new QueryAppStatAccessInfoResult();
        List<StatAccessInfo> statAccessInfoList = Lists.newArrayList();
        Pair<String, List<UserMenuItem>> pair = queryInnerWebMenus(arg.getEi(), arg.getEmployeeId(), arg.getAppId());
        if (pair == null) {
            log.warn("queryInnerWebMenus return null, ei:{}, employeeId:{}, appId:{}", arg.getEi(), arg.getEmployeeId(), arg.getAppId());
            return ret;
        }
        List<UserMenuItem> items = pair.getRight();
        log.info("queryInnerWebStatAccessInfo, items:{}, arg:{}", items, arg);
        for (UserMenuItem item : items) {
            StatAccessInfo statAccessInfo = getStatAccessInfo(null, item.getId());
            if (statAccessInfo != null) {
                statAccessInfoList.add(statAccessInfo);
            }
        }
        statAccessInfoList = appendStatAccessInfo(statAccessInfoList, arg.getAppId(), null);
        ret.setStatAccessInfos(statAccessInfoList);
        ret.setTemplateId(pair.getLeft());
        log.info("queryInnerWebStatAccessInfo, arg:{}, ret:{}", arg, ret);
        return ret;
    }

    private StatAccessInfo getStatAccessInfo(String upEa, String accessKey) {
        CountSource countSource = MenuCountSourceConfig.getCountSource(accessKey);
        if (countSource == null) {
            return null;
        }
        StatAccessInfo statAccessInfo = new StatAccessInfo();
        statAccessInfo.setAccessKey(accessKey);
        statAccessInfo.setUpEa(upEa);
        if (MenuCountSourceConfig.CountSourceType_SERVER_CONFIG == countSource.getCountSourceType()) {
            UserStatConfig userStatConfig = countSource.getUserStatConfig();
            statAccessInfo.setUserStatConfig(userStatConfig);
        }
        return statAccessInfo;
    }

    private Pair<String, List<UserMenuItem>> queryInnerWebMenus(int ei, int employeeId, String appId) {
        List<PageTemplate> templateList = userPageTempleService.getUserPageTemplateList(ei,
                employeeId, appId, TempleType.WEB);
        if (CollectionUtils.isEmpty(templateList)) {
            log.warn("queryInnerWebMenus templateList is empty, ei:{}, employeeId:{}, appId:{}",
                    ei, employeeId, appId);
            return null;
        }
        String defaultTemplateId = userPageTempleService.getUserDefaultTemplateId(ei,
                employeeId, appId, HomePageConstant.web);
        PageTemplate defaultTemplate = templateList.get(0);
        for (PageTemplate template : templateList) {
            if (template.getTempleId().equals(defaultTemplateId)) {
                log.info("queryInnerWebMenus defaultTemplate:{}", defaultTemplate);
                defaultTemplate = template;
                break;
            }
        }
        String ea = qxEIEAConverter.enterpriseIdToEa(ei);
        GetUserMenuByIdArg getUserMenuByIdArg = new GetUserMenuByIdArg();
        getUserMenuByIdArg.setMenuId(defaultTemplate.getWebMenuId());
        GetUserMenuByIdResult getUserMenuByIdResult = userMenuAction.getSupportUnReadUserMenuById(GeneralUtil.buildUserInfo(ei, ea, employeeId),
                buildWebClientInfo(), getUserMenuByIdArg);
        if (getUserMenuByIdResult == null || CollectionUtils.isEmpty(getUserMenuByIdResult.getItems())) {
            log.warn("queryInnerWebMenus getUserMenuByIdResult is empty, ei:{}, employeeId:{}, appId:{}",
                    ei, employeeId, appId);
            return Pair.of(defaultTemplate.getTempleId(), Lists.newArrayList());
        }
        return Pair.of(defaultTemplate.getTempleId(), getUserMenuByIdResult.getItems());
    }

    private ClientInfo buildWebClientInfo() {
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setType(ClientTypeEnum.Web);
        clientInfo.setLocale(Locale.CHINESE);
        return clientInfo;
    }

    @RequestMapping(value = "/queryCrossWebStatAccessInfo", method = RequestMethod.POST)
    @Override
    public QueryAppStatAccessInfoResult queryCrossWebStatAccessInfo(@RequestHeader Map<String, String> headers,
                                                                    @RequestBody GetCrossAppPageTemplateArg arg) {
        Pair<String, List<UserMenuItem>> pair = queryOuterWebMenus(arg.getUpEi(), arg.getOuterEi(), arg.getOuterId(), arg.getAppId());
        if (pair == null) {
            log.warn("queryOuterWebMenus return null, upEi:{}, outerEi:{}, outerId:{}, appId:{}",
                    arg.getUpEi(), arg.getOuterEi(), arg.getOuterId(), arg.getAppId());
            return null;
        }
        QueryAppStatAccessInfoResult ret = new QueryAppStatAccessInfoResult();
        List<StatAccessInfo> statAccessInfoList = Lists.newArrayList();
        List<UserMenuItem> items = pair.getRight();
        log.info("queryOuterWebMenus items:{}, upEi:{}, outerEi:{}, outerId:{}, appId:{}",
                items, arg.getUpEi(), arg.getOuterEi(), arg.getOuterId(), arg.getAppId());
        String upEA = qxEIEAConverter.enterpriseIdToEa(arg.getUpEi());
        for (UserMenuItem item : items) {
            StatAccessInfo statAccessInfo = getStatAccessInfo(upEA, item.getId());
            if (statAccessInfo != null) {
                statAccessInfoList.add(statAccessInfo);
            }
        }
        statAccessInfoList = appendStatAccessInfo(statAccessInfoList, arg.getAppId(), upEA);
        ret.setStatAccessInfos(statAccessInfoList);
        ret.setTemplateId(pair.getLeft());
        ret.setUpEi(String.valueOf(arg.getUpEi()));
        log.info("queryCrossWebStatAccessInfo, arg:{}, ret:{}", arg, ret);
        return ret;
    }

    private Pair<String, List<UserMenuItem>> queryOuterWebMenus(int upEi, long outerTenantId, long outerUserId, String appId) {
        List<UserPageTemplate> templateList = userPageTempleService.getUserPage(
                outerTenantId, outerUserId, upEi, appId, TempleType.WEB);
        if (CollectionUtils.isEmpty(templateList)) {
            log.warn("queryOuterWebMenus templateList is empty, upEi:{}, outerTenantId:{}, outerUserId:{}, appId:{}",
                    upEi, outerTenantId, outerUserId, appId);
            return null;
        }
        String defaultTemplateId = userPageTempleService.getUserDefaultTemplateId(upEi,
                (int) outerUserId, appId, HomePageConstant.web);
        UserPageTemplate defaultTemplate = templateList.get(0);
        for (UserPageTemplate template : templateList) {
            if (template.getTempleId().equals(defaultTemplateId)) {
                log.info("queryOuterWebMenus defaultTemplate:{}", defaultTemplate);
                defaultTemplate = template;
                break;
            }
        }
        String upEA = qxEIEAConverter.enterpriseIdToEa(upEi);
        UserInfo userInfo = GeneralUtil.buildUserInfo(upEi, upEA, 0);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(outerTenantId,
                outerUserId, null, null);
        GetUserMenuByAppIdArg getUserMenuByIdArg = new GetUserMenuByAppIdArg();
        getUserMenuByIdArg.setId(defaultTemplate.getWebMenuId());
        GetUserMenuByAppIdResult getUserMenuByIdResult = userMenuAction.getSupportUnReadUserMenuByAppId(userInfo, buildWebClientInfo(),
                outerUserInfo, getUserMenuByIdArg);
        if (getUserMenuByIdResult == null || CollectionUtils.isEmpty(getUserMenuByIdResult.getUserMenuTempleVos())) {
            log.warn("queryOuterWebMenus getUserMenuByIdResult is empty, upEi:{}, outerTenantId:{}, outerUserId:{}, appId:{}",
                    upEi, outerTenantId, outerUserId, appId);
            return Pair.of(defaultTemplate.getTempleId(), Lists.newArrayList());
        }
        return Pair.of(defaultTemplate.getTempleId(), getUserMenuByIdResult.getUserMenuTempleVos().get(0).getItems());
    }

    @RequestMapping(value = "/queryVendorWebStatAccessInfo", method = RequestMethod.POST)
    @Override
    public QueryAppStatAccessInfoResult queryVendorWebStatAccessInfo(@RequestHeader Map<String, String> headers,
                                                                     @RequestBody GetVendorAppPageTemplateArg arg) {
        log.info("queryVendorWebStatAccessInfo, arg:{}", arg);
        String ea = qxEIEAConverter.enterpriseIdToEa(arg.getEi());
        UserInfo userInfo = GeneralUtil.buildUserInfo(arg.getEi(), ea, arg.getEmployeeId());
        GetWebVendorTemplateListArg getArg = new GetWebVendorTemplateListArg();
        getArg.setAppId(arg.getAppId());
        GetWebVendorTemplateListResult listResult = userPageTempleAction.getWebVendorTemplateList(userInfo, buildWebClientInfo(), getArg);
        String defaultTemplateId = userPageTempleService.getUserDefaultTemplateId(arg.getEi(),
                arg.getEmployeeId(), arg.getAppId(), HomePageConstant.web);

        if (listResult == null
                || CollectionUtils.isEmpty(listResult.getUserWebPageTemplateList())) {
            log.warn("queryVendorWebStatAccessInfo listResult is empty, ei:{}, employeeId:{}, appId:{}",
                    arg.getEi(), arg.getEmployeeId(), arg.getAppId());
            return null;
        }

        List<UserWebPageTemplate> userAppPageTemplateList = listResult.getUserWebPageTemplateList();
        UserWebPageTemplate defaultTemplate = userAppPageTemplateList.get(0);
        for (UserWebPageTemplate template : userAppPageTemplateList) {
            if (template.getTempleId().equals(defaultTemplateId)) {
                defaultTemplate = template;
                break;
            }
        }

        GetOuterAccountByFsResult outerAccount = userPageTempleService.getOuterAccount(userInfo);
        OuterUserInfo outerUserInfo = GeneralUtil.buildOuterUserInfo(outerAccount.getOuterTenantId(),
                outerAccount.getOuterUid(), null, null);

        GetUserMenuByAppIdArg getUserMenuByIdArg = new GetUserMenuByAppIdArg();
        getUserMenuByIdArg.setId(defaultTemplate.getWebMenuId());
        String upEa = qxEIEAConverter.enterpriseIdToEa(defaultTemplate.getUpTenantId());
        UserInfo upUserInfo = GeneralUtil.buildUserInfo(defaultTemplate.getUpTenantId(), upEa, 0);
        log.info("queryVendorWebStatAccessInfo getUserMenuByIdArg:{}, userInfo:{}, outerUserInfo:{}",
                getUserMenuByIdArg, upUserInfo, outerUserInfo);
        GetUserMenuByAppIdResult getUserMenuByIdResult = userMenuAction.getSupportUnReadUserMenuByAppId(upUserInfo, buildWebClientInfo(), outerUserInfo, getUserMenuByIdArg);

        if (getUserMenuByIdResult == null || CollectionUtils.isEmpty(getUserMenuByIdResult.getUserMenuTempleVos())) {
            log.warn("queryVendorWebStatAccessInfo getUserMenuByIdResult is empty, ei:{}, employeeId:{}, appId:{}",
                    arg.getEi(), arg.getEmployeeId(), arg.getAppId());
            return null;
        }
        List<UserMenuItem> items = getUserMenuByIdResult.getUserMenuTempleVos().get(0).getItems();
        log.info("queryVendorWebStatAccessInfo items:{}, upEi:{}, outerEi:{}, outerId:{}, appId:{}",
                items, arg.getEi(), outerAccount.getOuterTenantId(), outerAccount.getOuterUid(), arg.getAppId());

        QueryAppStatAccessInfoResult ret = new QueryAppStatAccessInfoResult();
        List<StatAccessInfo> statAccessInfoList = Lists.newArrayList();
        for (UserMenuItem item : items) {
            StatAccessInfo statAccessInfo = getStatAccessInfo(upEa, item.getId());
            if (statAccessInfo != null) {
                statAccessInfoList.add(statAccessInfo);
            }
        }
        statAccessInfoList = appendStatAccessInfo(statAccessInfoList, arg.getAppId(), upEa);
        ret.setStatAccessInfos(statAccessInfoList);
        ret.setTemplateId(defaultTemplate.getTempleId());
        log.info("queryVendorWebStatAccessInfo, arg:{}, ret:{}", arg, ret);
        return ret;
    }

    private List<StatAccessInfo> appendStatAccessInfo(List<StatAccessInfo> statAccessInfoList, String appId, String upEa){
        List<String> webMenuCountSourceList = webMainChannelConfig.WebMenuCountSource.get(appId);
        if (CollectionUtils.isEmpty(webMenuCountSourceList)){
            return statAccessInfoList;
        }
        List<StatAccessInfo> ret = Lists.newArrayList(statAccessInfoList);
        for (String menuCountSourceId : webMenuCountSourceList){
            boolean has = false;
            for (StatAccessInfo info : statAccessInfoList){
                if (menuCountSourceId.equals(info.getAccessKey())){
                    has = true;
                    break;
                }
            }
            if (!has){
                StatAccessInfo statAccessInfo = getStatAccessInfo(upEa, menuCountSourceId);
                if (statAccessInfo != null) {
                    ret.add(statAccessInfo);
                }
            }
        }
        return ret;
    }

    @RequestMapping(value = "/getEmployeeConfigValueByApiName", method = RequestMethod.POST)
    @Override
    public GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByApiName(@RequestHeader Map<String, String> headers, @RequestBody GetEmployeeConfigValueByApiNameArg arg) {
        UserInfo userInfo = GeneralUtil.buildUserInfo(arg.getTenantId(), qxEIEAConverter.enterpriseIdToEa(arg.getTenantId()), arg.getUserId());
        return homePageObjectAction.getEmployeeConfigValueByApiName(userInfo, arg);
    }

    @RequestMapping(value = "/setEmployeeConfigValueByApiName", method = RequestMethod.POST)
    @Override
    public SetEmployeeConfigValueResult setEmployeeConfigValueByApiName(@RequestHeader Map<String, String> headers, @RequestBody SetEmployeeConfigValueByApiNameArg arg) {

        UserInfo userInfo = GeneralUtil.buildUserInfo(arg.getTenantId(), qxEIEAConverter.enterpriseIdToEa(arg.getTenantId()), arg.getUserId());
        return homePageObjectAction.setEmployeeConfigValueByApiName(userInfo, arg);
    }

    @Override
    @RequestMapping(value = "/getAppPageInfoByUserHighestPageTemple", method = RequestMethod.POST)
    public GetAppPageInfoByUserHighestPageTempleResult getAppPageInfoByUserHighestPageTemple(@RequestHeader Map<String, String> headers, @RequestBody GetAppPageInfoByUserHighestPageTempleArg arg) {
        //查询用户优先级最高的的视图
        List<PageTemplate> templateList = userPageTempleService.getUserPageTemplateList(arg.getTenantId(), arg.getUserId(), arg.getAppId(), TempleType.APP);
        if (CollectionUtils.isNotEmpty(templateList)) {
            //视图里的appPageId
            String appPageId = templateList.get(0).getAppPageId();
            GetPageTemplate.Arg getPageTemplate = new GetPageTemplate.Arg();
            getPageTemplate.setId(appPageId);
            getPageTemplate.setEnterpriseId(arg.getTenantId());
            getPageTemplate.setLocale(Locale.CHINA);
            //查询appPageId对应的appPageInfo
            GetPageTemplate.Result result = pageTemplateService.getPageTemplate(getPageTemplate);
            AppPageTemplateVO appPageTemplateVO = new AppPageTemplateVO();
            BeanUtils.copyProperties(result.getPageTemplate(), appPageTemplateVO);
            return GetAppPageInfoByUserHighestPageTempleResult.builder()
                    .appPageTemplate(appPageTemplateVO)
                    .build();
        }
        return null;
    }


    @RequestMapping(value = "/getEmployeeConfigValueByApiNameHasNotDefault", method = RequestMethod.POST)
    @Override
    public GetEmployeeConfigValueByKeysResult getEmployeeConfigValueByApiNameHasNotDefault(@RequestHeader Map<String, String> headers, @RequestBody GetEmployeeConfigValueByApiNameArg arg) {
        UserInfo userInfo = GeneralUtil.buildUserInfo(arg.getTenantId(), qxEIEAConverter.enterpriseIdToEa(arg.getTenantId()), arg.getUserId());
        return homePageObjectAction.getEmployeeConfigValueByApiNameHasNotDefault(userInfo, arg);
    }
}
