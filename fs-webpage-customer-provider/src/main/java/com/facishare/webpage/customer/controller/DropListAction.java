package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.component.GetMenuDropListArg;
import com.facishare.webpage.customer.controller.model.arg.component.GetUtilityBarDropListArg;
import com.facishare.webpage.customer.controller.model.result.component.GetMenuDropListResult;
import com.facishare.webpage.customer.controller.model.result.component.GetUtilityBarDropListResult;

/**
 * Created by zhangyu on 2020/10/27
 */
public interface DropListAction {

    /**
     * 菜单的设计器
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetMenuDropListResult getMenuDropList(UserInfo userInfo, ClientInfo clientInfo, GetMenuDropListArg arg);

    /**
     * 工具栏设计器
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetUtilityBarDropListResult getUtilityBarDropList(UserInfo userInfo, ClientInfo clientInfo, GetUtilityBarDropListArg arg);

    /**
     * 个人级菜单设计器
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetMenuDropListResult getUserMenuDropList(UserInfo userInfo, ClientInfo clientInfo, GetMenuDropListArg arg);
}
