package com.facishare.webpage.customer.common;

import com.alibaba.fastjson.JSONObject;
import com.facishare.qixin.objgroup.common.service.utils.PaasMetaUtils;
import com.facishare.rest.api.model.newmetadata.NewMetadataBaseResult;
import com.facishare.webpage.customer.api.constant.ScopeType;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeArg;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeResult;
import com.facishare.webpage.customer.common.model.QueryScopeNameArg;
import com.facishare.webpage.customer.common.model.QueryScopeNameResult;
import com.facishare.webpage.customer.common.resource.WebPagePaaSApiBusV3Resource;
import com.facishare.webpage.customer.common.resource.model.result.QueryObjectNameResult;
import com.facishare.webpage.customer.core.util.ScopesUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 * @date 2021/11/29 2:51 pm
 */
@Component
@Slf4j
public class WebPageOutUidService implements WebPageOrganizationService {

    @Resource
    private WebPagePaaSApiBusV3Resource webPagePaaSApiBusV3Resource;

    @Override
    public QueryScopeNameResult queryScopeName(QueryScopeNameArg arg) {
        List<String> outUIds = arg.getScopeList().
                stream().
                filter(scope -> ScopeType.OutUId.getType() == scope.getDataType()).
                distinct().
                map(Scope::getDataId).
                collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outUIds)) {
            return null;
        }
        Map<String, String> scopeNameMap = Maps.newHashMap();
        try {
            Map<String, String> headers = PaasMetaUtils.getHeaders(String.valueOf(arg.getTenantId()));
            NewMetadataBaseResult<JSONObject> employeeObjNamesResult = webPagePaaSApiBusV3Resource.findPublicEmployeeObjNames(headers, outUIds);
            QueryObjectNameResult queryObjectNameResult = JSONObject.parseObject(JSONObject.toJSONString(employeeObjNamesResult.getData()), QueryObjectNameResult.class);
            if (queryObjectNameResult == null
                    || queryObjectNameResult.getQueryResult() == null
                    || CollectionUtils.isEmpty(queryObjectNameResult.getQueryResult().getData())) {
                return QueryScopeNameResult.builder().build();
            }
            queryObjectNameResult.
                    getQueryResult().
                    getData().stream().forEach(x -> {
                        scopeNameMap.put(x.getId(), x.getName());
                    });
        } catch (Exception e) {
            log.error("findPublicEmployeeObjNames error by arg:{}", arg, e);
        }
        return QueryScopeNameResult.builder().
                scopeType(ScopeType.OutUId.getType()).
                scopeNameMap(scopeNameMap).build();
    }

    @Override
    public QueryEmployeeScopeResult queryUserScope(QueryEmployeeScopeArg arg) {
        if (!arg.isCross()) {
            return null;
        }
        List<Scope> scopeList = ScopesUtil.buildScope(ScopeType.OutUId.getType(), Lists.newArrayList(String.valueOf(arg.getOutUserId())));
        return QueryEmployeeScopeResult.builder().
                scopeList(scopeList).build();
    }
}
