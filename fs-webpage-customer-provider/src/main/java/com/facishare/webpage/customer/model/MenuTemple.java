package com.facishare.webpage.customer.model;

import com.facishare.webpage.customer.api.model.Scope;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/16.
 */
@Data
public class MenuTemple implements Serializable {
    private String menuId;
    private String appId;
    private Integer tenantId;
    private List<MenuItem> menuItems = Lists.newArrayList();
    private String templeId;
    private int status;
    private String name;
    private int priorityLevel;
    private String description;
    @Deprecated
    private List<String> roleIds;
    @Deprecated
    private List<String> roleNames;
    private List<String> scopeNameList;
    private List<Scope> scopeList;
    private int creatorId;
    private long createTime;
    private String creatorName;
    private int updaterId;
    private long updateTime;
    private String updaterName;
    private boolean isSystem;
    private Boolean isShowMenuIcon = true;
    private String target;

    private Boolean hiddenQuickCreate = false;
    private Boolean dealHomeFlag = false;
}
