package com.facishare.webpage.customer.remote;

import com.facishare.webpage.customer.metadata.model.ApplicationData;

import java.util.List;
import java.util.Locale;

/**
 * Created by zhangyu on 2021/1/22
 */
public interface InnerAppCacheService {

    List<ApplicationData> queryUserInnerAppViews(int tenantId, String enterpriseAccount, int employeeId, Locale locale);

    void removeCacheByEmployee(int enterpriseId, int employeeId);

    void removeCacheByEnterpriseId(int enterpriseId);

}
