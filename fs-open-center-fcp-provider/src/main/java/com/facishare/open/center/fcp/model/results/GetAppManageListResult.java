package com.facishare.open.center.fcp.model.results;

import com.alibaba.fastjson.annotation.JSONField;
import com.dyuproject.protostuff.Tag;
import com.facishare.open.center.fcp.model.vos.ComponentVO;

import java.io.Serializable;
import java.util.List;


/**
 * result.
 * Created by zenglb on 2016/1/7.
 */
public class GetAppManageListResult implements Serializable {
    private static final long serialVersionUID = 2832618154033635369L;

    /**
     * 错误码，1.请求成功.2.参数错误.
     */
    @Tag(1)
    @JSONField(name = "M1")
    private String code;

    /**
     * 可见列表.
     */
    @Tag(2)
    @JSONField(name = "M2")
    private List<ComponentVO> visibleComponents;

    /**
     * 不可见列表.
     */
    @Tag(3)
    @JSONField(name = "M3")
    private List<ComponentVO> unVisibleComponents;

    public GetAppManageListResult() {
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<ComponentVO> getVisibleComponents() {
        return visibleComponents;
    }

    public void setVisibleComponents(List<ComponentVO> visibleComponents) {
        this.visibleComponents = visibleComponents;
    }

    public List<ComponentVO> getUnVisibleComponents() {
        return unVisibleComponents;
    }

    public void setUnVisibleComponents(List<ComponentVO> unVisibleComponents) {
        this.unVisibleComponents = unVisibleComponents;
    }

    @Override
    public String toString() {
        return "GetAppManageListResult{" +
                "code='" + code + '\'' +
                ", visibleComponents=" + visibleComponents +
                ", unVisibleComponents=" + unVisibleComponents +
                '}';
    }
}
