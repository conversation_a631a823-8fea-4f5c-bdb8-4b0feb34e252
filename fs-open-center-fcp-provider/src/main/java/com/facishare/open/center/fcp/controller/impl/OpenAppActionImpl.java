package com.facishare.open.center.fcp.controller.impl;

import com.facishare.open.app.ad.api.enums.ModuleKeyEnum;
import com.facishare.open.app.ad.api.service.FsUserVisibleConfigService;
import com.facishare.open.app.ad.api.service.NewComponentService;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.BaseDO;
import com.facishare.open.app.center.api.model.CallBackBindDO;
import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.enums.AppViewTypeEnum;
import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.app.center.api.model.property.OpenAppProperties;
import com.facishare.open.app.center.api.model.vo.TryStatusAppVO;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.AppListResult;
import com.facishare.open.app.center.api.service.AppCallBackService;
import com.facishare.open.app.center.api.service.AppIconService;
import com.facishare.open.app.center.api.service.OpenAppComponentService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.center.fcp.controller.OpenAppAction;
import com.facishare.open.center.fcp.manager.AppAdManager;
import com.facishare.open.center.fcp.manager.AppIconManager;
import com.facishare.open.center.fcp.manager.CheckAppUpdatedManager;
import com.facishare.open.center.fcp.manager.OpenAppComponentManager;
import com.facishare.open.center.fcp.model.AppLoginUrlArg;
import com.facishare.open.center.fcp.model.AppLoginUrlResult;
import com.facishare.open.center.fcp.model.AppVO;
import com.facishare.open.center.fcp.model.VisibleAppListArg;
import com.facishare.open.center.fcp.model.VisibleAppListResult;
import com.facishare.open.center.fcp.model.args.*;
import com.facishare.open.center.fcp.model.cons.AppBizConstant;
import com.facishare.open.center.fcp.model.enums.ResultCodeEnum;
import com.facishare.open.center.fcp.model.results.*;
import com.facishare.open.center.fcp.model.vos.BannerVO;
import com.facishare.open.center.fcp.model.vos.BestAppVO;
import com.facishare.open.center.fcp.model.vos.ComponentVO;
import com.facishare.open.center.fcp.model.vos.ModuleStatusVO;
import com.facishare.open.center.fcp.model.vos.ModuleVO;
import com.facishare.open.center.fcp.model.vos.MoreAppVO;
import com.facishare.open.center.fcp.model.vos.ServiceNumberVO;
import com.facishare.open.center.fcp.utils.*;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.MsgSessionService;
import com.fxiaoke.i18n.client.I18nClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * app 请求.
 *
 * <AUTHOR>
 * @since 2015年8月12日 下午2:10:27
 */
@Service("Messenger")
public class OpenAppActionImpl implements OpenAppAction {
    private static final Logger logger = LoggerFactory.getLogger(OpenAppActionImpl.class);

    @Resource
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Resource
    private AppCallBackService appCallBackService;
    @Resource
    private OpenAppService openAppService;
    
    @Resource
    private AppAdManager appAdManager;
    @Resource
    private CheckAppUpdatedManager checkAppUpdatedManager;
    @Resource
    private NewComponentService newComponentService;
    @Resource
    private FsUserVisibleConfigService fsUserVisibleConfigService;

    @Resource
    private AppIconService appIconService;

    @Resource
    private OpenAppComponentManager openAppComponentManager;
    @Resource
    private MsgSessionService msgSessionService;
    @Resource
    private OpenAppComponentService openAppComponentService;

    @Resource
    private AppIconManager appIconManager;

    @Override
    public VisibleAppListResult queryAppList(VisibleAppListArg arg) {
        VisibleAppListResult result = new VisibleAppListResult();
        if (null == arg) {
            logger.warn(" queryAppList param error! arg[null]");
            return result;
        }
        ArrayList<AppVO> list = new ArrayList<>();
        List<UserCanViewListVO> userCanViewListVOs = openAppComponentManager.queryUserCanViewListVOByFsUser(arg.getVersion());
        if (!CollectionUtils.isEmpty(userCanViewListVOs)) {
            userCanViewListVOs.forEach(userCanViewListVO -> {
                AppVO vo = new AppVO();
                vo.setComponentId(userCanViewListVO.getComponentId());
                vo.setComponentLabel(userCanViewListVO.getComponentLabel());
                vo.setComponentName(userCanViewListVO.getComponentName());
                vo.setLogoUrl(userCanViewListVO.getImageUrl() + "&ts=2016");
                vo.setAppId(userCanViewListVO.getAppId());
                list.add(vo);
            });
        }
        result.setAppList(list);
        return result;
    }

    @Override
    public AppLoginUrlResult getCallBackBindUrl(AppLoginUrlArg arg) {
        logger.debug("getCallBackBindUrl param:{} clientType:{}", arg, FcpServiceContextUtil.getEmployeeFullId());

        AppLoginUrlResult result = new AppLoginUrlResult();
        if (null == arg || Strings.isNullOrEmpty(arg.getAppOrComponentId())) {
            logger.warn(" getCallBackBindUrl param error! arg[{}]", arg);
            return result;
        }
        String fsUserId = FcpServiceContextUtil.getEmployeeFullId();

        try {
                CallBackBindDO callbackArg = new CallBackBindDO();
                callbackArg.setAppOrComponentId(arg.getAppOrComponentId());
                callbackArg.setFsUserId(fsUserId);
                callbackArg.setAppType(AppViewTypeEnum.APP);
                callbackArg.setMessageJumpUrl(arg.getMessageJumpUrl());

                // 调用B1层 获取回调登录url
                com.facishare.open.app.center.api.result.BaseResult callbackResult = appCallBackService.getAppCallBackBindParam(callbackArg);
                if (callbackResult != null && callbackResult.isSuccess()) {
                    result.setAppOrComponentUrl((String) callbackResult.getResult());
                }
//            }

            //消去红点
            CommonThreadPoolUtils.getExecutor().execute(() -> {
                try {
                    FsUserVO fsUserVO = new FsUserVO(fsUserId);
                    BaseResult<Boolean> cancelNewStateAndGetResult = newComponentService
                            .cancelNewStateAndGet(fsUserVO, arg.getAppOrComponentId());
                    if (!cancelNewStateAndGetResult.isSuccess()) {
                        logger.warn("clickComponent for click first failed ,user {}, componentId {} ,result {} ", fsUserId,
                                arg.getAppOrComponentId(), cancelNewStateAndGetResult);
                    }
                    if (cancelNewStateAndGetResult.getResult()){
                        com.facishare.open.app.center.api.result.BaseResult<OpenAppComponentDO> openAppComponentDOBaseResult = openAppComponentService.loadOpenAppComponentById(fsUserVO, arg.getAppOrComponentId());
                        if (!openAppComponentDOBaseResult.isSuccess() || null == openAppComponentDOBaseResult.getResult()){
                            logger.warn("loadOpenAppComponentById failed,appOrComponentId[{}], user[{}], result[{}]", arg.getAppOrComponentId(), fsUserVO, openAppComponentDOBaseResult);
                            return;
                        }
                        notifyViewModified(openAppComponentDOBaseResult.getResult().getAppId(), fsUserVO.getEnterpriseAccount(), Lists.newArrayList(fsUserVO.getUserId()));//可以考虑不处理。
                    }
                } catch (Exception e) {
                    logger.warn("cancelNewState failed. fsUserId[{}], componentId[{}]", fsUserId, arg.getAppOrComponentId(), e);
                }
            });
            //logger.debug("getCallBackBindUrl result:{} ", result);
        } catch (Exception e) {
            logger.warn("getCallBackBindUrl arg:{} error:{}", arg, e);
        }
        return result;
    }


    /**
     * 通知纷享用户的终端重新拉取可见列表.
     *
     * @param appId    应用id
     * @param fsEa     企业账号
     * @param ownerIds 用户id列表
     */
    private void notifyViewModified(String appId, String fsEa, List<Integer> ownerIds) {
        // 更新推送手机端
        logger.info("notify view modified to client ： appId＝{},fsEa={},ownerIds={}", appId, fsEa, ownerIds);
        //更新checkUpdate
        checkAppUpdatedManager.resetTagByModuleKeyAndUserIds(ModuleKeyEnum.COMPONENTS, fsEa, ownerIds);
        String value = "{\"timestamp\":" + System.currentTimeMillis() + "}";
        MessageResult msgResult = msgSessionService.updateUserPropertiesBatchAsync(appId, fsEa, ownerIds,
                CommonConstant.UPDATE_USER_PROPERTIES_VIEW_MODIFIED, value);
        if (!msgResult.isSuccess()) {
            logger.warn("msgSessionService.updateUserPropertiesBatchAsync appId[{}], fsEa[{}], ownerIds[{}], msgResult[{}]", appId, fsEa, ownerIds, msgResult);
        }
    }


    @Override
    public GetMoreAppsResult getMoreApps(GetMoreAppsArg arg) {

        GetMoreAppsResult getMoreAppsResult = new GetMoreAppsResult();
        if (null == arg) {
            logger.warn(" getMoreApps param error! arg[null]");
            getMoreAppsResult.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return getMoreAppsResult;
        }
        try {
            FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
            Pager<OpenAppDO> page = new Pager<>();

            page.setCurrentPage(arg.getCurrentPage());
            page.setPageSize(arg.getPageSize());
            page.addParam("fsEnterpriseAccount", fsUser.getEnterpriseAccount());
            page.addParam("fsAdminUser", fsUser.asStringUser());
            page.addParam("fsUser", fsUser);
            // 获取所有第三方应用（不排除已授权的应用）modify by albert 2016-2-22
            com.facishare.open.app.center.api.result.BaseResult<Pager<TryStatusAppVO>> pageResult =
                    openAppService.queryDevAppList(page);

            Pager<TryStatusAppVO> result = pageResult.getResult();

            getMoreAppsResult.setCurrentPage(result.getCurrentPage());
            getMoreAppsResult.setPageTotal(result.getPageTotal());
            getMoreAppsResult.setRecordSize(result.getRecordSize());

            List<MoreAppVO> moreAppVOs = new ArrayList<>();
            if (!CollectionUtils.isEmpty(result.getData())) {
                //只保留在线状态的应用
                moreAppVOs = result.getData().stream().filter(tryStatusAppVO -> {
                            String appId = tryStatusAppVO.getOpenAppDO().getAppId();
                            //                //如果版本小于5.2 ，过滤pk会议助手
                            if (NumberUtils.toFloat(BizCommonUtil.getBigVersion(arg.getVersion()), 0) < 5.19) {
                                if (StringUtils.equalsIgnoreCase(BizCommonUtil.FS_BROKER_PK_APP_ID.trim(), appId) ||
                                        StringUtils.equalsIgnoreCase(BizCommonUtil.FS_BROKER_MEETING_APP_ID.trim(),
                                                appId)) {

                                    return false;
                                }
                            }


                            return true;
                        }
                ).map(tryStatusAppVO -> openAppComponentManager.tryStatusAppVOToMoreAppVO(tryStatusAppVO, arg.getVersion(), fsUser))
                        .collect(Collectors.toList());

                //补充logoUrl
                List<String> appIds = moreAppVOs.stream().map(MoreAppVO::getAppId).collect(Collectors.toList());
                Map<String, String> appIdIconUrlMap = appIconManager.batchGetIconUrl(appIds, IconType.LOGO);
                moreAppVOs.forEach(moreAppVO -> {
                    String iconUrl = appIdIconUrlMap.get(moreAppVO.getAppId());
                    if (StringUtils.isNotBlank(iconUrl)) {
                        moreAppVO.setImageUrl(iconUrl);
                    }
                });
            }

            //替换多语言
            Locale locale = FcpServiceContextUtil.getAuthInfo().getLocale();
            logger.debug("getMoreApps .locale[{}]", locale);
            if (locale != null) {
                moreAppVOs = modifyMoreAppsByLocale(moreAppVOs, locale);
            }

            getMoreAppsResult.setMoreApps(moreAppVOs);
            getMoreAppsResult.setPageSize(result.getPageSize());
            getMoreAppsResult.setCode(ResultCodeEnum.SUCCESS.getStringCode());
            return getMoreAppsResult;
        } catch (Exception e) {
            logger.error("getMoreApps arg:{} error:{}", arg, e);
        }
        getMoreAppsResult.setCode(ResultCodeEnum.FAIL.getStringCode());

        logger.debug("getMoreApps arg:{} , result:{}", arg, getMoreAppsResult);

        return getMoreAppsResult;
    }


    private List<MoreAppVO> modifyMoreAppsByLocale(List<MoreAppVO> moreAppVOs, Locale locale) {
        //应用名称
        List<String> appNameKeys = Lists.newArrayList();
        String appNameKeyFormatString = "appcenter.appinfo.%s.appName";

        //应用描述
        List<String> appDescKeys = Lists.newArrayList();
        String appDescKeyFormatString = "appcenter.appinfo.%s.appDesc";

        moreAppVOs.forEach(moreAppVO -> {
            appNameKeys.add(String.format(appNameKeyFormatString, moreAppVO.getAppId()));
            appDescKeys.add(String.format(appDescKeyFormatString, moreAppVO.getAppId()));
        });

        Map<String, String> appNameMap = I18nClient.getInstance()
                .get(appNameKeys, FcpServiceContextUtil.getAuthInfo().getEnterpriseId(), locale.toLanguageTag());

        Map<String, String> appDescMap = I18nClient.getInstance()
                .get(appDescKeys, FcpServiceContextUtil.getAuthInfo().getEnterpriseId(), locale.toLanguageTag());

        //button
        String appHasAddedButtonTextKey = "appcenter.appinfo.hasadded.button.text";
        String appNotAddedButtonTextKey = "appcenter.appinfo.notadded.button.text";

        Map<String, String> appButtonTextMap = I18nClient.getInstance()
                .get(Arrays.asList(appHasAddedButtonTextKey, appNotAddedButtonTextKey), FcpServiceContextUtil.getAuthInfo().getEnterpriseId(), locale.toLanguageTag());

        moreAppVOs.forEach(moreAppVO -> {
            String appName = appNameMap.get(String.format(appNameKeyFormatString, moreAppVO.getAppId()));
            if (StringUtils.isNotBlank(appName)) {
                moreAppVO.setAppName(appName);
            }
            String appDesc = appDescMap.get(String.format(appDescKeyFormatString, moreAppVO.getAppId()));
            if (StringUtils.isNotBlank(appDesc)) {
                moreAppVO.setDesc(appDesc);
            }

            if (Objects.equals(moreAppVO.getTargetText(), "添加")) { // ignoreI18n
                String notAddedButtonText = appButtonTextMap.get(appNotAddedButtonTextKey);
                if (StringUtils.isNotBlank(notAddedButtonText)) {
                    moreAppVO.setTargetText(notAddedButtonText);
                }
            }

            if (Objects.equals(moreAppVO.getTargetText(), "已添加")) { // ignoreI18n
                String hasAddedButtonText = appButtonTextMap.get(appHasAddedButtonTextKey);
                if (StringUtils.isNotBlank(hasAddedButtonText)) {
                    moreAppVO.setTargetText(hasAddedButtonText);
                }
            }

        });
        return moreAppVOs;
    }

    @Override
    public CheckAppUpdatedResult checkAppUpdated(CheckAppUpdatedArg arg) {
        CheckAppUpdatedResult result = new CheckAppUpdatedResult();
        if (null == arg || CollectionUtils.isEmpty(arg.getModules())) {
            logger.warn("checkAppUpdated arg check fail,arg [{}]", arg);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        for (ModuleVO moduleVO : arg.getModules()) {
            if (StringUtils.isBlank(moduleVO.getModuleKey()) || StringUtils.isBlank(moduleVO.getTag()) || null == ModuleKeyEnum.getByModuleKey(moduleVO.getModuleKey())) {
                result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
                logger.error("checkAppUpdated arg check fail,moduleVO [{}] modules [{}]", moduleVO, arg.getModules());
                return result;
            }
        }
        // 获取用户信息
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        try {
            List<ModuleStatusVO> moduleStatuses = checkAppUpdatedManager.checkAppUpdated(fsUser, arg.getVersion(), arg.getModules());
            result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
            result.setModuleStatuses(moduleStatuses);
        } catch (Exception e) {
            logger.error("checkAppUpdated failed. arg:{} error:{}", arg, e);
            result.setCode(ResultCodeEnum.FAIL.getStringCode());
        }
        return result;
    }

    @Override
    public GetBannersResult getBanners(GetBannersArg arg) {
        GetBannersResult result = new GetBannersResult();
        if (null == arg) {
            logger.warn("getBanners arg check fail,arg [null]");
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        // 获取用户信息
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        try {
            List<BannerVO> banners = appAdManager.getBanners(fsUser, arg.getVersion());
            result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
            result.setModuleKey(ModuleKeyEnum.BANNERS.getModuleKey());
            result.setTag(checkAppUpdatedManager.getOrGenerateTag(fsUser, arg.getVersion(), ModuleKeyEnum.BANNERS));
            result.setBanners(banners);
        } catch (Exception e) {
            logger.error("getBanners failed arg:[{}] user[{}] error:{}", arg, fsUser, e);
            result.setCode(ResultCodeEnum.FAIL.getStringCode());
        }
        return result;
    }

    @Override
    public GetBestAppsResult getBestApps(GetBestAppsArg arg) {
        GetBestAppsResult result = new GetBestAppsResult();
        if (null == arg) {
            logger.warn("getBestApps arg check fail,arg [null]");
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        // 获取用户信息
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        try {
            List<BestAppVO> bestApps = appAdManager.getBestApps(fsUser, arg.getVersion());
            result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
            result.setModuleKey(ModuleKeyEnum.BEST_APPS.getModuleKey());
            result.setTag(checkAppUpdatedManager.getOrGenerateTag(fsUser, arg.getVersion(), ModuleKeyEnum.BEST_APPS));
            result.setBestApps(bestApps);
        } catch (Exception e) {
            logger.error("getBestApps failed arg:[{}] user[{}] error:{}", arg, fsUser, e);
            result.setCode(ResultCodeEnum.FAIL.getStringCode());
        }
        return result;
    }

    @Override
    public GetVisibleComponentsResult getVisibleComponents(GetVisibleComponentsArg arg) {
        GetVisibleComponentsResult result = new GetVisibleComponentsResult();
        if (null == arg) {
            logger.warn("getVisibleComponents arg check fail,arg [null]");
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }

        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        GetAppManageListArg getAppManageListArg = new GetAppManageListArg();
        getAppManageListArg.setVersion(arg.getVersion());
        GetAppManageListResult appManageListResult = getAppManageList(getAppManageListArg);

        result.setCode(ResultCodeEnum.FAIL.getStringCode());
        if (ResultCodeEnum.SUCCESS.getStringCode().equals(appManageListResult.getCode())) {
            result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
            result.setModuleKey(ModuleKeyEnum.COMPONENTS.getModuleKey());
            result.setTag(checkAppUpdatedManager.getOrGenerateTag(fsUser, arg.getVersion(), ModuleKeyEnum.COMPONENTS));
            result.setComponentList(appManageListResult.getVisibleComponents());
        }
        return result;
    }

    @Override
    public GetAppManageListResult getAppManageList(GetAppManageListArg arg) {
        GetAppManageListResult result = new GetAppManageListResult();
        if (null == arg) {
            logger.warn("getAppManageList param error! arg[null]");
            return result;
        }
        List<ComponentVO> componentVOList = openAppComponentManager.queryComponentVOListByFsUser(arg.getVersion());

        // 获取用户信息
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
        if (!CollectionUtils.isEmpty(componentVOList)) {

            BaseResult<List<String>> visibleComponentIdsResult = fsUserVisibleConfigService.loadVisibleComponentIds(fsUser
                    .asStringUser());
            if (!visibleComponentIdsResult.isSuccess()) {
                logger.error("loadVisibleComponentIds failed,arg={} result={}", arg, visibleComponentIdsResult);
                result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
                return result;
            }
            List<String> visibleComponentIds = visibleComponentIdsResult.getResult();
            List<ComponentVO> visibleComponents = new ArrayList<>();
            List<ComponentVO> unVisibleComponents = new ArrayList<>();
            componentVOList.forEach(component -> {
                if (null == visibleComponentIds || visibleComponentIds.contains(component.getComponentId())) {
                    visibleComponents.add(component);
                } else {
                    unVisibleComponents.add(component);
                }
            });
            if (!CollectionUtils.isEmpty(visibleComponentIds)) {
                Map<String, Integer> map = new HashMap<>();
                for (int i = 0; i < visibleComponentIds.size(); i++) {
                    map.put(visibleComponentIds.get(i), i);
                }
                visibleComponents.sort((ComponentVO o1, ComponentVO o2) -> {
                    int a = map.get(o1.getComponentId());
                    int b = map.get(o2.getComponentId());
                    return a == b ? 0 : (a > b ? 1 : -1);
                });
            }
            result.setVisibleComponents(visibleComponents);
            result.setUnVisibleComponents(unVisibleComponents);
        }
        return result;
    }

    @Override
    public UpdateVisibleComponentsResult updateVisibleComponents(UpdateVisibleComponentsArg arg) {
        UpdateVisibleComponentsResult result = new UpdateVisibleComponentsResult();
        if (null == arg || null == arg.getVersion()) {
            logger.warn("updateVisibleComponents param error! arg[{}]", arg);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        if (arg.getComponentIds() == null) { //如果参数为空则修正为空list
            arg.setComponentIds(new ArrayList<>());
        }
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        String fsUserId = fsUser.asStringUser();
        BaseResult<List<String>> listBaseResult = fsUserVisibleConfigService.loadVisibleComponentIds(fsUserId);
        if (!listBaseResult.isSuccess()) {
            logger.error("loadVisibleComponentIds failed, arg=[{}], result=[{}]", arg, listBaseResult);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
        }
        List<String> componentIds = listBaseResult.getResult();
        if (!CollectionUtils.isEmpty(componentIds)) {
            final String bigVersion = BizCommonUtil.getBigVersion(arg.getVersion());
            componentIds.forEach(componentId -> {
                if (!arg.getComponentIds().contains(componentId) && openAppComponentManager.isFilterComponent(bigVersion, componentId)) {
                    arg.getComponentIds().add(componentId);
                }
            });
        }

        BaseResult<Void> voidBaseResult = fsUserVisibleConfigService.saveFsUserVisibleComponentIds(fsUserId, arg.getComponentIds());

        if (!voidBaseResult.isSuccess()) {
            logger.error("saveFsUserVisibleComponentIds failed,arg={} result={}", arg, voidBaseResult);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        //重置组件缓存，设置缓存数据。
        try {
            checkAppUpdatedManager.resetModuleCache(fsUser, ModuleKeyEnum.COMPONENTS);
        } catch (BizException e) {
            logger.warn("resetModuleCache failed! fsUser[{}]", fsUser.asStringUser());
        }

        result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
        return result;
    }

    @Override
    public QueryVisibleByAuthKeyResult queryVisibleByAuthKey(QueryVisibleByAuthKeyArg arg) {
        QueryVisibleByAuthKeyResult result = new QueryVisibleByAuthKeyResult();
        if (null == arg) {
            logger.warn("queryVisibleByAuthKey param error! arg[null]");
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        String componentId = BizCommonUtil.authKeyConfig.get(arg.getAuthKey());
        if (StringUtils.isBlank(componentId)) {
            logger.error("queryVisibleByAuthKey param error! componentId[{}]", componentId);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());
        BaseResult<Boolean> booleanBaseResult = openFsUserAppViewService.canAccessComponent(fsUser, componentId);
        if (!booleanBaseResult.isSuccess()) {
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            logger.error("openFsUserAppViewService.canAccessComponent failed,arg={} ,user= {} result={}", arg, fsUser, booleanBaseResult);
            return result;
        }
        result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
        result.setVisibleStatus(AppBizConstant.NO);
        if (booleanBaseResult.getResult()) {
            result.setVisibleStatus(AppBizConstant.YES);
        }
        return result;
    }

    @Override
    public GetServiceNumberListResult getServiceNumberList(GetServiceNumberListArg arg) {
        GetServiceNumberListResult result = new GetServiceNumberListResult();
        if (null == arg || null == arg.getVersion()) {
            logger.warn("getServiceNumberList param error! arg[{}]", arg);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());

        result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
        result.setModuleKey(ModuleKeyEnum.SERVICE_NUMBER.getModuleKey());
        result.setTag(checkAppUpdatedManager.getOrGenerateTag(fsUser, arg.getVersion(), ModuleKeyEnum.SERVICE_NUMBER));

        List<ServiceNumberVO> serviceNumberVOList;
        long fcpClientVersion = FCPClientUtil.getVersionCode();
        try {
            // 终端版本低于6.0走老逻辑，新逻辑服务号列表包括内部服务号及互联服务号，并且按创建时间降序排
            if (fcpClientVersion >= 600000) {
                serviceNumberVOList = getServiceNumberVOsVersionGT60(fsUser);
            } else {
                serviceNumberVOList = getServiceNumberVOsVersionLT60(fsUser);
            }
        } catch (BizException e) {
            result.setCode(Integer.toString(e.getErrCode()));
            return result;
        }
        result.setServiceNumberList(serviceNumberVOList);
        return result;
    }

    // 获取高于6.0.0版本的服务号列表
    private List<ServiceNumberVO> getServiceNumberVOsVersionGT60(FsUserVO user) {
        List<ServiceNumberVO> serviceNumberVOList = new ArrayList<>();
        List<String> appViewIds = Lists.newArrayList();

        // 获取可见范围服务号组件
        List<UserCanViewListVO> serviceViewList = queryComponentsByUserAndType(user, AppAccessTypeEnum.SERVICE);
        if (!CollectionUtils.isEmpty(serviceViewList)) {
            appViewIds.addAll(serviceViewList.stream().map(UserCanViewListVO::getAppId).collect(Collectors.toList()));
        }
        List<UserCanViewListVO> linkServiceViewList = queryComponentsByUserAndType(user, AppAccessTypeEnum.LINK_SERVICE);
        if (!CollectionUtils.isEmpty(linkServiceViewList)) {
            appViewIds.addAll(linkServiceViewList.stream().map(UserCanViewListVO::getAppId).collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(appViewIds)) {
            return serviceNumberVOList;
        }

        AppListResult loadOpenAppByIdsFastResult = openAppService.loadOpenAppByIdsFast(appViewIds);
        if (!loadOpenAppByIdsFastResult.isSuccess()) {
            logger.error("openAppService.loadOpenAppByIdsFast failed,arg={} result={}", appViewIds, loadOpenAppByIdsFastResult);
            throw new BizException(loadOpenAppByIdsFastResult);
        }
        List<OpenAppDO> openAppDOs = loadOpenAppByIdsFastResult.getResult();

        // 按创建时间排序
        openAppDOs = openAppDOs.stream().sorted(Comparator.comparing(BaseDO::getGmtCreate).reversed()).collect(Collectors.toList());

        for (OpenAppDO openAppDO : openAppDOs) {
            ServiceNumberVO snVo = new ServiceNumberVO();
            snVo.setAppId(openAppDO.getAppId());
            snVo.setAppName(openAppDO.getServiceName());
            snVo.setDesc(openAppDO.getAppDesc());
//            snVo.setImageUrl(queryAppIconUrl(openAppDO.getAppId(), IconType.SERVICE));
            snVo.setAppType(openAppDO.getAppType());
            snVo.setServiceType(openAppDO.getAppType());
            Integer recommended = null;
            if (!StringUtils.isBlank(openAppDO.getProperties())){
                try {
                    recommended =  OpenAppProperties.fromJson(openAppDO.getProperties()).getIsRecommendedApp();
                } catch (Exception e) {
                    logger.error("openAppDO.getIsRecommendedApp failed,openAppDO={}", openAppDO, e);
                }
            }
            snVo.setRecommended((recommended != null && recommended == 1) ? 1 : 2);
            serviceNumberVOList.add(snVo);
        }

        List<String> appIds = serviceNumberVOList.stream().map(ServiceNumberVO::getAppId).collect(Collectors.toList());
        Map<String, String> appIdIconUrlMap = appIconManager.batchGetIconUrl(appIds, IconType.SERVICE);
        serviceNumberVOList.forEach(serviceNumberVO -> {
            String iconUrl = appIdIconUrlMap.get(serviceNumberVO.getAppId());
            if (StringUtils.isNotBlank(iconUrl)) {
                serviceNumberVO.setImageUrl(iconUrl);
            }
        });

        return serviceNumberVOList;
    }

    // 获取低于6.0.0版本的服务号列表
    private List<ServiceNumberVO> getServiceNumberVOsVersionLT60(FsUserVO user) {
        List<ServiceNumberVO> serviceNumberVOList = Lists.newArrayList();
        List<String> appViewIds = Lists.newArrayList();
        // 获取可见范围服务号组件
        List<UserCanViewListVO> serviceViewList = queryComponentsByUserAndType(user, AppAccessTypeEnum.SERVICE);
        if (!CollectionUtils.isEmpty(serviceViewList)) {
            appViewIds.addAll(serviceViewList.stream().map(UserCanViewListVO::getAppId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(appViewIds)) {
            return serviceNumberVOList;
        }

        AppListResult loadOpenAppByIdsFastResult = openAppService.loadOpenAppByIdsFast(appViewIds);
        if (!loadOpenAppByIdsFastResult.isSuccess()) {
            logger.error("openAppService.loadOpenAppByIdsFast failed,arg={} result={}", appViewIds, loadOpenAppByIdsFastResult);
            throw new BizException(loadOpenAppByIdsFastResult);
        }
        List<OpenAppDO> openAppDOs = loadOpenAppByIdsFastResult.getResult();

        //按 数字，特殊字符，英文字母或者中文首字母排序
        openAppDOs.sort(Comparator.comparing(h -> SortUtils.sortFirst(h.getAppName())));

        for (OpenAppDO openAppDO : openAppDOs) {
            ServiceNumberVO snVo = new ServiceNumberVO();
            snVo.setAppId(openAppDO.getAppId());
            snVo.setAppName(openAppDO.getServiceName());
            snVo.setDesc(openAppDO.getAppDesc());
            snVo.setImageUrl(queryAppIconUrl(openAppDO.getAppId(), IconType.SERVICE));
            //兼容旧版手机端，服务号详情页面无法进入服务号bug
            snVo.setAppType(5 == openAppDO.getAppType() ? 1 : openAppDO.getAppType());
            Integer recommended = null;
            if (!StringUtils.isBlank(openAppDO.getProperties())){
                try {
                    recommended =  OpenAppProperties.fromJson(openAppDO.getProperties()).getIsRecommendedApp();
                } catch (Exception e) {
                    logger.error("openAppDO.getIsRecommendedApp failed,openAppDO={}", openAppDO, e);
                }
            }
            snVo.setRecommended((recommended != null && recommended == 1) ? 1 : 2);
            serviceNumberVOList.add(snVo);
        }
        return serviceNumberVOList;
    }

    private List<UserCanViewListVO> queryComponentsByUserAndType(FsUserVO user, AppAccessTypeEnum appAccessTypeEnum) {
        com.facishare.open.app.center.api.result.BaseResult<List<UserCanViewListVO>> baseResult = openFsUserAppViewService.queryComponentsByFsUser(user, appAccessTypeEnum);
        if (!baseResult.isSuccess()) {
            logger.error("openFsUserAppViewService.queryComponentsByFsUser failed. user=[{}], AppAccessTypeEnum=[{}], result=[{}]", user, appAccessTypeEnum, baseResult);
            throw new BizException(baseResult);
        }
        return baseResult.getResult();
    }

    @Override
    public QueryCrmAvailResult queryCrmAvailability(QueryCrmAvailArg arg) {
        QueryCrmAvailResult result = new QueryCrmAvailResult();
        if (null == arg || null == arg.getVersion()) {
            logger.warn("queryCrmAvailability param error! arg[{}]", arg);
            result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
            return result;
        }
        FsUserVO fsUser = new FsUserVO(FcpServiceContextUtil.getEmployeeFullId());

        final com.facishare.open.app.center.api.result.BaseResult<Boolean> accessResult =
                openFsUserAppViewService.canAccessComponent(fsUser, BizCommonUtil.FS_BROKER_CRM_COMPONENT_ID);
        if (!accessResult.isSuccess()) {
            logger.warn("fail to call openFsUserAppViewService.canAccessComponent, user={}, componentId={}",
                    fsUser, BizCommonUtil.FS_BROKER_CRM_COMPONENT_ID);
            result.setCode(Integer.toString(accessResult.getErrCode()));
            return result;
        }
        result.setCode(ResultCodeEnum.SUCCESS.getStringCode());
        result.setAvailability(accessResult.getResult() ? AppBizConstant.YES : AppBizConstant.NO);
        return result;
    }

    @Override
    public BaseFcpResult cleanNewStatus(CleanNewStatusArg arg) {
        BaseFcpResult result = new BaseFcpResult();
        try {
            if (null == arg || Strings.isNullOrEmpty(arg.getComponentId())) {
                logger.warn(" CleanNewStatusArg param error! arg[{}]", arg);
                result.setCode(ResultCodeEnum.PARAM_ERROR.getStringCode());
                result.setErrorMessage("componentId is null or empty");
                return result;
            }

            String fsUserId = FcpServiceContextUtil.getEmployeeFullId();

            FsUserVO fsUserVO = new FsUserVO(fsUserId);
            BaseResult<Boolean> cancelNewStateAndGetResult = newComponentService
                    .cancelNewStateAndGet(fsUserVO, arg.getComponentId());
            if (!cancelNewStateAndGetResult.isSuccess()) {
                logger.warn(
                        "newComponentService.cancelNewStateAndGet failed ,user [{}], componentId [{}] ,result [{}] ",
                        fsUserId,
                        arg.getComponentId(), cancelNewStateAndGetResult);
                result.setErrorMessage("newComponentService.cancelNewStateAndGet error");
                result.setCode(ResultCodeEnum.FAIL.getStringCode());
                return result;
            }
            result.setCode(ResultCodeEnum.SUCCESS.getStringCode());

        } catch (Exception e) {
            logger.error("cleanNewStatus failed. arg:[{}]", arg, e);
            result.setErrorMessage("cleanNewStatus error");
            result.setCode(ResultCodeEnum.FAIL.getStringCode());
        }

        return result;
    }


    /**
     * 查询iconUrl
     * @param appIdOrComponentId 应用或者组件ID
     * @param type 图片类型
     * @return iconUrl
     */
    private String queryAppIconUrl(String appIdOrComponentId, IconType type){
        BaseResult<String> iconUrlResult = appIconService.queryAppIconUrl(appIdOrComponentId, type);
        if (!iconUrlResult.isSuccess()) {
            logger.warn("queryAppIconUrl failed, appIdOrComponentId[{}], type[{}], result[{}] : " + appIdOrComponentId, type, iconUrlResult);
        }
        return iconUrlResult.getResult();
    }
}
