package com.facishare.open.center.fcp.controller;

import com.facishare.fcp.protocol.*;
import com.facishare.fcp.serialization.SerializerType;
import com.facishare.fcp.service.FcpApplication;
import com.facishare.open.center.fcp.SimpleFcpClient;
import com.facishare.open.center.fcp.model.args.QueryComponentListByTypeArg;
import com.facishare.open.center.fcp.model.args.SaveComponentSortArg;
import com.facishare.open.center.fcp.model.results.QueryComponentListByTypeResult;
import com.facishare.open.center.fcp.model.results.SaveComponentSortResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by liq<PERSON><PERSON> on 2016/9/27.
 *
 * <AUTHOR>
 * @since 2016/9/27
 */
@Ignore
public class OpenAppComponentActionTest {
    private SimpleFcpClient client;
    private AtomicInteger seqId = new AtomicInteger();

    @Before
    public void setUp() throws Exception {
        client = new SimpleFcpClient("172.31.105.109", 29101);
//        client = new SimpleFcpClient();
        client.connect();
    }

    @Test
    public void queryComponentListByType_Success() {
        QueryComponentListByTypeArg queryComponentListByTypeArg = new QueryComponentListByTypeArg();
        queryComponentListByTypeArg.setVersion("5.4");
        queryComponentListByTypeArg.setType("mainPage");
        byte[] arg = FcpApplication.getInstance().getSerializerManager()
                .getSerializer(SerializerType.PROTO_BUF.value()).encode(queryComponentListByTypeArg);

        FcpMessageBuilder<FcpRequest> requestBuilder = FcpMessageBuilder.createFcpRequestBuilder(FcpMessageType.QUERY)
                .addFcpHeader(FcpHeader.FcpHeaderType.CALL_ID, UUID.randomUUID().toString())
                .addFcpHeader(FcpHeader.FcpHeaderType.SEQUENCE_ID, seqId.incrementAndGet())
                .addFcpHeader(FcpHeader.FcpHeaderType.QUERY_NAME, "TEST.OpenAppComponent.QueryComponentListByType")
                .addFcpHeader(FcpHeader.FcpHeaderType.USER_INFO,"E.fsfte2a.1363")
                .addFcpHeader(FcpHeader.FcpHeaderType.CLIENT_INFO,"IOS.41")
                .addFcpHeader(FcpHeader.FcpHeaderType.CONTENT_TYPE, SerializerType.PROTO_BUF.value())
                .addFcpBody(arg);
        FcpRequest request = requestBuilder.build();

        FcpResponse fcpResponse = client.sendFcpRequest(request);
        System.out.println(fcpResponse);

        QueryComponentListByTypeResult queryComponentListByTypeResult =
                FcpApplication.getInstance().getSerializerManager().getSerializer(SerializerType.PROTO_BUF).
                        decode(QueryComponentListByTypeResult.class, fcpResponse.getMergedBinaryBody());
        System.out.println(queryComponentListByTypeResult);

        Assert.assertTrue(fcpResponse.getMessageType().equals(FcpMessageType.OK));
    }

    @Test
    public void saveComponentSort_Success() {
        SaveComponentSortArg saveComponentSortArg = new SaveComponentSortArg();
        saveComponentSortArg.setVersion("5.4");
        saveComponentSortArg.setComponentIds(Arrays.asList("FSAID_9896c1", "FSAID_9897be"));

        byte[] arg = FcpApplication.getInstance().getSerializerManager()
                .getSerializer(SerializerType.PROTO_BUF.value()).encode(saveComponentSortArg);

        FcpMessageBuilder<FcpRequest> requestBuilder = FcpMessageBuilder.createFcpRequestBuilder(FcpMessageType.QUERY)
                .addFcpHeader(FcpHeader.FcpHeaderType.CALL_ID, UUID.randomUUID().toString())
                .addFcpHeader(FcpHeader.FcpHeaderType.SEQUENCE_ID, seqId.incrementAndGet())
                .addFcpHeader(FcpHeader.FcpHeaderType.QUERY_NAME, "TEST.OpenAppComponent.SaveComponentSort")
                .addFcpHeader(FcpHeader.FcpHeaderType.USER_INFO,"E.fsfte2a.1011")
                .addFcpHeader(FcpHeader.FcpHeaderType.CLIENT_INFO,"IOS.41")
                .addFcpHeader(FcpHeader.FcpHeaderType.CONTENT_TYPE, SerializerType.PROTO_BUF.value())
                .addFcpBody(arg);
        FcpRequest request = requestBuilder.build();

        FcpResponse fcpResponse = client.sendFcpRequest(request);
        System.out.println(fcpResponse);

        SaveComponentSortResult saveComponentSortResult =
                FcpApplication.getInstance().getSerializerManager().getSerializer(SerializerType.PROTO_BUF).
                        decode(SaveComponentSortResult.class, fcpResponse.getMergedBinaryBody());
        System.out.println(saveComponentSortResult);

        Assert.assertTrue(fcpResponse.getMessageType().equals(FcpMessageType.OK));
    }


}
