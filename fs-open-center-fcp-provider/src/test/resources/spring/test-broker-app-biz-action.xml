<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd
       	http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
		http://code.alibabatech.com/schema/dubbo
		http://code.alibabatech.com/schema/dubbo/dubbo.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop.xsd">

    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:dubbo.properties</value>
                <value>classpath:data-access.properties</value>
            </list>
        </property>
    </bean>
	
    <!-- 注入action -->
    <bean name="Messenger" class="com.facishare.open.center.fcp.controller.impl.OpenAppActionImpl" >
         <property name="openFsUserAppViewService" ref="openFsUserAppViewService" /> 
    </bean>

    <bean name="Guide" class="com.facishare.open.center.fcp.controller.impl.OpenAppGuideActionImpl" >
        <property name="openFsUserAppViewService" ref="openFsUserAppViewService" />
    </bean>

    <bean id="logMornitorAspect" class="com.facishare.open.broker.common.aop.ServiceMornitorAspect">
        <constructor-arg name="monitorKeyPrefix" value="open.broker.b1." />
        <property name="reportEnabled" value="true" />
    </bean>

    <aop:config>
        <aop:aspect id="logAspect" ref="logMornitorAspect">
            <aop:pointcut id="businessService" expression="execution(* com.facishare.open.broker.*.service.*.*(..))" />
            <aop:pointcut id="appBusinessService" expression="execution(* com.facishare.open.app.center.api.service.*.*(..))" />
            <aop:pointcut id="appActionBusinessService" expression="execution(* com.facishare.open.center.fcp.controller.*.*(..))" />

            <aop:around pointcut-ref="businessService" method="around"/>
            <aop:around pointcut-ref="appBusinessService" method="around"/>
            <aop:around pointcut-ref="appActionBusinessService" method="around"/>
        </aop:aspect>
    </aop:config>

    <!-- 消费dubbo服务 -->
    <bean id="openFsUserAppViewService" class="org.mockito.Mockito" factory-method="mock">
        <constructor-arg value="com.facishare.open.app.center.api.service.OpenFsUserAppViewService"></constructor-arg>
    </bean>

</beans>
