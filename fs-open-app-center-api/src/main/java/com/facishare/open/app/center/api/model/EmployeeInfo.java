package com.facishare.open.app.center.api.model;

public class EmployeeInfo extends Employee {
    private static final long serialVersionUID = 9024329817216629352L;

    /**
     * 主属部门ID，0表示无主属部门
     */
    private Integer mainCircleId;

    public Integer getMainCircleId() {
        return mainCircleId;
    }

    public void setMainCircleId(Integer mainCircleId) {
        this.mainCircleId = mainCircleId;
    }

    @Override
    public String toString() {
        StringBuilder superStr = new StringBuilder(super.toString());
        superStr.replace(0, 8, "EmployeeInfo");
        int indexOfQQ = superStr.indexOf("qq");
        superStr.insert(indexOfQQ, "mainCircleId=" + mainCircleId + ", ");
        return superStr.toString();
    }
}