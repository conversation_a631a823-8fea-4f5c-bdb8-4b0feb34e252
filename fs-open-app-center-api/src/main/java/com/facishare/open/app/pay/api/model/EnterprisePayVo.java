package com.facishare.open.app.pay.api.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业购买记录.
 * <AUTHOR>
 * @since 2016/3/21 10:33
 */
@Getter
@Setter
@ToString
public class EnterprisePayVo implements Serializable {
    private Long id;
    private String fsEa;
    private Date gmtBegin;
    private Date gmtEnd;
    private Integer status;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        EnterprisePayVo that = (EnterprisePayVo) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (fsEa != null ? !fsEa.equals(that.fsEa) : that.fsEa != null) return false;
        if (gmtBegin != null ? !gmtBegin.equals(that.gmtBegin) : that.gmtBegin != null) return false;
        if (gmtEnd != null ? !gmtEnd.equals(that.gmtEnd) : that.gmtEnd != null) return false;
        return status != null ? status.equals(that.status) : that.status == null;

    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (fsEa != null ? fsEa.hashCode() : 0);
        result = 31 * result + (gmtBegin != null ? gmtBegin.hashCode() : 0);
        result = 31 * result + (gmtEnd != null ? gmtEnd.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        return result;
    }
}
