package com.facishare.open.app.pay.api.enums;


/**
 * 应用购买.
 * <AUTHOR>
 * @since 2016/3/21 17:39
 */
public enum AppPayEnum {
    /**
     * 停用.
     */
    STOPED("停用", 9),

    /**
     * 未购买
     */
    UNPURCHASED("未购买", 0),

    /**
     * 已购买.
     */
    PURCHASED("已购买", 1),

    /**
     * 购买已过期.
     */
    EXPIRED("购买已过期", 2),


    ;
    private String name;
    private Integer value;

    AppPayEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

}
