package com.facishare.open.app.pay.api.enums;

import java.io.Serializable;

/**
 * Created by xialf on 1/27/16.
 *
 * <AUTHOR>
 */
public enum QuotaType implements CodeEnum, Serializable {

    /**
     * 购买.
     */
    PURCHASE(1),

    /**
     * 试用.
     */
    TRIAL(2);

    private final int code;

    QuotaType(int code) {
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }
}
