package com.facishare.open.app.center.api.service.externals;

import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.vo.OpenAppForManageVO;
import com.facishare.open.app.center.api.model.vo.PaasAppViewsForEaVo;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import java.util.List;

/**
 * 提供给paas组调用查询相应的应用组件
 */
public interface IPaasOpenAppViewService {
    /**
     * 查后端管理应用（基础应用、扩展应用、自建应用）列表
     * 740需求：http://wiki.firstshare.cn/pages/viewpage.action?pageId=135686031
     * @param fsUserVO
     * @param lang
     * @return
     */
    BaseResult<List<OpenAppForManageVO>> queryAppListForManage(FsUserVO fsUserVO, String lang);

    /**
     * 通过企业号以及终端信息查询应用列表
     * @param fsEa 企业账号
     * @param appAccessTypeEnum 客户端类型
     * @param lang 国际化信息
     * @return
     */
    BaseResult<List<PaasAppViewsForEaVo>> queryPaasAppViewsForEaVo(String fsEa, AppAccessTypeEnum appAccessTypeEnum, String lang);

    /**
     * 通过用户信息以及终端信息查询应用列表
     * @param fsUserVO
     * @param appAccessTypeEnum
     * @param lang 国际化信息
     * @return
     */
    BaseResult<List<PaasAppViewsForEaVo>> queryPaasAppViewsForUserVo(FsUserVO fsUserVO, AppAccessTypeEnum appAccessTypeEnum, String lang);
}
