package com.facishare.open.app.pay.mq.api.item;

import com.facishare.open.app.pay.mq.api.item.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * Created by linchf on 2017/7/13.
 */
@Getter
@Setter
@ToString
public class ExperienceAccountCopyDataItem extends ProtoBase implements Serializable {

    private static final long serialVersionUID = 7613090561239659795L;

    public static final int FLAG = 1;

    /**
     * 来源企业id
     */
    @Tag(1)
    private Integer fromEnterpriseId;

    @Tag(2)
    private String fromEnterpriseAccount;
    /**
     * 目的企业id
     */
    @Tag(3)
    private Integer toEnterpriseId;

    @Tag(4)
    private String toEnterpriseAccount;

    public ExperienceAccountCopyDataItem() {
    }

    public ExperienceAccountCopyDataItem(Integer fromEnterpriseId, String fromEnterpriseAccount,
                                         Integer toEnterpriseId, String toEnterpriseAccount) {
        this.fromEnterpriseId = fromEnterpriseId;
        this.fromEnterpriseAccount = fromEnterpriseAccount;
        this.toEnterpriseId = toEnterpriseId;
        this.toEnterpriseAccount = toEnterpriseAccount;
    }
}
