package com.facishare.open.operating.center.api.service;

import com.facishare.dubbo.plugin.annotation.RestAction;
import com.facishare.open.app.center.mq.item.AppAdminItem;
import com.facishare.open.app.center.mq.item.AppOrServiceCreateItem;
import com.facishare.open.app.center.mq.item.CustomAppAdminItem;
import com.facishare.open.app.center.mq.item.ServiceChangeItem;

import java.util.List;

/**
 * 发消息
 *
 * <AUTHOR>
 * @date 2019/1/11
 */
public interface OperatingAppMessageService {

    /**
     * 自定义应用管理员变更
     *
     * @param customAppAdminItem 事件
     * @throws Exception
     */
    void adminsChanged(CustomAppAdminItem customAppAdminItem);

    /**
     * 创建服务号，自定义应用
     *
     * @param appOrServiceCreateItem 事件
     * @throws Exception
     */
    void appOrServiceCreate(AppOrServiceCreateItem appOrServiceCreateItem);


    /**
     * 移动客服开关
     *
     * @param serviceChangeItem
     */
    void serviceNumberStatusChange(ServiceChangeItem serviceChangeItem);

    /**
     * 服务号，管理员变更
     *
     * @param appAdminItem 事件
     * @throws Exception
     */
    @RestAction("adminsChangedAction")
    void adminsChanged(AppAdminItem appAdminItem);
}
