package com.facishare.open.operating.center.api.model;

import java.io.Serializable;

/**
 * Created by huyue on 2016/8/16.
 */
public class OperationLogVO implements Serializable {

    private String enterpriseAccount;   // 企业号
    private String appId;               // 应用id
    private Integer operatorId;         // 操作者的ID
    private String operatorName;        // 操作者姓名
    private String operateTime;         // 操作时间
    private String operateContent;      // 操作内容

    public String getEnterpriseAccount() {
        return enterpriseAccount;
    }

    public void setEnterpriseAccount(String enterpriseAccount) {
        this.enterpriseAccount = enterpriseAccount;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }


    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperateContent() {
        return operateContent;
    }

    public void setOperateContent(String operateContent) {
        this.operateContent = operateContent;
    }

    @Override
    public String toString() {
        return "OperationLogVO{" +
                " enterpriseAccount='" + enterpriseAccount + '\'' +
                " appId='" + appId + '\'' +
                " operatorId='" + operatorId + '\'' +
                " operatorName='" + operatorName + '\'' +
                ", operateTime='" + operateTime + '\'' +
                ", operateContent='" + operateContent + '\'' +
                '}';
    }
}
