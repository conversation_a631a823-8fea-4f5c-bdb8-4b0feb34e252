package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Lists;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/1/16 19:23
 */
public interface DelFuncCodeRoles {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private List<String> funcSet;

    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private boolean success;
    }
}
