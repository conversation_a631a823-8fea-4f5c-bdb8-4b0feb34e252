package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Lists;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/2/24 10:28
 */
public interface UpdateFunc {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private CreateFunctionPrivilege.FunctionPojo functionPojo;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private boolean success;
    }
}
