package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;

public interface QueryDataAuth {

  @Data
  class Arg{
    //企业ID
    private String tenantId;

    //员工ID
    private String userId;

    //员工姓名
    private String userName;

    //外部企业ID
    private String outTenantId;

    //外部员工ID
    private String outUserId;

    //外部员工姓名
    private String outUserName;

    //语言
    private String lang;

    //对象描述ApiName
    private String describeApiName;

    //数据ID
    private String dataId;

    //数据主属性
    private String name;

    //业务类型
    private String recordType;
  }

}
