package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.GetCustomInitRoleActionCodes;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.IsAdminInitByDefault;
import lombok.Builder;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.GetSupportedActionCodes;

@Builder
public class RemoteFunctionPrivilegeProvider implements FunctionPrivilegeProvider {

    private String describeApiName;
    private FunctionPrivilegeProviderProxy proxy;

    @Override
    public String getApiName() {
        return describeApiName;
    }

    @Override
    public List<String> getSupportedActionCodes() {
        GetSupportedActionCodes.Arg arg = GetSupportedActionCodes.Arg.builder()
                .describeApiName(describeApiName)
                .build();
        Map<String, String> headers = RestUtils.buildHeaders(describeApiName);
        return proxy.getSupportedActionCodes(arg, headers).getData().getSupportedActionCodes();
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        GetCustomInitRoleActionCodes.Arg arg = GetCustomInitRoleActionCodes.Arg.builder()
                .describeApiName(describeApiName)
                .build();
        Map<String, String> headers = RestUtils.buildHeaders(describeApiName);
        return proxy.getCustomInitRoleActionCodes(arg, headers).getData().getCustomInitRoleActionCodes();
    }

    @Override
    public boolean isAdminInitByDefault() {
        IsAdminInitByDefault.Arg arg = IsAdminInitByDefault.Arg.builder()
                .describeApiName(describeApiName)
                .build();
        Map<String, String> headers = RestUtils.buildHeaders(describeApiName);
        return Optional.of(proxy.isAdminInitByDefault(arg, headers).getData().getIsAdminInitByDefault()).orElse(true);
    }
}

