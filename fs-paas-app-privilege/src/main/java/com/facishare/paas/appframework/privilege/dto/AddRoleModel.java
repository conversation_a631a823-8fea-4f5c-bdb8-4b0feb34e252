package com.facishare.paas.appframework.privilege.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/5/28.
 */
public interface AddRoleModel {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;

        private String roleCode;
        private String roleName;
        private String description;
        private Integer roleType;
    }

    @Data
    class Result {
        int errCode;
        String errMessage;
        String result;
        boolean success;
    }
}
