package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface FindDefaultRoleView {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private String recordTypeId;
        private String entityId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private List<FindView.RoleViewPojo> result;
    }

    @Data
    class RoleViewPojo {
        private String id;
        private String tenantId;
        private String appId;
        private String roleCode;
        private String entityId;
        private String viewId;
        private String recordTypeId;
    }

}
