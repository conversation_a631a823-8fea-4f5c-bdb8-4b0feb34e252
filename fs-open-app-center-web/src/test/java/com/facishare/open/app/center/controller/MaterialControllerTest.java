package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.material.api.enums.CreatorTypeEnum;
import com.facishare.open.material.api.model.vo.ImageTextVO;
import com.facishare.open.material.api.service.MaterialService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import test.facishare.open.app.center.controller.ControllerBaseUnitTest;

import static org.junit.Assert.*;

/**
 * Created by zenglb on 2015/12/29.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:web-unit-test.xml")
public class MaterialControllerTest extends ControllerBaseUnitTest {

//    private final FsUserVO adminUser = FsUserVO.toFsUserVO("E.fsfte2a.100");
//    private final String appId  = "testAppId";

    @Autowired
    private MaterialController materialController;

    @Autowired
    private MaterialService materialService;

    @Test
    public void testQueryPageAndAppId() throws Exception {
        Mockito.when(openAppAdminService.isAppAdmin(FsUserVO.toFsUserString(ADMIN_USER_VO), APP_ID)).thenReturn(new BaseResult(true));

        Pager<ImageTextVO> imageTextVOPager = new Pager<ImageTextVO>();
        com.facishare.open.common.result.BaseResult<Pager<ImageTextVO>> imageTextVOBaseResult = new BaseResult<Pager<ImageTextVO>>();
        imageTextVOBaseResult.setResult(imageTextVOPager);

        Mockito.when(materialService.findImageTextByAppId(Mockito.any(Pager.class), Mockito.eq(APP_ID), Mockito.eq(CreatorTypeEnum.APP_ADMIN), Mockito.eq(ADMIN_USER_VO)))
                .thenReturn(imageTextVOBaseResult);

        AjaxResult ajaxResult = materialController.queryPageAndAppId(ADMIN_USER_VO, 1, 10, APP_ID);
        assertEquals(AjaxCode.OK,ajaxResult.getErrCode().intValue());
    }

    @Test
    public void testLoadMaterialById() throws Exception {

    }

    @Test
    public void testDeleteMaterialById() throws Exception {

    }

    @Test
    public void testCreateMaterial() throws Exception {

    }

    @Test
    public void testUpdateMaterial() throws Exception {

    }

    @Test
    public void testUploadImageAndId() throws Exception {

    }
}