package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.common.model.FsUserVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date on 2017/5/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-common-web-test.xml")
public class AppViewControllerTest {


    @Resource
    private AppViewController appViewController;

    @Test
    public void queryComponentListByFsUser(){
        FsUserVO fsUserVO = new FsUserVO();
        fsUserVO.setEnterpriseAccount("53432");
        fsUserVO.setUserId(1018);
        AjaxResult ajaxResult = appViewController.queryComponentListByFsUser(fsUserVO, "zh-CN");
        System.out.println(ajaxResult);

    }


}
