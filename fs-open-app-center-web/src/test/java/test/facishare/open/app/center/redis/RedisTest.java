package test.facishare.open.app.center.redis;

import com.github.jedis.support.MergeJedisCmd;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import test.BaseTest;

import javax.annotation.Resource;
import java.util.Random;

/**
 * <AUTHOR>
 * @date on 2016/1/11.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:redis-test.xml")
public class RedisTest extends BaseTest {


    @Resource
    private MergeJedisCmd jedis;

    private static String key = "test.1.2";
    private static String key2 = "test.1.2.3";


    @Test
    public void testGet() throws Exception {

        jedis.set(key, "tab");

        String s = jedis.get(key);
        Assert.assertTrue("tab".equals(s));

        String s1 = key2 + new Random().nextInt();

        Long incr1 = jedis.incr(s1);
        Long incr2 = jedis.incr(s1);
        Long incr3 = jedis.incr(s1);
        Long incr4 = jedis.incr(s1);
        Long incr5 = jedis.incr(s1);

        assertTrue(incr5 != null & incr5 % 5 == 0);

        Long incr6 = jedis.incr(s1);

        assertTrue(incr6 != null & incr6 % 5 != 0);

    }

}
