package test.facishare.open.app.center.manager;

import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.enums.IconType;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.AppIconService;
import com.facishare.open.app.center.manager.AppManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import test.BaseMockTest;

import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:web-unit-test.xml"})

public class AppManagerTest extends BaseMockTest {
    @Autowired
    protected AppIconService appIconService;

    @Autowired
    private AppManager appManager;

    @Test
    public void testCreateCustomApp() throws Exception {

    }

    @Test
    public void testUpdateApp() throws Exception {

    }

    @Test
    public void testUpdateCustomAppLogo() throws Exception {

    }

    @Test
    public void testUpdateCustomAppAdmins() throws Exception {

    }

    @Test
    public void testUpdateComponentView() throws Exception {

    }

    @Test
    public void testLoadAppList() throws Exception {

    }

    @Test
    public void testLoadAppByAppId() throws Exception {

    }

    @Test
    public void testGetAppDevModeInfo() throws Exception {

    }

    @Test
    public void testCreateCustomComponent() throws Exception {

    }

    @Test
    public void testLoadAppDtlByComponentId() throws Exception {
        Mockito.when(openAppComponentService.loadOpenAppComponentById(fsUser, componentApp.getComponentId()))
                .thenReturn(new BaseResult<OpenAppComponentDO>(componentApp));
        Mockito.when(openAppService.loadOpenApp(componentApp.getAppId())).thenReturn(new AppResult(app));

        Mockito.when(appIconService.queryAppIconUrl(app.getAppId(), IconType.WEB)).thenReturn(new BaseResult("iconUrl"));

        Map<String, Object> map = appManager.loadAppDtlByComponentId(fsUser, componentApp.getComponentId());
        assertNotNull(map);
        assertFalse(map.isEmpty());
        assertEquals(componentApp.getComponentId(), map.get("componentId"));
        assertEquals(app.getAppName(), map.get("appName"));
        assertEquals(app.getAppType(), map.get("appType"));
        assertEquals(app.getAppDesc(), map.get("appDesc"));
        assertEquals(null, map.get("appClassName"));
        assertEquals(app.getAppClass(), map.get("appClass"));
        assertEquals(componentApp.getComponentName(), map.get("componentName"));
        assertEquals(app.getGmtCreate(), map.get("gmtCreateDate"));
        assertEquals(app.getOpenDevDO().getDevName(), map.get("devName"));
        assertNotNull(map.get("appLogoUrl"));
    }
}