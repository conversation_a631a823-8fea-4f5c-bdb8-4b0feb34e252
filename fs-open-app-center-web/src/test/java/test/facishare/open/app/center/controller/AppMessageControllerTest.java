package test.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.controller.AppMessageController;
import com.facishare.open.app.center.model.AppMessageVO;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * 简单的集成测试，需要从浏览器Cookie中获取TOKEN
 * Created by xialf on 2015/8/27.
 */
@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-dubbo-test.xml")
public class AppMessageControllerTest {
    @Resource
    private AppMessageController appMessageController;

    private static final String APP_ID = "FSAID_1993983";

    @Ignore //TODO &lambo 不能跑 call xialf
    @Test
    public void testSendMsg_Success() {
        AppMessageVO appMessageVO = new AppMessageVO();
        appMessageVO.setAppId(APP_ID);
        appMessageVO.setTargets("{\"department\":[],\"member\":[1000]}");
        appMessageVO.setContent("Hello China From Jacob");

        AjaxResult ajaxResult = appMessageController.sendTextMsg(null, appMessageVO);
        System.out.println(ajaxResult);

        appMessageVO.setTargets("{\"department\":[\"999999\"],\"member\":[]}");
        appMessageVO.setContent("飞洒非法手段[呲牙]发士大夫撒旦");
        ajaxResult = appMessageController.sendTextMsg(null, appMessageVO);
        System.out.println(ajaxResult);
    }


    @Ignore //TODO &lambo 不能跑 call xialf
    @Test
    public void testQueryView_Success() {
        AjaxResult ajaxResult = appMessageController.queryView(null, APP_ID);
    }
}
