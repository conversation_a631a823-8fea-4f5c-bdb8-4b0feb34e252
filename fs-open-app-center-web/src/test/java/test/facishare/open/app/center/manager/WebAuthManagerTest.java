//package test.facishare.open.app.center.manager;
//
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import test.BaseTest;
//
//import com.facishare.open.addressbook.api.EmployeeService;
//import com.facishare.open.addressbook.model.MetaParam;
//import com.facishare.open.addressbook.result.BeanResult;
//import com.facishare.open.app.center.api.model.FsUserVO;
//import com.facishare.open.app.center.manager.OperatingWebAuthManager;
//import com.facishare.open.broker.login.model.GetUserBySSOTokenResult;
//import com.facishare.open.broker.login.service.FsSigService;
//
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath:test-open-app-center-web.xml")
//public class WebAuthManagerTest extends BaseTest {
//	@Autowired
//	@InjectMocks
//	private OperatingWebAuthManager webAuthManager;
//	@Autowired
//	private EmployeeService employeeService;
//	@Autowired
//	private FsSigService fsSigService;
//	
//	static final String token = "xx";
//	@Before
//	public void init(){
//		GetUserBySSOTokenResult getUserBySSOTokenResult = new GetUserBySSOTokenResult();
//		getUserBySSOTokenResult.setEnterpriseAccount("a");
//		getUserBySSOTokenResult.setUserId(1);
//		Mockito.when(fsSigService.checkCredential(token)).thenReturn(getUserBySSOTokenResult);
//		MetaParam metaParam = new MetaParam("a",1);
//		Mockito.when(employeeService.isAdmin(metaParam, 1)).thenReturn(new BeanResult<Boolean>(true));
//	}
//	
//	@Test
//	public void test_checkAndLoadWebUser() throws Exception {
//		FsUserVO user = webAuthManager.checkAndLoadWebUser(token, false);
//		assertNotNull(user);
//		assertEquals(user.getUserId(), 1);
//	}
//}
