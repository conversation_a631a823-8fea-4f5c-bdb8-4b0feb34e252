//package test.facishare.open.app.center.controller;
//
//import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
//import com.facishare.open.app.center.api.model.vo.FsUserVO;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import test.BaseTest;
//
//import com.facishare.open.addressbook.api.EmployeeService;
//import com.facishare.open.addressbook.model.MetaParam;
//import com.facishare.open.addressbook.result.BeanResult;
//import com.facishare.open.app.center.ajax.result.AjaxResult;
//import com.facishare.open.app.center.api.model.AppViewDO;
//import com.facishare.open.app.center.api.model.OpenAppDO;
//import com.facishare.open.app.center.api.model.enums.AppCenterEnum.ViewType;
//import com.facishare.open.app.center.api.result.AppResult;
//import com.facishare.open.app.center.api.result.AppViewResult;
//import com.facishare.open.app.center.api.result.IntegerResult;
//import com.facishare.open.app.center.api.service.OpenAppService;
//import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
//import com.facishare.open.app.center.api.service.OpenFsUserBindAppService;
//import com.facishare.open.app.center.controller.AppController;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath:test-open-app-center-web.xml")
//public class AppControllerTest extends BaseTest {
//	@InjectMocks
//	@Autowired
//	private AppController appController;
//	@Autowired
//	private OpenAppService openAppService;
//	@Autowired
//	private EmployeeService employeeService;
//	@Autowired
//	private OpenFsUserAppViewService openFsUserAppViewService;
//	@Autowired
//	private OpenFsUserBindAppService openFsUserBindAppService;
//	private static final String token = "xx";
//
//	@Before
//	public void setUp() {
//		String ea = "fssdetest";
//		int userId = 109;
//		String appId = "appId_001";
//
//		MetaParam metaParam = new MetaParam(ea, userId);
//		Mockito.when(employeeService.isAdmin(metaParam, userId)).thenReturn(new BeanResult<Boolean>(true));
//		FsUserVO user = new FsUserVO(ea, userId);
//		AppViewDO viewDO = new AppViewDO();
//		Mockito.when(openFsUserAppViewService.loadAppViewByType(user, appId, AppComponentTypeEnum.WEB)).thenReturn(
//				new AppViewResult(viewDO));
//		Mockito.when(openFsUserAppViewService.loadAppViewByType(user, appId, AppComponentTypeEnum.APP)).thenReturn(
//				new AppViewResult(viewDO));
//		Mockito.when(openFsUserBindAppService.queryAppBindStatus(user, appId)).thenReturn(new IntegerResult(1));
//
//	}
//
//	@Test
//	public void test_loadAppByAppId() {
//
//		String appId = "appId_001";
//		try {
//			OpenAppDO app = new OpenAppDO();
//			app.setAppMode(1);
//			AppResult answer = new AppResult(app);
//			Mockito.when(openAppService.loadOpenApp(appId)).thenReturn(answer);
//			AjaxResult result = appController.loadAppByAppId(null, appId);
//			assertEquals(Integer.valueOf(0), result.getErrCode());
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//}
