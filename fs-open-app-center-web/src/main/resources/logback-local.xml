<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy/MM/dd-HH:mm:ss.SSS} [%thread] [%level] %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.facishare" level="DEBUG" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>
    <logger name="com.alibaba.druid" level="DEBUG" additivity="false">
    	<appender-ref ref="STDOUT" />
    </logger>
    <logger name="com.github.trace" level="OFF" additivity="false">
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>