package com.facishare.open.app.center.model;

import com.facishare.open.app.pay.api.enums.PayStatus;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryAppsVO implements Serializable {

    /**
     * appId
     */
    private String appId;

    /**
     * app名称
     */
    private String appName;

    /**
     * 应用类型.1 自定义应用 2 开发者应用 3 基础应用
     *
     * @see com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType
     */
    private Integer appType;

    private String appLogoUrl;

    /**
     * 绑定状态,主要相对于企业的应用.1.为绑定.2为停用
     */
    private Integer bindStatus;

    /**
     * 1 付费 2免费
     * @see com.facishare.open.app.center.api.model.enums.PayTypeEnum
     *
     */
    private Integer payType;

    /**
     * @see PayStatus
     */
    private Integer payStatus = null;
}
