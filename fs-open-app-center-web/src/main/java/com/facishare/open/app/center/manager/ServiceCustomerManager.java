package com.facishare.open.app.center.manager;

import com.facishare.open.app.center.api.model.vo.OpenCustomerVO;
import com.facishare.open.app.center.model.OpenCustomerForm;
import com.facishare.open.app.center.model.outers.args.OpenCustomerQueryPagerArgs;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.proxy.model.wx.IndividualQrResult;

import java.util.List;
import java.util.Map;

/**
 * 开平客服
 * Created by chenzs on 2016/11/2.
 */
public interface ServiceCustomerManager {

    /**
     * 分页加载客服信息
     *
     * @param fsUserVO                   纷享用户
     * @param openCustomerQueryPagerArgs
     * @return
     */
    Pager<OpenCustomerVO> queryPager(final FsUserVO fsUserVO, OpenCustomerQueryPagerArgs openCustomerQueryPagerArgs);

    /**
     * 添加客服人员
     *
     * @param fsUserVO
     * @param openCustomerForm
     * @return total：总人数；   updateNum：更新的人数    addNum：新加的人数
     */
    Map<String, Object> addOpenCustomer(FsUserVO fsUserVO, OpenCustomerForm openCustomerForm);

    /**
     * 修改客服人员角色
     *
     * @param fsUserVO
     * @param openCustomerVO
     */
    void updateOpenCustomerRole(FsUserVO fsUserVO, OpenCustomerVO openCustomerVO);

    /**
     * 是否允许"删除"客服人员
     *
     * @param fsUserVO
     * @param openCustomerVO
     */
    void checkAllowToDelete(FsUserVO fsUserVO, OpenCustomerVO openCustomerVO);

    /**
     * "删除"客服人员
     *
     * @param fsUserVO
     * @param customerId
     */
    void deleteOpenCustomer(FsUserVO fsUserVO, String customerId);

    /**
     * 客服人员生成二维码图片
     *
     * @param fsUserVO
     * @param appId
     * @return
     */
    Map<String, Object> viewQRCode(FsUserVO fsUserVO, String appId);

    /**
     * 查询公司 fsEa的某个外联服务号appId的所有有效的客服人员的userId
     *
     * @param fsUserVO
     * @param appId
     * @return
     */
    List<Integer> queryCustomersUserIds(FsUserVO fsUserVO, String appId);
}
