package com.facishare.open.app.center.kits;

public final class Watch {
    static final ThreadLocal<Watch> watchCache = new ThreadLocal<Watch>();
    private long start = 0;
    private long last = 0;

    /**
     * 重置
     *
     * @return
     */
    public static final Watch reset() {
        long now = System.currentTimeMillis();
        Watch watch = new Watch();
        watch.start = watch.last = now;
        watchCache.set(watch);
        return watch;
    }

    public static final void interval(String msg) {
        long tid = Thread.currentThread().getId();
        System.out.println("Watch.interval[" + tid + "] : " + msg + " use: (" + interval() + ") ms ");
    }

    public static final void use(String msg) {
        long tid = Thread.currentThread().getId();
        System.out.println("Watch.use[" + tid + "] : " + msg + " use: (" + use() + ") ms ");
    }

    /**
     * 取间隔
     *
     * @return
     */
    public static final long interval() {
        long now = System.currentTimeMillis();
        Watch watch = watchCache.get();
        if (null == watch) {
            watch = reset();
        }
        long ss = now - watch.last;
        watch.last = now;
        return ss;
    }

    /**
     * 取总的使用时间
     *
     * @return
     */
    public static final long use() {
        long now = System.currentTimeMillis();
        Watch watch = watchCache.get();
        if (null == watch) {
            watch = reset();
        }
        long ss = now - watch.start;
        watch.last = now;
        return ss;
    }

}
