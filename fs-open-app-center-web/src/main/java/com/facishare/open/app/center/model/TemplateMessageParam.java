package com.facishare.open.app.center.model;

import com.facishare.open.msg.constant.MessageTypeEnum;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Map;

/**
 * Created by xialf on 10/28/15.
 *
 * <AUTHOR>
 */
public class TemplateMessageParam extends MessageParam {
    private static final long serialVersionUID = 5573458621862187381L;

    private String templateId;
    private Map<String, Map<String, String>> data;
    private String url;
    private String topColor;

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, Map<String, String>> getData() {
        return data;
    }

    public void setData(Map<String, Map<String, String>> data) {
        this.data = data;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTopColor() {
        return topColor;
    }

    public void setTopColor(String topColor) {
        this.topColor = topColor;
    }

    @Override
    public void checkParam() throws IllegalArgumentException {
        super.checkParam();
        if (!MessageTypeEnum.TEMPLATE.getType().equals(getMessageType())) {
            throw new IllegalArgumentException("请使用消息类型template"); // ignoreI18n
        }
        if (StringUtils.isBlank(templateId)) {
            throw new IllegalArgumentException("请填写模板编号"); // ignoreI18n
        }
        if (StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("模板消息的URL不能为空"); // ignoreI18n
        }
        if (null == data) {
            throw new IllegalArgumentException("模板消息的数据不能为空"); // ignoreI18n
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("templateId", templateId)
                .append("data", data)
                .append("url", url)
                .append("topColor", topColor)
                .toString();
    }
}
