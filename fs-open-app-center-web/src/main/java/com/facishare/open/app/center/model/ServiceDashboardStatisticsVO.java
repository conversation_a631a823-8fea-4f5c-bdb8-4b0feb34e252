package com.facishare.open.app.center.model;

import java.io.Serializable;

/**
 * 服务号统计数据VO
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/4.
 */
public class ServiceDashboardStatisticsVO implements Serializable{

    private static final long serialVersionUID = -900555508456063676L;

    private String appId;
    private long sendMsgCount;
    private long receiveMsgCount;
    private long viewCount;
    private long commentCount;
    private long likeCount;
    private long readCount;
    private long dataEndTime;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public long getSendMsgCount() {
        return sendMsgCount;
    }

    public void setSendMsgCount(long sendMsgCount) {
        this.sendMsgCount = sendMsgCount;
    }

    public long getReceiveMsgCount() {
        return receiveMsgCount;
    }

    public void setReceiveMsgCount(long receiveMsgCount) {
        this.receiveMsgCount = receiveMsgCount;
    }

    public long getViewCount() {
        return viewCount;
    }

    public void setViewCount(long viewCount) {
        this.viewCount = viewCount;
    }

    public long getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(long commentCount) {
        this.commentCount = commentCount;
    }

    public long getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(long likeCount) {
        this.likeCount = likeCount;
    }

    public long getReadCount() {
        return readCount;
    }

    public void setReadCount(long readCount) {
        this.readCount = readCount;
    }

    public long getDataEndTime() {
        return dataEndTime;
    }

    public void setDataEndTime(long dataEndTime) {
        this.dataEndTime = dataEndTime;
    }

    @Override
    public String toString() {
        return "ServiceDashboardStatisticsVO{" +
                "appId='" + appId + '\'' +
                ", sendMsgCount=" + sendMsgCount +
                ", receiveMsgCount=" + receiveMsgCount +
                ", viewCount=" + viewCount +
                ", commentCount=" + commentCount +
                ", likeCount=" + likeCount +
                ", readCount=" + readCount +
                ", dataEndTime=" + dataEndTime +
                '}';
    }
}
