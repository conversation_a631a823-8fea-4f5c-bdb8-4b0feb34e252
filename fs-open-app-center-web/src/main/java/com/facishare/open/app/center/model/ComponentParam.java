package com.facishare.open.app.center.model;

import com.facishare.open.app.center.api.model.EmployeeRange;
import com.google.common.base.MoreObjects;

import java.io.Serializable;

/**
 * 组件相关入参.
 * Created by xialf on 2/29/16.
 *
 * <AUTHOR>
 */
public class ComponentParam implements Serializable {
    private static final long serialVersionUID = 7101537156831577229L;

    private String componentId;
    private EmployeeRange view;

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public EmployeeRange getView() {
        return view;
    }

    public void setView(EmployeeRange view) {
        this.view = view;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("componentId", componentId)
                .add("view", view)
                .toString();
    }
}
