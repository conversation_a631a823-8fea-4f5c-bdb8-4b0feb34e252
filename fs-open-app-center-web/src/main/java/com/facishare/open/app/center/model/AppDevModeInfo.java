package com.facishare.open.app.center.model;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Created by xialf on 11/17/15.
 *
 * <AUTHOR>
 */
public class AppDevModeInfo {
    private String appId;
    /**
     * 永久授权码.
     */
    private String code;
    private String appSecret;
    private String token;
    private String encodingAesKey;
    private String callBackDomain;
    private String callBackMsgUrl;
    private Map<String, String> serviceIdNameMap;


    private int isStop; // 1代表启用，0代表停用;
    /**
     * app后台服务ip白名单，限制最大100个 ipv4 地址.
     */
    private String ipWhites;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEncodingAesKey() {
        return encodingAesKey;
    }

    public void setEncodingAesKey(String encodingAesKey) {
        this.encodingAesKey = encodingAesKey;
    }

    public String getCallBackDomain() {
        return callBackDomain;
    }

    public void setCallBackDomain(String callBackDomain) {
        this.callBackDomain = callBackDomain;
    }

    public String getCallBackMsgUrl() {
        return callBackMsgUrl;
    }

    public void setCallBackMsgUrl(String callBackMsgUrl) {
        this.callBackMsgUrl = callBackMsgUrl;
    }

    public int getIsStop() {
        return isStop;
    }

    public void setIsStop(int isStop) {
        this.isStop = isStop;
    }

    public String getIpWhites() {
        return ipWhites;
    }

    public void setIpWhites(String ipWhites) {
        this.ipWhites = ipWhites;
    }

    public Map<String, String> getServiceIdNameMap() {
        return serviceIdNameMap;
    }

    public void setServiceIdNameMap(Map<String, String> serviceIdNameMap) {
        this.serviceIdNameMap = serviceIdNameMap;
    }

    /**
     * 创建或激活开发模式时,参数是否合法.
     *
     * @throws IllegalArgumentException 参数不合法,具体信息可以参看{@code IllegalArgumentException.getMessage}
     */
    public void checkParamsForEnable() throws IllegalArgumentException {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("请填写应用id"); // ignoreI18n
        }
        if (StringUtils.isBlank(token)) {
            throw new IllegalArgumentException("token不能为空"); // ignoreI18n
        }
        if (StringUtils.isBlank(encodingAesKey)) {
            throw new IllegalArgumentException("EncodingAESKey不能为空"); // ignoreI18n
        }
    }

    @Override
    public String toString() {
        return "AppDevModeInfo{" +
                "appId='" + appId + '\'' +
                ", code='" + code + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", token='" + token + '\'' +
                ", encodingAesKey='" + encodingAesKey + '\'' +
                ", callBackDomain='" + callBackDomain + '\'' +
                ", callBackMsgUrl='" + callBackMsgUrl + '\'' +
                ", serviceIdNameMap=" + serviceIdNameMap +
                ", ipWhites='" + ipWhites + '\'' +
                '}';
    }
}
