package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.api.result.AppResult;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.app.center.manager.MaterialManager;
import com.facishare.open.app.center.model.ImageMaterialForm;
import com.facishare.open.app.center.model.ImageTextMaterialForm;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.open.material.api.enums.CreatorTypeEnum;
import com.facishare.open.material.api.enums.MaterialTemporaryTypeEnum;
import com.facishare.open.material.api.model.ImageResult;
import com.facishare.open.material.api.model.vo.ImageTextVO;
import com.facishare.open.material.api.model.vo.ImageVO;
import com.facishare.open.material.api.service.MaterialService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;

/**
 * Created by zenglb on 2015/12/25.
 */
@Service
public class MaterialManagerImpl implements MaterialManager {

    private static final Logger logger = LoggerFactory.getLogger(MaterialManagerImpl.class);

    @Autowired
    private MaterialService materialService;

    @Autowired
    private OpenAppService openAppService;

    @Override
    public Pager<ImageTextVO> queryPageAndAppId(FsUserVO user, String appId, Integer currentPage, Integer pageSize) {
        Pager<ImageTextVO> pager = new Pager<>();
        pager.setCurrentPage(currentPage);
        pager.setPageSize(pageSize);
        BaseResult<Pager<ImageTextVO>> imageTextByAppIdResult = materialService.findImageTextByAppId(pager, appId, CreatorTypeEnum.APP_ADMIN, user);
        if (!imageTextByAppIdResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, imageTextByAppIdResult, "查询失败"); // ignoreI18n
        }
        return imageTextByAppIdResult.getResult();
    }

    @Override
    public ImageTextMaterialForm loadMaterialById(FsUserVO user, String appId, String materialId) {
        AppResult appResult = openAppService.loadOpenAppFast(appId);
        if (!appResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, appResult,appResult.getErrDescription());
        }

        BaseResult<ImageTextVO> imageTextByIdResult = materialService.findImageTextById(materialId);
        if (!imageTextByIdResult.isSuccess()) {
            throw new BizException(imageTextByIdResult);
        }
        ImageTextMaterialForm result = new ImageTextMaterialForm();
        ImageTextVO imageTextVO = imageTextByIdResult.getResult();
        result.setImageTextParams(imageTextVO.getImageTextParams());
        result.setAppId(appId);
        result.setAppName(appResult.getResult().getAppName());
        result.setModifiedTime(imageTextVO.getModifiedTime());
        result.setStatus(imageTextVO.getStatus());
        return result;
    }

    @Override
    public void deleteMaterialById(FsUserVO user, String appId, String materialId) {
        BaseResult<Void> deleteMaterialResult = materialService.deleteMaterial(materialId,appId,CreatorTypeEnum.APP_ADMIN,user);
        if (!deleteMaterialResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, deleteMaterialResult, "删除图文消息失败"); // ignoreI18n
        }
    }

    @Override
    public String createMaterial(FsUserVO user, ImageTextMaterialForm imageTextMaterialForm) {
        ImageTextVO imageTextVO = new ImageTextVO();
        imageTextVO.setIsTemporary(MaterialTemporaryTypeEnum.NORMAL.getCode());
        imageTextVO.setImageTextParams(imageTextMaterialForm.getImageTextParams());
        BaseResult<String> addImageTextResult = materialService.addImageText(imageTextVO, imageTextMaterialForm.getAppId(), CreatorTypeEnum.APP_ADMIN, user);
        if (!addImageTextResult.isSuccess()) {
            throw new BizException(addImageTextResult);
        }
        return addImageTextResult.getResult();
    }

    @Override
    public String updateMaterial(FsUserVO user, ImageTextMaterialForm imageTextMaterialForm) {
        ImageTextVO imageTextVO = new ImageTextVO();
        imageTextVO.setImageTextParams(imageTextMaterialForm.getImageTextParams());
        BaseResult<Void> modifyImageTextResult = materialService.modifyImageText(imageTextMaterialForm.getMaterialId(), imageTextMaterialForm.getAppId(), imageTextVO, CreatorTypeEnum.APP_ADMIN, user);
        if (!modifyImageTextResult.isSuccess()) {
            throw new BizException(modifyImageTextResult);
        }
        return imageTextMaterialForm.getMaterialId();
    }

    @Override
    public ImageResult uploadImageAndAppId(FsUserVO user, ImageMaterialForm imageMaterialForm) {
        ImageVO imageVO = new ImageVO();
        CommonsMultipartFile imageFile = imageMaterialForm.getImageFile();
        String originalFilename = imageFile.getOriginalFilename();
        int lastIndex = originalFilename.lastIndexOf(".");
        if (lastIndex < 1) {
            imageVO.setExtName("");
        } else {
            imageVO.setExtName(originalFilename.substring(lastIndex + 1));
        }
        try {
            BufferedImage image = ImageIO.read(imageFile.getInputStream());
            imageVO.setRotate(0);
            imageVO.setRectangle(new Rectangle(0, 0, image.getWidth(), image.getHeight()));
            imageVO.setData(imageFile.getBytes());
        } catch (IOException e) {
            logger.warn("ImageIO.read(file) is error!", e);
            throw new BizException(AjaxCode.PARAM_ERROR, e, "无法解析您上传的图片，该图片可能采用了比较特殊的编码格式或颜色空间"); // ignoreI18n
        }
        BaseResult<ImageResult> imageResultBaseResult = materialService.addImage(imageVO, imageMaterialForm.getAppId(), CreatorTypeEnum.APP_ADMIN, user);
        if (!imageResultBaseResult.isSuccess()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "上传图片消息失败."); // ignoreI18n
        }
        return imageResultBaseResult.getResult();
    }
}
