package com.facishare.open.app.center.controller;

import com.facishare.open.app.center.ajax.code.AjaxCode;
import com.facishare.open.app.center.ajax.result.AjaxResult;
import com.facishare.open.app.center.common.BaseController;
import com.facishare.open.app.center.form.ArticleCategoryForm;
import com.facishare.open.app.center.manager.ArticleCategoryManager;
import com.facishare.open.app.center.threadlocal.UserContextHolder;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.material.api.enums.ArticleCategorySwitchEnum;
import com.facishare.open.material.api.model.vo.ArticleCategoryVO;
import com.facishare.open.material.api.result.MessageArticleCategoryResult;
import com.facishare.open.material.api.result.MongoPager;
import com.facishare.open.material.api.service.ArticleCategorySwitchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务号文章分类Controller
 * Created by liqiulin on 2016/11/14.
 */
@Controller
@RequestMapping("/open/appcenter/service/article/category")
public class ArticleCategoryController extends BaseController {
    @Resource
    ArticleCategoryManager articleCategoryManager;

    @Resource
    ArticleCategorySwitchService articleCategorySwitchService;

    /**
     * 是否需要提示文章分类新功能引导
     * @return
     */
    @RequestMapping("/needGuide")
    @ResponseBody
    public AjaxResult needGuide(@RequestParam(value = "onlyQuery", required = false) Boolean onlyQuery) {
        if (onlyQuery == null) {
            onlyQuery = true;
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        return new AjaxResult(articleCategoryManager.needGuide(fsUserVO, onlyQuery));
    }

    /**
     * 查询分类列表（不包括分类下文章数量）
     * @param appId
     * @return
     */
    @RequestMapping("/queryCategories")
    @ResponseBody
    public AjaxResult queryCategories(@RequestParam(value = "appId", required = false) String appId) {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        List<ArticleCategoryVO> articleCategoryVOList = articleCategoryManager.queryCategories(fsUserVO.getEnterpriseAccount(), appId);
        return new AjaxResult(articleCategoryVOList);
    }

    /**
     * 查询分类列表（包括分类下文章数量）
     * @param appId
     * @return
     */
    @RequestMapping("/queryCategoriesWithArticlesCount")
    @ResponseBody
    public AjaxResult queryCategoriesWithArticlesCount(@RequestParam(value = "appId", required = false) String appId) {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        List<ArticleCategoryVO> articleCategoryVOList = articleCategoryManager.queryCategoriesWithArticlesCount(fsUserVO.getEnterpriseAccount(), appId);
        return new AjaxResult(articleCategoryVOList);
    }

    /**
     * 创建文章分类
     * @param articleCategoryForm
     * @return
     */
    @RequestMapping(value = "/createCategory", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult createCategory(@RequestBody ArticleCategoryForm articleCategoryForm) {
        String appId = articleCategoryForm.getAppId();
        String title = articleCategoryForm.getTitle();
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (StringUtils.isBlank(title)) {
            throw new IllegalArgumentException("分类标题为空"); // ignoreI18n
        }
        checkParamRegex(title, "^.{1,6}$", "请填写有效的标题."); // ignoreI18n
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        String categoryId = articleCategoryManager.createCategory(fsUserVO, appId, title);
        return new AjaxResult(categoryId);
    }

    /**
     * 重命名文章分类
     * @param articleCategoryForm
     * @return
     */
    @RequestMapping(value = "/renameCategoryTitle", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult renameCategoryTitle(@RequestBody ArticleCategoryForm articleCategoryForm) {
        String appId = articleCategoryForm.getAppId();
        String categoryId = articleCategoryForm.getCategoryId();
        String newTitle = articleCategoryForm.getNewTitle();

        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (StringUtils.isBlank(categoryId)) {
            throw new IllegalArgumentException("categoryId is bank");
        }
        if (StringUtils.isBlank(newTitle)) {
            throw new IllegalArgumentException("分类标题为空"); // ignoreI18n
        }
        checkParamRegex(newTitle, "^.{1,6}$", "请填写有效的标题."); // ignoreI18n
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        articleCategoryManager.renameCategoryTile(fsUserVO.getEnterpriseAccount(), appId, categoryId, newTitle);
        return new AjaxResult(null);
    }

    /**
     * 添加文章到指定分类下
     * @param articleCategoryForm
     * @return
     */
    @RequestMapping(value = "/addArticles", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult addArticles(@RequestBody ArticleCategoryForm articleCategoryForm) {
        String appId = articleCategoryForm.getAppId();
        String categoryId = articleCategoryForm.getCategoryId();
        List<String> msgImageTextParamIds = articleCategoryForm.getMsgImageTextParamIds();

        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (StringUtils.isBlank(categoryId)) {
            throw new IllegalArgumentException("categoryId is bank");
        }
        if (CollectionUtils.isEmpty(msgImageTextParamIds)) {
            throw new IllegalArgumentException("msgImageTextParamIds is empty");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        articleCategoryManager.addArticles(fsUserVO.getEnterpriseAccount(), appId, categoryId, msgImageTextParamIds);
        return new AjaxResult(null);
    }

    /**
     * 查询分类下文章分页
     * @param appId
     * @param categoryId
     * @return
     */
    @RequestMapping("/queryCategoryArticles")
    @ResponseBody
    public AjaxResult queryCategoryArticles(@RequestParam(value = "appId", required = false) String appId,
                                                @RequestParam(value = "categoryId", required = false) String categoryId,
                                                @RequestParam(value = "title", required = false) String title,
                                                @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                @RequestParam(value = "lastTime", required = false) Long lastTime) {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (StringUtils.isBlank(categoryId)) {
            throw new IllegalArgumentException("categoryId is bank");
        }
        if (pageSize == null || pageSize < 0) {
            throw new IllegalArgumentException("pageSize is invalid");
        }
        if (lastTime == null) {
            throw new IllegalArgumentException("lastTime is null");
        }

        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        MongoPager<MessageArticleCategoryResult> messageArticleCategoryResultMongoPager =
                articleCategoryManager.queryCategoryArticles(fsUserVO.getEnterpriseAccount(), appId, categoryId, title, pageSize, lastTime);
        return new AjaxResult(messageArticleCategoryResultMongoPager);
    }

    /**
     * 查询文章列表用于添加到分类
     * @param appId
     * @param title
     * @param pageSize
     * @param lastTime
     * @return
     */
    @RequestMapping("/queryArticlesForAdd")
    @ResponseBody
    public AjaxResult queryArticlesForAdd (@RequestParam(value = "appId", required = false) String appId,
                                            @RequestParam(value = "title", required = false) String title,
                                            @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                            @RequestParam(value = "lastTime", required = false) Long lastTime) {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (pageSize == null || pageSize < 0) {
            throw new IllegalArgumentException("pageSize is invalid");
        }
        if (lastTime == null) {
            throw new IllegalArgumentException("lastTime is null");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        return new AjaxResult(articleCategoryManager.queryArticlesForAdd(fsUserVO.getEnterpriseAccount(), appId, title, pageSize, lastTime));
    }



    /**
     * 删除分类
     * @param articleCategoryForm
     * @return
     */
    @RequestMapping(value = "/deleteCategory", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult deleteCategory(@RequestBody ArticleCategoryForm articleCategoryForm) {
        String appId = articleCategoryForm.getAppId();
        String categoryId = articleCategoryForm.getCategoryId();

        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (StringUtils.isBlank(categoryId)) {
            throw new IllegalArgumentException("categoryId is bank");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        articleCategoryManager.deleteCategory(fsUserVO.getEnterpriseAccount(), appId, categoryId);
        return new AjaxResult(null);
    }

    /**
     * 删除分类下文章
     * @param articleCategoryForm
     * @return
     */
    @RequestMapping(value = "/deleteArticles", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult deleteArticles(@RequestBody ArticleCategoryForm articleCategoryForm) {
        String appId = articleCategoryForm.getAppId();
        String categoryId = articleCategoryForm.getCategoryId();
        List<String> msgImageTextParamIds = articleCategoryForm.getMsgImageTextParamIds();

        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (StringUtils.isBlank(categoryId)) {
            throw new IllegalArgumentException("categoryId is bank");
        }
        if (CollectionUtils.isEmpty(msgImageTextParamIds)) {
            throw new IllegalArgumentException("msgImageTextParamIds is empty");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        articleCategoryManager.deleteArticles(fsUserVO.getEnterpriseAccount(), appId, categoryId, msgImageTextParamIds);
        return new AjaxResult(null);
    }

    /**
     * 保存分类顺序
     * @param articleCategoryForm
     * @return
     */
    @RequestMapping(value = "/saveCategoriesOrder", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult saveCategoriesOrder(@RequestBody ArticleCategoryForm articleCategoryForm) {
        String appId = articleCategoryForm.getAppId();
        List<String> categoryIds = articleCategoryForm.getCategoryIds();

        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if (CollectionUtils.isEmpty(categoryIds)) {
            throw new IllegalAccessError("categoryIds is empty");
        }
        FsUserVO fsUserVO = UserContextHolder.getFsUserVO();
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        articleCategoryManager.saveCategoriesOrder(fsUserVO.getEnterpriseAccount(), appId, categoryIds);
        return new AjaxResult(null);
    }

    /**
     * 获取文章分类发布地址
     * @param fsUserVO
     * @return
     */
    @RequestMapping("/queryPublishUrl")
    @ResponseBody
    public AjaxResult queryPublishUrl(@ModelAttribute FsUserVO fsUserVO, @RequestParam(value = "appId", required = false) String
            appId) {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        if(!isFsAdmin(fsUserVO) && !isAppAdmin(fsUserVO, appId)) {
            throw new BizException(AjaxCode.NO_AUTHORITY, "NEED_FS_ADMIN_OR_APP_ADMIN", "操作需要系统管理员权限或者应用管理员权限"); // ignoreI18n
        }
        checkSwitch(fsUserVO.getEnterpriseAccount(), appId);
        String H5 = String.format(ConfigCenter.ARTICLE_CATEGORY_H5, fsUserVO.getEnterpriseAccount(), appId);
        return new AjaxResult(H5);
    }

    /**
     * 获取文章分类开关
     * @param fsUserVO
     * @return
     */
    @RequestMapping("/queryArticleCategorySwitch")
    @ResponseBody
    public AjaxResult queryArticleCategorySwitch(@ModelAttribute FsUserVO fsUserVO, @RequestParam(value = "appId", required = false) String
            appId) {
        if (StringUtils.isBlank(appId)) {
            throw new IllegalArgumentException("appId is bank");
        }
        BaseResult<ArticleCategorySwitchEnum> articleCategorySwitchEnumBaseResult = articleCategorySwitchService
                .queryCategorySwitchByFsEaAndAppId(fsUserVO.getEnterpriseAccount(), appId);
        if (!articleCategorySwitchEnumBaseResult.isSuccess()) {
            logger.error("failed to call articleCategorySwitchService.queryCategorySwitchByFsEaAndAppId. " +
                    "fsEa[{}], appId[{}], articleCategorySwitchEnumBaseResult[{}]",
                    fsUserVO.getEnterpriseAccount(), appId, articleCategorySwitchEnumBaseResult);
            throw new BizException(articleCategorySwitchEnumBaseResult);
        }
        return new AjaxResult(articleCategorySwitchEnumBaseResult.getResult().getCode());
    }

    private void checkSwitch(String fsEa, String appId) {
        //判断开关
        BaseResult<ArticleCategorySwitchEnum> articleCategorySwitchEnumBaseResult = articleCategorySwitchService
                .queryCategorySwitchByFsEaAndAppId(fsEa, appId);
        if (!articleCategorySwitchEnumBaseResult.isSuccess()) {
            logger.error("failed to call articleCategorySwitchService.queryCategorySwitchByFsEaAndAppId. " +
                    "fsEa[{}], appId[{}], articleCategorySwitchEnumBaseResult[{}]", fsEa, appId, articleCategorySwitchEnumBaseResult);
            throw new BizException(articleCategorySwitchEnumBaseResult);
        }
        if (articleCategorySwitchEnumBaseResult.getResult().getCode() == ArticleCategorySwitchEnum.OFF.getCode()) {
            throw new BizException(AjaxCode.BIZ_EXCEPTION, "文章分类功能已经关闭"); // ignoreI18n
        }
    }
}
