package com.facishare.open.app.center.model;

import java.io.Serializable;
import java.util.Arrays;

/**
 * Description: 客服人员信息
 * User: zhouq
 * Date: 2016/6/2
 */
public class SupportStaffForm implements Serializable{
    private static final long serialVersionUID = -5524675064495475829L;

    /**
     * 应用id.
     */
    private String appId;

    /**
     * 客服名称.
     */
    private String supportStaffName;

    /**
     * 客服人员列表;逗号分割的字符串（233,3432,44234）
     */
    private String[] supportStaffs;

    /**
     * 客服头像logoUrl.
     */
    private String supportStaffLogo;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSupportStaffName() {
        return supportStaffName;
    }

    public void setSupportStaffName(String supportStaffName) {
        this.supportStaffName = supportStaffName;
    }

    public String[] getSupportStaffs() {
        return supportStaffs;
    }

    public void setSupportStaffs(String[] supportStaffs) {
        this.supportStaffs = supportStaffs;
    }

    public String getSupportStaffLogo() {
        return supportStaffLogo;
    }

    public void setSupportStaffLogo(String supportStaffLogo) {
        this.supportStaffLogo = supportStaffLogo;
    }

    @Override
    public String toString() {
        return "SupportStaffForm{" +
                "appId='" + appId + '\'' +
                ", supportStaffName='" + supportStaffName + '\'' +
                ", supportStaffs=" + Arrays.toString(supportStaffs) +
                ", supportStaffLogo='" + supportStaffLogo + '\'' +
                '}';
    }
}
