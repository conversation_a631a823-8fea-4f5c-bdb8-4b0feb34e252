package com.facishare.open.app.center.common;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
   * 常量
   * <AUTHOR>
   * @date 2015年10月27日
   */
public class AppCenterConstants {

    public static String BIND_APPS;
    //自定义菜单能绑定的应用属性
    public final static Map<String, String> BIND_MAP = new HashMap<>();
    static {
        ConfigFactory.getInstance().getConfig("fs-open-app-center-common", config -> {
            BIND_APPS = config.get("BIND_APPS");
            if (StringUtils.isBlank(BIND_APPS)){
                String[] appMenuList = BIND_APPS.split(";");
                for (String str : appMenuList) {
                    String[] appMenu = str.split("--");
                    if (ArrayUtils.isNotEmpty(appMenu)){
                        BIND_MAP.put(appMenu[2], appMenu[0]);
                    }
                }
            }
        });
    }

    /**
     * 配置中心，以strs=aakey:aavalue,bbkey:bbvalue数据转换为map(key,value)
     *
     * @param strs str
     * @return map
     */
    private static Map<String, String> stringToMap(String strs) {

        if (StringUtils.isBlank(strs)) {
            return Collections.emptyMap();
        }
        String[] strArray = StringUtils.split(strs, ",");

        Map<String, String> map = Maps.newHashMap();
        for (String str : strArray) {
            String[] entry = StringUtils.split(str, ":");
            if (entry.length == 0) {
                continue;
            } else if (entry.length == 1) {
                continue;
            } else {
                map.put(entry[0], entry[1]);
            }
        }
        return map;
    }

}
