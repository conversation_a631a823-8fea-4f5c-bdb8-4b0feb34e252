package com.facishare.open.operating.center.manager.impl;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.ajax.code.AjaxCode;
import com.facishare.open.operating.center.api.service.FirstTimeEventService;
import com.facishare.open.operating.center.manager.FirstTimeEventManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 第一次事件Controller
 * Created by liqiulin on 2016/8/8.
 */
@Service
public class FirstTimeEventManagerImpl implements FirstTimeEventManager {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FirstTimeEventService firstTimeEventService;

    @Override
    public boolean isFirstTime(FsUserVO fsUserVO, String eventFlag, boolean onlyQuery) {
        String eventExecutor = FsUserVO.toFsUserString(fsUserVO);
        return isFirstTime(eventFlag, eventExecutor, onlyQuery);
    }

    @Override
    public boolean isFirstTime(String eventFlag, String eventExecutor, boolean onlyQuery) {
        BaseResult<Boolean> isFirstTimeResult =
                firstTimeEventService.isFirstTime(eventFlag, eventExecutor, onlyQuery);
        if(!isFirstTimeResult.isSuccess()) {
            logger.warn("failed to call firstTimeEventService.isFirstTime, " +
                    "eventFlag[{}], eventExecutor[{}], isFirstTimeResult[{}]",eventFlag, eventExecutor, isFirstTimeResult);
            throw new BizException(AjaxCode.BIZ_EXCEPTION, isFirstTimeResult, "判断是否第一次执行事败异常"); // ignoreI18n
        }
        return isFirstTimeResult.getResult();
    }
}
