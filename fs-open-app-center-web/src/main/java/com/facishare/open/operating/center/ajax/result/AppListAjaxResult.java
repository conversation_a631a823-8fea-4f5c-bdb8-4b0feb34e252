package com.facishare.open.operating.center.ajax.result;


import com.facishare.open.operating.center.ajax.code.AjaxCode;

import java.util.List;

/**
 * 返回一个list数据
 *
 * <AUTHOR>
 * @date 2015年8月17日
 */
public class AppListAjaxResult extends AjaxResult {
    private List<?> appList;

    public AppListAjaxResult(Integer errCode, String errMsg) {
        super(errCode, errMsg);
    }

    public AppListAjaxResult(List<?> appList) {
        super(AjaxCode.OK, "OK");
        this.appList = appList;
    }

    public List<?> getAppList() {
        return appList;
    }

    public void setAppList(List<?> appList) {
        this.appList = appList;
    }
}
