package com.facishare.open.operating.center.controller;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.operating.center.ajax.result.AjaxResult;
import com.facishare.open.operating.center.common.BaseController;
import com.facishare.open.operating.center.manager.ServiceEventManager;
import com.facishare.open.operating.center.utils.ConfigCenter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by liqiulin on 2016/8/8.
 */
@Controller
@RequestMapping("/open/appcenter/operating/service/event")
public class ServiceEventController extends BaseController {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private ServiceEventManager serviceEventManager;

    /**
     * 是否第一次群发消息
     * @param fsUserVO
     * @return
     */
    @RequestMapping("/isFirstTimeSendGroupMsg")
    @ResponseBody
    public AjaxResult isFirstTimeSendGroupMsg(@ModelAttribute FsUserVO fsUserVO,
                                              @RequestParam(value = "appId", required = false) String appId) {
        super.checkParamNotBlank(appId, "应用ID为空"); // ignoreI18n
        boolean isFirstTimeSendGroupMsg = serviceEventManager.isFirstTimeSendGroupMsg(fsUserVO, appId);
        if (StringUtils.isNotBlank(ConfigCenter.IS_OPEN_GROUP_MSG_TASK_FUNCTION) && Boolean.parseBoolean(ConfigCenter.IS_OPEN_GROUP_MSG_TASK_FUNCTION)) {
            serviceEventManager.isSendGroupMsgInApp(fsUserVO, appId);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("isFirstTime", isFirstTimeSendGroupMsg);
        return new AjaxResult(result);
    }

    /**
     * (用户、服务号判断是否第一次)进入外联服务号工作台第一次引导
     * @param fsUserVO
     * @return
     */
    @RequestMapping("/isFirstTimeEnterDashboard")
    @ResponseBody
    public AjaxResult isFirstTimeEnterDashboard(@ModelAttribute FsUserVO fsUserVO,
                                              @RequestParam(value = "appId", required = false) String appId) {
        super.checkParamNotBlank(appId, "应用ID为空"); // ignoreI18n
        boolean isFirstTime = serviceEventManager.isFirstTimeEnterDashboard(fsUserVO, appId);
        Map<String, Object> result = new HashMap<>();
        result.put("isFirstTime", isFirstTime);
        if (isFirstTime && StringUtils.isNotBlank(ConfigCenter.IS_NEED_GUIDE_ENTER_SERVICE_DASHBOARD)) {
            result.put("moreUrl", ConfigCenter.IS_NEED_GUIDE_ENTER_SERVICE_DASHBOARD);
        }
        return new AjaxResult(result);
    }
}
