package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LayoutButtonsTest {

    @Mock
    private ObjectDescribeExt objectDescribeExt;

    @BeforeEach
    void setUp() {
        // Mock基础设置
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取已知对象的LayoutButtons实例
     */
    @Test
    @DisplayName("正常场景 - 获取已知对象的LayoutButtons实例")
    void testGetInstance_KnownObject() {
        // Given
        when(objectDescribeExt.getApiName()).thenReturn("AccountObj");

        // When
        LayoutButtons result = LayoutButtons.getInstance(objectDescribeExt);

        // Then
        assertNotNull(result);
        assertEquals(LayoutButtons.AccountObj, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取未知对象的LayoutButtons实例，应返回UserDefinedObj
     */
    @Test
    @DisplayName("正常场景 - 获取未知对象返回UserDefinedObj")
    void testGetInstance_UnknownObject() {
        // Given
        when(objectDescribeExt.getApiName()).thenReturn("unknown_object__c");

        // When
        LayoutButtons result = LayoutButtons.getInstance(objectDescribeExt);

        // Then
        assertNotNull(result);
        assertEquals(LayoutButtons.UserDefinedObj, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取客户对象的动作按钮
     */
    @Test
    @DisplayName("正常场景 - 获取客户对象的动作按钮")
    void testGetActionButtons_AccountObj() {
        // When
        List<IButton> result = LayoutButtons.AccountObj.getActionButtons();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证包含预期的按钮
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.DELETE.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取联系人对象的动作按钮
     */
    @Test
    @DisplayName("正常场景 - 获取联系人对象的动作按钮")
    void testGetActionButtons_ContactObj() {
        // When
        List<IButton> result = LayoutButtons.ContactObj.getActionButtons();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证包含预期的按钮
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.DIAL.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取商机对象的动作按钮
     */
    @Test
    @DisplayName("正常场景 - 获取商机对象的动作按钮")
    void testGetActionButtons_OpportunityObj() {
        // When
        List<IButton> result = LayoutButtons.OpportunityObj.getActionButtons();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证包含预期的按钮
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.CHANGE_SALE_ACTION.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取自定义对象的动作按钮
     */
    @Test
    @DisplayName("正常场景 - 获取自定义对象的动作按钮")
    void testGetActionButtons_UserDefinedObj() {
        // When
        List<IButton> result = LayoutButtons.UserDefinedObj.getActionButtons();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证包含预期的按钮
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.START_BPM.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取尾部按钮的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取普通对象的尾部按钮")
    void testGetTailButtons_NormalObject() {
        // When
        List<IButton> result = LayoutButtons.AccountObj.getTailButtons();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 验证包含预期的尾部按钮
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.SEND_MAIL.getActionCode().equals(btn.getAction())));
        assertTrue(result.stream().anyMatch(btn -> ObjectAction.PRINT.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取销售订单产品的尾部按钮，应该不包含打印按钮
     */
    @Test
    @DisplayName("正常场景 - 获取销售订单产品的尾部按钮")
    void testGetTailButtons_SalesOrderProduct() {
        // Given
        // 直接使用常量，不需要Mock

            // When
            List<IButton> result = LayoutButtons.SalesOrderProductObj.getTailButtons();

            // Then
            assertNotNull(result);
            // 验证不包含打印按钮
            assertFalse(result.stream().anyMatch(btn -> ObjectAction.PRINT.getActionCode().equals(btn.getAction())));
            // 验证包含其他补充按钮
            assertTrue(result.stream().anyMatch(btn -> ObjectAction.SEND_MAIL.getActionCode().equals(btn.getAction())));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取退货单产品的尾部按钮，应该不包含打印按钮
     */
    @Test
    @DisplayName("正常场景 - 获取退货单产品的尾部按钮")
    void testGetTailButtons_ReturnedGoodsInvoiceProduct() {
        // Given
        try (MockedStatic<Utils> utilsMock = mockStatic(Utils.class)) {
            utilsMock.when(Utils::getReturnGoodsInvoiceProductApiName).thenReturn("ReturnedGoodsInvoiceProductObj");

            // When
            List<IButton> result = LayoutButtons.ReturnedGoodsInvoiceProductObj.getTailButtons();

            // Then
            assertNotNull(result);
            // 验证不包含打印按钮
            assertFalse(result.stream().anyMatch(btn -> ObjectAction.PRINT.getActionCode().equals(btn.getAction())));
            // 验证包含其他补充按钮
            assertTrue(result.stream().anyMatch(btn -> ObjectAction.SEND_MAIL.getActionCode().equals(btn.getAction())));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试所有枚举值都有对应的名称
     */
    @Test
    @DisplayName("正常场景 - 验证所有枚举值都有名称")
    void testAllEnumValuesHaveNames() {
        // When
        LayoutButtons[] allValues = LayoutButtons.values();

        // Then
        for (LayoutButtons layoutButton : allValues) {
            assertNotNull(layoutButton.name());
            assertFalse(layoutButton.name().isEmpty());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试枚举常量列表的初始化
     */
    @Test
    @DisplayName("正常场景 - 验证枚举常量列表初始化")
    void testNameListInitialization() {
        // When
        LayoutButtons[] allValues = LayoutButtons.values();

        // Then
        // 验证nameList包含所有枚举值的名称
        for (LayoutButtons layoutButton : allValues) {
            assertTrue(LayoutButtons.nameList.contains(layoutButton.name()));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试特定对象的按钮配置
     */
    @Test
    @DisplayName("正常场景 - 测试产品对象的按钮配置")
    void testProductObjButtons() {
        // When
        List<IButton> actionButtons = LayoutButtons.ProductObj.getActionButtons();
        List<IButton> tailButtons = LayoutButtons.ProductObj.getTailButtons();

        // Then
        assertNotNull(actionButtons);
        assertNotNull(tailButtons);
        // 验证产品对象包含特定按钮
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.DIAL.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试工单对象的按钮配置
     */
    @Test
    @DisplayName("正常场景 - 测试工单对象的按钮配置")
    void testCasesObjButtons() {
        // When
        List<IButton> actionButtons = LayoutButtons.CasesObj.getActionButtons();

        // Then
        assertNotNull(actionButtons);
        // 验证工单对象包含特定按钮
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.SERVICE_RECORD.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试市场活动对象的按钮配置
     */
    @Test
    @DisplayName("正常场景 - 测试市场活动对象的按钮配置")
    void testMarketingEventObjButtons() {
        // When
        List<IButton> actionButtons = LayoutButtons.MarketingEventObj.getActionButtons();

        // Then
        assertNotNull(actionButtons);
        // 验证市场活动对象包含特定按钮
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.CHANGE_STATES.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试合同对象的按钮配置
     */
    @Test
    @DisplayName("正常场景 - 测试合同对象的按钮配置")
    void testContractObjButtons() {
        // When
        List<IButton> actionButtons = LayoutButtons.ContractObj.getActionButtons();

        // Then
        assertNotNull(actionButtons);
        // 验证合同对象包含特定按钮
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.UPDATE.getActionCode().equals(btn.getAction())));
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.ADD_TEAM_MEMBER.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试电子签名对象的按钮配置
     */
    @Test
    @DisplayName("正常场景 - 测试电子签名对象的按钮配置")
    void testElectronicSignerObjButtons() {
        // When
        List<IButton> actionButtons = LayoutButtons.ElectronicSignerObj.getActionButtons();

        // Then
        assertNotNull(actionButtons);
        // 验证电子签名对象包含特定按钮
        assertTrue(actionButtons.stream().anyMatch(btn -> ObjectAction.SIGN_FILE.getActionCode().equals(btn.getAction())));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取实例时传入null参数
     */
    @Test
    @DisplayName("异常场景 - 获取实例时传入null参数")
    void testGetInstance_NullParameter() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            LayoutButtons.getInstance(null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取实例时对象API名称为null
     */
    @Test
    @DisplayName("边界场景 - 对象API名称为null")
    void testGetInstance_NullApiName() {
        // Given
        when(objectDescribeExt.getApiName()).thenReturn(null);

        // When
        LayoutButtons result = LayoutButtons.getInstance(objectDescribeExt);

        // Then
        assertNotNull(result);
        assertEquals(LayoutButtons.UserDefinedObj, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取实例时对象API名称为空字符串
     */
    @Test
    @DisplayName("边界场景 - 对象API名称为空字符串")
    void testGetInstance_EmptyApiName() {
        // Given
        when(objectDescribeExt.getApiName()).thenReturn("");

        // When
        LayoutButtons result = LayoutButtons.getInstance(objectDescribeExt);

        // Then
        assertNotNull(result);
        assertEquals(LayoutButtons.UserDefinedObj, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证按钮创建的一致性
     */
    @Test
    @DisplayName("正常场景 - 验证按钮创建的一致性")
    void testButtonCreationConsistency() {
        // When
        List<IButton> actionButtons1 = LayoutButtons.AccountObj.getActionButtons();
        List<IButton> actionButtons2 = LayoutButtons.AccountObj.getActionButtons();

        // Then
        assertNotNull(actionButtons1);
        assertNotNull(actionButtons2);
        assertEquals(actionButtons1.size(), actionButtons2.size());
        
        // 验证每次调用都创建新的按钮实例
        for (int i = 0; i < actionButtons1.size(); i++) {
            assertEquals(actionButtons1.get(i).getAction(), actionButtons2.get(i).getAction());
            assertEquals(actionButtons1.get(i).getName(), actionButtons2.get(i).getName());
        }
    }
}
