package com.facishare.open.custom.menu.dao;

import com.facishare.open.custom.menu.api.model.CustomMenuDO;
import com.facishare.open.custom.menu.api.enums.CustomMenuStatusEnum;

/**
 * 自定义菜单DAO
 *
 * <AUTHOR>
 * @since on 2015/12/7.
 */
public interface CustomMenuDAO {


    /**
     * 保存.
     *
     * @return 受影响的行数
     */
    int insert(CustomMenuDO customMenuDO);

    /**
     * 通过appId查找CustomMenu.
     *
     * @return 如果存在则返回实体，否则返回null
     */
    CustomMenuDO selectByAppId(String appId);

    /**
     * 更新自定义菜单的状态.
     *
     * @return 受影响的行
     */
    int updateCustomMenuStatus(String appId, CustomMenuStatusEnum newStatus, CustomMenuStatusEnum oldStatus);

    /**
     * 更新自定义菜单的内容.
     *
     * @return 受影响的行
     */
    int updateContent(String appId, String content);


}
