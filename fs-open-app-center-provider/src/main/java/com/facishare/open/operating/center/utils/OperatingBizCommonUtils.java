package com.facishare.open.operating.center.utils;

import com.fxiaoke.common.release.GrayRelease;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfigFactory;
import org.springframework.stereotype.Service;

/**
 * 公用工具类.
 *
 * <AUTHOR>
 * @date on 2016/2/25.
 */
@Service
public class OperatingBizCommonUtils {

    private static IConfigFactory factory = ConfigFactory.getInstance();

    /**
     * 使用应用运营系统发送消息.
     *
     * @param fsEa
     * @return
     */
    public static boolean isServiceTemplateAdminOperating(String fsEa) {
        return GrayRelease.isAllow("app-center", "useServiceAdminOperating", fsEa);
    }

    /**
     * 不需要素材中心来发送文本消息.
     *
     * @param fsEa
     * @return
     */
    public static boolean isNotUseMaterialSendMsg(String fsEa) {
        return GrayRelease.isAllow("app-center", "isNotUseMaterialSendMsg", fsEa);
    }

}
