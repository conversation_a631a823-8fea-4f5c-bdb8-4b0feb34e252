package com.facishare.open.operating.center.service.impl;

import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.operating.center.api.model.enums.OpenOperatingCenterCodeEnum;
import com.facishare.open.operating.center.api.service.FirstTimeEventService;
import com.facishare.open.operating.center.manager.FirstTimeEventManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by liqiulin on 2016/8/1.
 */
@Service("firstTimeEventServiceImpl")
public class FirstTimeEventServiceImpl implements FirstTimeEventService {
    private static final Logger logger = LoggerFactory.getLogger(FirstTimeEventServiceImpl.class);

    @Resource
    FirstTimeEventManager firstTimeEventManager;

    @Override
    public BaseResult<Boolean> isFirstTime(String eventFlag, String eventExecutor) {
        if (StringUtils.isBlank(eventFlag) || StringUtils.isBlank(eventExecutor)) {
            return new BaseResult<>(OpenOperatingCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            boolean isFirstTime = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor);
            return new BaseResult<>(isFirstTime);
        } catch (BizException e) {
            logger.warn("failed to call isFirstTime, eventFlag[" + eventFlag + "], eventExecutor[" + eventExecutor + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to call isFirstTime, eventFlag[" + eventFlag + "], eventExecutor[" + eventExecutor + "]", e);
            return new BaseResult<>(OpenOperatingCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Boolean> isFirstTime(String eventFlag, String eventExecutor, boolean onlyQuery) {
        if (StringUtils.isBlank(eventFlag) || StringUtils.isBlank(eventExecutor)) {
            return new BaseResult<>(OpenOperatingCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            boolean isFirstTime = firstTimeEventManager.isFirstTime(eventFlag, eventExecutor, onlyQuery);
            return new BaseResult<>(isFirstTime);
        } catch (BizException e) {
            logger.warn("failed to call isFirstTime, eventFlag[" + eventFlag + "], eventExecutor[" + eventExecutor + "], onlyQuery["+onlyQuery+"]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to call isFirstTime, eventFlag[" + eventFlag + "], eventExecutor[" + eventExecutor + "], onlyQuery["+onlyQuery+"]", e);
            return new BaseResult<>(OpenOperatingCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }
}
