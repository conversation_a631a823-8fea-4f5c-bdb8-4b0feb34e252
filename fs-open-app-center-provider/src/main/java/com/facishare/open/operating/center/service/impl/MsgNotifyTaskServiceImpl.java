package com.facishare.open.operating.center.service.impl;

import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.jobs.api.model.WorkVO;
import com.facishare.open.jobs.api.service.WorkCallBackService;
import com.facishare.open.operating.center.api.service.MsgNotifyTaskService;
import com.facishare.open.operating.center.manager.ServiceMessageManager;
import com.facishare.open.operating.center.model.ServiceTemplateTaskDO;
import com.facishare.open.operating.center.utils.CommonThreadPoolUtils;
import com.facishare.open.operating.center.utils.DateFormatUtils;
import com.facishare.open.operating.center.utils.JsonKit;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Created by huyue on 2016/9/20.
 */
@Service("msgNotifyTaskServiceImpl")
public class MsgNotifyTaskServiceImpl implements MsgNotifyTaskService {
    private static final Logger logger = LoggerFactory.getLogger(MsgNotifyTaskServiceImpl.class);

    @Resource
    private ServiceMessageManager serviceMessageManager;

    @Resource
    private WorkCallBackService workCallBackService;

    @Override
    public BaseResult<Void> processWork(WorkVO workVO) {
        if (null == workVO || StringUtils.isBlank(workVO.getContent())) {
            return new BaseResult<>();
        }
        CommonThreadPoolUtils.getExecutor().execute(() -> {
            try {
                ServiceTemplateTaskDO serviceTemplateTaskDO = JsonKit.json2object(workVO.getContent(), ServiceTemplateTaskDO.class);
                String serviceTemplateId = serviceTemplateTaskDO.getServiceTemplateId();
                String stringUser = serviceTemplateTaskDO.getStringUser();
                String appId = serviceTemplateTaskDO.getAppId();
                Date date = workVO.getGmtBiz();
                boolean isSuccessExecute = serviceMessageManager.serviceGuidanceMsgSent(new FsUserVO(stringUser), appId, serviceTemplateId, DateFormatUtils.formatDate(date), DateFormatUtils.getDatesDelay(date, 3));
                if (!isSuccessExecute) {
                    BaseResult<Void> executeResult = workCallBackService.failed(workVO.getId(), "serviceMessageManager.serviceGuidanceMsgSent error .isSuccessExecute=" + isSuccessExecute);
                    if (!executeResult.isSuccess()) {
                        logger.error("workCallBackService.failed error : workVO[{}], executeResult[{}]", workVO, executeResult);
                    }
                } else {
                    BaseResult<Void> executeSuccessResult = workCallBackService.success(workVO.getId());
                    if (!executeSuccessResult.isSuccess()) {
                        logger.error("workCallBackService.success failed : workVO[{}], executeSuccessResult[{}]", workVO, executeSuccessResult);
                    }
                }
            } catch (Exception e) {
                logger.warn("processWork find exception  : workVO[{}]", workVO, e);
                BaseResult<Void> executeResult = workCallBackService.failed(workVO.getId(), "serviceMessageManager.serviceGuidanceMsgSent exception ");
                if (!executeResult.isSuccess()) {
                    logger.error("workCallBackService.failed in exception error : workVO[{}], executeResult[{}]", workVO, executeResult);
                }
            }
        });
        return new BaseResult<>();
    }
}
