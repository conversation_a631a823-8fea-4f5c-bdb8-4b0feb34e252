package com.facishare.open.operating.center.manager;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/1.
 */
public interface FirstTimeEventManager {

    /**
     * 判断是否每一次事件，注意：此方法返回true时会保存事件记录
     * @param eventFlag
     * @param eventExecutor
     * @return
     */
    boolean isFirstTime(String eventFlag, String eventExecutor);

    /**
     * 判断是否每一次事件
     * @param eventFlag
     * @param eventExecutor
     * @param onlyQuery true 仅查询，不记录事件标识， false 查询结果的同时会保存事件记录
     * @return
     */
    boolean isFirstTime(String eventFlag, String eventExecutor, boolean onlyQuery);
}
