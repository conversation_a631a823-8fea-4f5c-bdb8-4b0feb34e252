package com.facishare.open.operating.center.manager;

import com.facishare.open.app.pay.mq.api.item.*;

/**
 * Description: 付费应用试用事件处理类
 * User: zhouq
 * Date: 2016/7/28
 */
public interface OpenAppPayManager {
    /**
     * 企业试用操作
     * @param eaAppTrialItem
     */
    void eaAppTrail(EaAppTrialItem eaAppTrialItem);

    /**
     * 个人试用操作
     * @param employeeAppTrialItem
     */
    void employeeAppTrail(EmployeeAppTrialItem employeeAppTrialItem);

    /**
     * 企业试用过期操作
     * @param eaAppTrialExpItem
     */
    void eaAppTrialExpire(EaAppTrialExpItem eaAppTrialExpItem);

    /**
     * 个人试用过期操作
     * @param employeeAppTrialExpItem
     */
    void employeeAppTrialExpire(EmployeeAppTrialExpItem employeeAppTrialExpItem);


    /**
     * 企业购买操作
     * @param eaAppPurchaseItem
     */
    void sendEaAppPurchase(EaAppPurchaseItem eaAppPurchaseItem);

    /**
     * 企业购买到期操作
     * @param eaAppPurchaseExpItem
     */
    void sendEaAppPurchaseExpire(EaAppPurchaseExpItem eaAppPurchaseExpItem);

    /**
     * 汇聚启停用付费应用
     * @param appOnOffItem
     */
    void sendAppOnOffItem(AppOnOffItem appOnOffItem);
}
