package com.facishare.open.app.center.mq.listener;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.mq.handler.AddressBookEventHandler;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 组织架构通用MQ:人员变更事件监听
 * topic:  employeeChangeTopic
 * consumerGroup: fs-open-app-center-provider-organization-listener
 * 参考： <a href="http://wiki.firstshare.cn/pages/viewpage.action?pageId=65278404">员工部门变动监听mq</a>
 */
@Service
@Slf4j
public class EmployeeChangedListener extends OrganizationChangedListener {

    @Resource
    private AddressBookEventHandler addressBookEventHandler;
    @Resource
    private EIEAConverter eieaConverter;

    public EmployeeChangedListener() {
        super();
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        try {
            EmployeeDto oldEmployeeDto = event.getOldEmployeeDto();
            EmployeeDto newEmployeeDto = event.getNewEmployeeDto();

            // 处理人员停用
            if (Objects.nonNull(oldEmployeeDto) && Objects.nonNull(newEmployeeDto)) {
                EmployeeEntityStatus oldStatus = oldEmployeeDto.getStatus();
                EmployeeEntityStatus newStatus = newEmployeeDto.getStatus();

                // 老状态!=停用 && 新状态==停用
                if (!EmployeeEntityStatus.STOP.equals(oldStatus)
                        && EmployeeEntityStatus.STOP.equals(newStatus) ) {

                    log.info("人员停用 enterpriseId[{}], employeeId[{}], employeeName[{}]",
                            oldEmployeeDto.getEnterpriseId(), oldEmployeeDto.getEmployeeId(), oldEmployeeDto.getName());

                    // 删除应用可见范围
                    String ea = eieaConverter.enterpriseIdToAccount(oldEmployeeDto.getEnterpriseId());
                    addressBookEventHandler.removeUserIdInView(ea, newEmployeeDto.getEmployeeId());
                }
            }
        } catch (Exception e) {
            log.error("onEmployeeChanged error. event[{}]", event, e);
            throw e;
        }
    }
}
