package com.facishare.open.app.center.manager;

import com.facishare.open.common.model.FsUserVO;

import java.util.List;

/**
 * @describe: 用户相关的manager
 * @author: xiaoweiwei
 * @date: 2016/4/18 19:52
 */
public interface UserManager {
    /**
     * 判断是否是企业管理员
     *
     * @param fsUserVO
     * @return
     */
    boolean isAdmin(FsUserVO fsUserVO);

    /**
     * 获取企业管理员列表.
     *
     * @param fsEa 企业账号
     * @return 管理员列表
     */
    List<Integer> getAdminIds(final String fsEa);
}
