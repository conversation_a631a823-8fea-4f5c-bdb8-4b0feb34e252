package com.facishare.open.app.ad.service.impl;

import com.facishare.open.app.ad.api.enums.AppAdCodeEnum;
import com.facishare.open.app.ad.api.service.AppComponentSortService;
import com.facishare.open.app.ad.mongo.dao.AppComponentSortDAO;
import com.facishare.open.app.ad.model.AppComponentSortDO;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * impl.
 * Created by zenglb on 2016/8/17.
 */
@Service("appComponentSortServiceImpl")
public class AppComponentSortServiceImpl implements AppComponentSortService {
    private final Logger logger = LoggerFactory.getLogger(AppComponentSortServiceImpl.class);

    @Resource
    private AppComponentSortDAO appComponentSortDao;

    @Override
    public BaseResult<Void> saveComponentSort(String fsUserId, String componentType, List<String> componentIds) {
        if (CollectionUtils.isEmpty(componentIds)|| !FsUserVO.isFsUserString(fsUserId) || StringUtils.isEmpty(componentType)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            AppComponentSortDO appComponentSort = new AppComponentSortDO();
            appComponentSort.setFsUserId(fsUserId);
            appComponentSort.setComponentType(componentType);
            appComponentSort.setComponentIds(componentIds);
            appComponentSort.setGmtCreate(new Date());
            appComponentSort.setGmtModified(new Date());
            appComponentSortDao.insertOrUpdateComponentSort(appComponentSort);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("saveComponentSort failed! fsUserId[{}], componentType[{}], componentIds[{}]", fsUserId, componentType, componentIds, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("saveComponentSort failed! fsUserId[{}], componentType[{}], componentIds[{}]", fsUserId, componentType, componentIds, e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<List<String>> queryComponentSort(String fsUserId, String componentType) {
        if (!FsUserVO.isFsUserString(fsUserId) || StringUtils.isEmpty(componentType)) {
            return new BaseResult<>(AppAdCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            AppComponentSortDO appComponentSort = appComponentSortDao.selectOne(fsUserId, componentType);
            List<String> result = null;
            if (null != appComponentSort) {
                result = appComponentSort.getComponentIds();
            }
            return new BaseResult<>(result);
        } catch (BizException e) {
            logger.warn("queryComponentSort failed! fsUserId[{}], componentType[{}]", fsUserId, componentType, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("queryComponentSort failed! fsUserId[{}], componentType[{}]", fsUserId, componentType, e);
            return new BaseResult<>(AppAdCodeEnum.SYSTEM_EXCEPTION);
        }
    }
}
