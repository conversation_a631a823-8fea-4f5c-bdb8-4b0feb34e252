package com.facishare.open.app.center.mq.item;

import com.facishare.open.app.center.mq.item.base.ProtoBase;
import com.google.common.base.MoreObjects;
import io.protostuff.Tag;

import java.util.List;

/**
 * Created by xialf on 8/12/16.
 *
 * <AUTHOR>
 */
public class CrmInstructItem extends ProtoBase {
    public static final int FLAG = 9999; //RocketMQ Flag
    public static final String TOPIC = "NONE"; //RocketMQ Topic

    public static final int ADD = 1;
    public static final int REMOVE = 2;
    public static final int NONE = 3;

    @Tag(1)
    private int crmViewAction; //ADD/REMOVE/NONE

    @Tag(2)
    private int crmAvailAction; //ADD/REMOVE/NONE

    @Tag(3)
    private String fsEa;

    @Tag(4)
    private List<Integer> userIds;

    public int getCrmViewAction() {
        return crmViewAction;
    }

    public void setCrmViewAction(int crmViewAction) {
        this.crmViewAction = crmViewAction;
    }

    public int getCrmAvailAction() {
        return crmAvailAction;
    }

    public void setCrmAvailAction(int crmAvailAction) {
        this.crmAvailAction = crmAvailAction;
    }

    public String getFsEa() {
        return fsEa;
    }

    public void setFsEa(String fsEa) {
        this.fsEa = fsEa;
    }

    public List<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Integer> userIds) {
        this.userIds = userIds;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("crmViewAction", crmViewAction)
                .add("crmAvailAction", crmAvailAction)
                .add("fsEa", fsEa)
                .add("userIds", userIds)
                .toString();
    }
}
