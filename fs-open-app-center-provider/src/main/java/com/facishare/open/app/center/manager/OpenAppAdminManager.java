package com.facishare.open.app.center.manager;

import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.common.model.FsUserVO;

import java.util.Collection;
import java.util.List;

public interface OpenAppAdminManager {

    /**
     * 是否（任何一个）应用管理员
     * @param fsUserId E.XX.XXX
     * @return
     */
    boolean isAppAdmin(String fsUserId);

    /**
     * 判断是否是应用管理员
     * @param fsUserId E.fs.UserId
     * @param appId 应用Id
     * @return boolean
     */
    boolean isOpenAppAdmin(final String fsUserId, final String appId);

    /**
     * 查找用户为指定应用的管事员记录总数.
     *
     * @param fsUserId 纷享账号
     */
    int getAppCount(final String fsUserId, final String appId);


    /**
     * 获取用户做为服务号管理员的服务号个数.
     */
    int getServicesCount(final String fsUserId);

    /**
     *
     * @param fsUserId
     * @param appTypes
     * @return
     */
    int getServicesCount(String fsUserId, List<Integer> appTypes);

    /**
     * 获取用户可以管理的应用数(应用管理员).
     *
     * @param fsUserId 用户的纷享id
     * @return 应用数量
     */
    int getOpenAppsCount(final String fsUserId);

    /**
     * 查询用户可以管理的应用列表(不包括服务号).
     *
     * @param fsUserId 用户的纷享id
     * @return 应用列表id列表
     */
    List<String> queryOpenAppIdList(final String fsUserId);

    /**
     * 查询用户可以管理的服务号列表.
     *
     * @param fsUserId 用户的纷享id
     * @return 应用列表id列表
     */
    List<String> queryServiceIdList(final String fsUserId);

    /**
     * 查询指定企业中可以管理指定应用的所有应用管理员.
     *
     * @param fsEa  企业id
     * @param appId 应用id
     * @return 所有应用管理员的纷享id
     */
    List<String> queryAppAdminIds(final String fsEa, final String appId);

    List<Integer> queryAppAdminIdsInDB(final String fsEa, final String appId);

    /**
     * 更新企业中应用的所有应用管理员(多删少增),如果是CRM还会受到配额的限制.
     *
     * @param fsEa            企业id
     * @param appId           应用id
     * @param appType         应用类型
     * @param appAdminIds     新的全量应用管理员id列表
     * @param operationSource
     */
    void updateAppAdminIds(final String fsEa, final String appId, final Integer appType, final Collection<String> appAdminIds,
                           final Integer operationSource, Integer userId);

    void updateAppAdminIds(String fsEa, String appId, Integer appType, Collection<String> appAdminIds,
                           Integer operationSource, Integer userId, boolean checkQuota);

    void addAppAdminIds(final String fsEa, final String appId, final Integer appType, final Collection<Integer> appAdminIds,
                        final Integer operationSource, Integer userId);

    void removeAppAdminIds(final String fsEa, final String appId, final Integer appType, final Collection<Integer> appAdminIdsToRemove,
                           final Integer operationSource, Integer userId);
    /**
     * 查询企业列表.
     *
     * @param currentPage
     * @param pageSize
     * @return
     */
    List<String> queryPagerAllFsEas(Integer currentPage, Integer pageSize);

    /**
     * 批量查询企业下的所有的应用管理员.
     *
     * @param fsEas
     * @param appTypeList 应用类型列表(相应管理员类型列表),参照AppCenterEnum.AppType
     * @param appIds
     * @return
     */
    List<FsUserVO> queryAppAdminByFsEas(List<String> fsEas, List<String> appTypeList, List<String> appIds);

    /**
     * 查询企业数.
     */
    Long queryCountAllFsEas();

    /**
     * 修改应用类型.
     *
     */
    void modifyAppType(String appId, AppCenterEnum.AppType appType);

    /**
     * 批量保存.
     *
     * @param fsEa      企业账号
     * @param appId     应用id
     * @param appType   {@link com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType}
     * @param collect
     */
    void saveBatch(String fsEa, String appId, Integer appType, List<String> collect);
}
