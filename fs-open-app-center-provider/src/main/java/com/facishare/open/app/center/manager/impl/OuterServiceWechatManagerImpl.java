package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.vo.OuterServiceWechatVO;
import com.facishare.open.app.center.dao.OuterServiceWechatDAO;
import com.facishare.open.app.center.manager.OpenAppManager;
import com.facishare.open.app.center.manager.OuterServiceWechatManager;
import com.facishare.open.app.center.model.entity.OuterServiceWechatDO;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.app.center.utils.IdGenerateUtils;
import com.facishare.open.autoreplymsg.model.PlatformMetaSessionVO;
import com.facishare.open.autoreplymsg.result.CustomerSessionResult;
import com.facishare.open.autoreplymsg.service.MsgCustomerService;
import com.facishare.open.common.result.exception.BizException;
import com.google.common.base.Strings;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * User: zhouq
 * Date: 2016/11/1
 */
@Service
public class OuterServiceWechatManagerImpl implements OuterServiceWechatManager {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OuterServiceWechatDAO outerServiceWechatDAO;

    @Resource
    private MsgCustomerService msgCustomerService;

    @Resource
    private OpenAppManager openApManager;

    @Override
    public boolean createOuterServiceWechat(OuterServiceWechatVO entity) {
        OuterServiceWechatDO outerServiceWechatDO = VO2DO(entity);
        outerServiceWechatDO.setId(IdGenerateUtils.generateUUID());
        outerServiceWechatDAO.createOuterServiceWechat(outerServiceWechatDO);
        return true;
    }

    @Override
    public boolean updateOuterServiceWechat(OuterServiceWechatVO entity) {
        if (null != entity.getUnbindSource() && !Strings.isNullOrEmpty(entity.getAppId())) {
            //外联服务号取消授权后，需要在工作台的session名称前面加上（已取消授权）
            OpenAppDO openAppDO = openApManager.loadOpenAppFast(entity.getAppId());
            if (null != openAppDO && AppCenterEnum.AppType.OUT_SERVICE_APP.value() == openAppDO.getAppType()) {
                String workBeanchName = openAppDO.getServiceName();
                workBeanchName = workBeanchName + ConfigCenter.SERIVCE_UNBIND_NOTIC_PER;
                PlatformMetaSessionVO platformMetaSessionVO = new PlatformMetaSessionVO();
                platformMetaSessionVO.setCustomerSessionName(workBeanchName);
                CustomerSessionResult<Boolean> customerSessionResult = msgCustomerService.updatePlatformMetaSessionNew(openAppDO.getAppId(), openAppDO.getAppCreater(),
                        2, platformMetaSessionVO, CommonConstant.IS_OUTER_SERVICE);
                if (!customerSessionResult.isSuccess()) {
                    logger.warn("failed to call updatePlatformMetaSessionNew, fsEa[{}], appId[{}], platformMetaSessionVO[{}], serviceType[{}], result={}",
                            openAppDO.getAppCreater(), openAppDO.getAppId(), platformMetaSessionVO, 2, customerSessionResult);
                }
            }
        }
        OuterServiceWechatDO outerServiceWechatDO = VO2DO(entity);
        outerServiceWechatDAO.updateOuterServiceWechat(outerServiceWechatDO);
        return true;
    }

    @Override
    public OuterServiceWechatVO loadOuterServiceWechat(OuterServiceWechatVO entity) {
        OuterServiceWechatDO outerServiceWechatDO;
        OuterServiceWechatVO outerServiceWechatVO = new OuterServiceWechatVO();
        try {
            outerServiceWechatDO = VO2DO(entity);
            OuterServiceWechatDO outerEntity = outerServiceWechatDAO.loadOuterServiceWechat(outerServiceWechatDO);
            if (null != outerEntity) {
                PropertyUtils.copyProperties(outerServiceWechatVO, outerEntity);
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("PropertyUtils.copyProperties error, desc[{}]", outerServiceWechatVO, e);
            throw new BizException(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
        return outerServiceWechatVO;
    }

    private OuterServiceWechatDO VO2DO(OuterServiceWechatVO entity) {
        OuterServiceWechatDO outerServiceWechatDO = new OuterServiceWechatDO();
        try {
            PropertyUtils.copyProperties(outerServiceWechatDO, entity);
        } catch (Exception e) {
            logger.error("PropertyUtils.copyProperties error, orig[{}] desc[{}]", entity, outerServiceWechatDO, e);
            throw new BizException(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
        return outerServiceWechatDO;
    }

    @Override
    public List<String> queryAppIds(List<String> appIds, Integer status) {
        return outerServiceWechatDAO.queryAppIds(appIds, status);
    }
}
