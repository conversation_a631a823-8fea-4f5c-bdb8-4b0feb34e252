package com.facishare.open.app.center.service.impl;

import com.facishare.open.app.center.api.service.ServiceNumberRoleService;
import com.facishare.open.app.center.dao.OpenAppDAO;
import com.facishare.open.app.center.manager.ServiceNumberRoleManager;
import com.facishare.open.common.result.BaseResult;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * create by liq<PERSON>lin on 2021/6/16
 */
@Service("serviceNumberRoleServiceImpl")
@Slf4j
public class ServiceNumberRoleServiceImpl implements ServiceNumberRoleService {

    @Autowired
    private ServiceNumberRoleManager serviceNumberRoleManager;
    @Autowired
    private OpenAppDAO openAppDAO;

    @Override
    public BaseResult<List<String>> resetRoleMember(List<String> eaList) {

        log.info("serviceEaList.size = {}", eaList.size());
        log.info("serviceEaList = {}", eaList);

        List<String> failedEaList = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(eaList)) {

            for (String ea : eaList) {
                try {
                    serviceNumberRoleManager.resetServiceAdminRoleMember(ea);
                } catch (Exception e) {
                    log.warn("resetServiceAdminRoleMember error. ea[{}]", ea, e);
                    failedEaList.add(ea);
                }
            }
        }

        if (!failedEaList.isEmpty()) {
            log.error("failedEaList[{}]", failedEaList);
        }

        return new BaseResult(failedEaList);
    }

    @Override
    public BaseResult<List<String>> refreshHisRoleMember() {
        List<String> serviceEaList = openAppDAO.queryAllCustomServiceEa();

        List<String> failedEaList = this.resetRoleMember(serviceEaList).getResult();
        return new BaseResult(failedEaList);

    }
}
