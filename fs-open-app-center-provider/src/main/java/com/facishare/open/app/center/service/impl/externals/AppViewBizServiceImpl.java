package com.facishare.open.app.center.service.impl.externals;

import com.facishare.open.app.ad.api.cons.DefaultUserIdConstant;
import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.service.externals.AppViewBizService;
import com.facishare.open.app.center.api.utils.UserUtils;
import com.facishare.open.app.center.manager.OpenFsUserAppViewManager;
import com.facishare.open.app.pay.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * impl.
 * Created by zenglb on 2016/8/23.
 */
@Service("appViewBizServiceImpl")
public class AppViewBizServiceImpl implements AppViewBizService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OpenFsUserAppViewManager openFsUserAppViewManager;

    @Override
    public BaseResult<Integer> addEmployeeView(String componentId, String fsEa, List<Integer> userIdList) {
        return addEmployeeView(componentId, fsEa, userIdList, true);
    }

    @Override
    public BaseResult<Integer> addEmployeeView(String componentId, String fsEa, List<Integer> userIdList, Boolean checkQuota) {
        if (null == fsEa || StringUtils.isBlank(componentId) || CollectionUtils.isEmpty(userIdList) || null == checkQuota) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            if (Objects.equals(componentId, ConfigCenter.getCrmComponentId())) {
                userIdList = userIdList.stream().filter(userId -> !UserUtils.isOutUser(userId)).collect(Collectors.toList());
                if (userIdList.size() == 0) {
                    return new BaseResult<>(0);
                }
            }
            Integer result = openFsUserAppViewManager.addEmployeeView(new FsUserVO(fsEa, DefaultUserIdConstant.DEFAULT_USER_ID), componentId, userIdList, true, checkQuota);
            return new BaseResult<>(result);
        } catch (BizException e) {
            logger.warn("addEmployeeView failed, componentId[{}], fsEa[{}], userIdList[{}]",
                componentId,  fsEa, userIdList, e);
            return new com.facishare.open.app.center.api.result.BaseResult<>(e);
        } catch (Exception e) {
            logger.error("addEmployeeView failed, componentId[{}], fsEa[{}], userIdList[{}]",
                componentId,  fsEa, userIdList, e);
            return new com.facishare.open.app.center.api.result.BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Integer> removeEmployeeView(String componentId, String fsEa, List<Integer> userIdList) {
        if (null == fsEa || StringUtils.isBlank(componentId) || CollectionUtils.isEmpty(userIdList)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            Integer result = openFsUserAppViewManager.removeEmployeeView(new FsUserVO(fsEa, DefaultUserIdConstant.DEFAULT_USER_ID), componentId,
                    userIdList);
            return new BaseResult<>(result);
        } catch (BizException e) {
            logger.warn("removeEmployeeView failed, componentId[{}], fsEa[{}], userIdList[{}]",
                    componentId,  fsEa, userIdList, e);
            return new com.facishare.open.app.center.api.result.BaseResult<>(e);
        } catch (Exception e) {
            logger.error("removeEmployeeView failed, componentId[{}], fsEa[{}], userIdList[{}]",
                    componentId,  fsEa, userIdList, e);
            return new com.facishare.open.app.center.api.result.BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }
}
