package com.facishare.open.app.pay.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Id;
import java.util.Date;

/**
 * Created by huang<PERSON> on 2016/8/16.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TrialTerminationTask {
    @Id
    private Long id;
    private String appId;
    private String fsEa;
    private Date triggerTime;
    private Date gmtCreate;
    private Date gmtModified;
    private Integer status;

    public TrialTerminationTask() {
    }

    public TrialTerminationTask(String appId, String fsEa, Date triggerTime) {
        this.appId = appId;
        this.fsEa = fsEa;
        this.triggerTime = triggerTime;
    }
}
