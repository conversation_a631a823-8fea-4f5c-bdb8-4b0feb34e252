package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.api.model.vo.CallbackMsgVO;
import com.facishare.open.app.center.manager.WechatMsgManager;
import org.springframework.stereotype.Service;

/**
 * Created by huyue on 2016/11/2.
 */

@Service
public class WechatMsgManagerImpl implements WechatMsgManager {
    /**
     * 群发消息到微信回调服务
     *
     * @param callbackMsgVO
     * @return
     */
    @Override
    public boolean callbackOpenServiceExternalMsg(CallbackMsgVO callbackMsgVO) {
        return false;
    }
}
