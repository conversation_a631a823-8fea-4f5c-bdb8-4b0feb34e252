package com.facishare.open.app.center.manager;

import com.facishare.open.app.center.api.model.OpenAppDO;

import java.util.List;

/**
 * 应用下线业务
 */
public interface AppOfflineManager {
    /**
     * 按应用企业白名单过滤
     * @param ea
     * @param appIds
     * @return
     */
    List<String> filterOfflineAppWithWhiteEaList(String ea, List<String> appIds);

    /**
     * 按应用企业黑名单过滤
     * @param ea
     * @param appIds
     * @return
     */
    List<String> filterAppIdWithBlackEaList(String ea, List<String> appIds);

    /**
     * 按应用企业黑名单过滤
     * @param ea
     * @param appDOs
     * @return
     */
    List<OpenAppDO> filterAppDOWithBlackEaList(String ea, List<OpenAppDO> appDOs);
}
