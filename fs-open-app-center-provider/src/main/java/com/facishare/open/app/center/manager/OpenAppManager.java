package com.facishare.open.app.center.manager;

import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum.AppType;
import com.facishare.open.app.center.api.model.enums.AppStatus;
import com.facishare.open.app.center.api.model.vo.OpenAppVO;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;

import java.util.List;
import java.util.Map;

/**
 * 应用信息保存
 *
 * <AUTHOR>
 * @date 2015年8月27日
 */
public interface OpenAppManager {


    /**
     * 创建单个应用
     *
     * @param entity
     * @return
     */
    boolean createOpenApp(OpenAppDO entity);

    /**
     * 更新单个应用
     *
     * @param entity
     * @return
     */
    boolean updateOpenApp(OpenAppDO entity);

    /**
     * 加载单个app
     *
     * @param appId
     * @return
     */
    OpenAppDO loadOpenApp(String appId);

    /**
     * 加载单个app快速接口
     *
     * @param appId
     * @return
     */
    OpenAppDO loadOpenAppFast(String appId);

    /**
     * 批量查询app
     *
     * @param appIds
     * @return
     */
    List<OpenAppDO> loadOpenAppByIds(List<String> appIds);

    /**
     * 批量查询app快速接口
     *
     * @param appIds
     * @return
     */
    List<OpenAppDO> loadOpenAppByIdsFast(List<String> appIds);

    /**
     *批量查询app接口，返回数据中包含logo信息
     * @param appIds 应用ID
     * return  errCode errMsg OpenAppVO
     */
    List<OpenAppVO> queryOpenAppByIds(List<String> appIds);

    /**
     * 分页查询的app列表
     *
     * @param pager
     * @param needFilterAdded 是否需要过滤已添加的.
     * @return
     */
    Pager<OpenAppDO> queryPlatFormAppList(Pager<OpenAppDO> pager,boolean needFilterAdded);

    /**
     * 删除自定义应用
     *
     * @param fsAdminUser
     * @param enterpriseAccount
     * @param entity
     * @return
     */
    boolean deleteCustomApp(FsUserVO fsAdminUser, String enterpriseAccount, OpenAppDO entity);

    /**
     * 获取全部的第三方应用
     * @param pager
     * @return
     */
    Pager<OpenAppDO> queryDevAppList(Pager<OpenAppDO> pager);

    /**
     * 获取应用列表
     * @param pager
     * @param needFilterAdded
     * @param typeList
     * @return
     */
    Pager<OpenAppDO> queryPlatFormAppList(Pager<OpenAppDO> pager, boolean needFilterAdded, List<Integer> typeList);

    /**
     * 查询应用根据类型
     *
     * @param appTypeList
     * @return
     */
    List<OpenAppDO> queryAppListByStatusAndAppTypes(List<AppStatus> statusList, List<AppType> appTypeList);

    /**
     * 查询应用根据类型
     *
     * @param appTypeList
     * @return
     */
    List<OpenAppDO> queryAppListByStatusAndAppTypesOrderByAppName(List<AppStatus> statusList, List<AppType> appTypeList);

    /**
     * 查询第三方应用
     *
     * @param pager
     * @return
     */
    Pager<OpenAppDO> queryOpenAppByDev(Pager<OpenAppDO> pager);

    /**
     * 查询服务号
     *
     * @param pager
     * @return
     */
    Pager<OpenAppDO> queryServiceByDev(Pager<OpenAppDO> pager);

    /**
     * 通过开发商Id查询开发商的应用个数
     *
     * @param appTypeList
     * @param devIdList
     * @return
     */
    List<Map<String, String>> queryAppNumByIds(List<Integer> appTypeList, List<Long> devIdList);

    /**
     * 查询企业自建应用列表
     * @param fsAdminUser 用户
     * @param openAppDO 应用类型
     * @return 应用列表
     */
    List<OpenAppDO> queryCustomApps(FsUserVO fsAdminUser, OpenAppDO openAppDO);

    /**
     * 查询企业自建应用列表
     * @param appCreater 企业账号
     * @param appIds 应用ID列表
     * @return
     */
    List<OpenAppDO> queryCustomAppByIds(String appCreater, List<String> appIds);

    /**
     * 物理删除慎用.
     * @param appId
     */
    @Deprecated
    void deleteAppPhysical(String appId);

    /**
     * 查询企业自建的服务号数量
     * @param fsEa
     * @return
     */
    long queryServiceCount(String fsEa);

    /**
     * 查询企业所有服务号数(包括自建/基础/扩展服务号）
     * @param fsEa
     * @return
     */
    int queryEaAllServiceCount(String fsEa);

    /**
     * DO转VO 并设定Logo
     * @param openAppDOList
     * @return
     */
    List<OpenAppVO> queryOpenAppsByDO(List<OpenAppDO> openAppDOList);

    /**
     * 根据管理员和类型查询
     * @param user
     * @param appTypes
     * @return
     */
    List<OpenAppDO> queryServicesByAdminAndType(FsUserVO user, List<Integer> appTypes);

    /**
     * 根据服务号管理员和类型查询
     * @param user
     * @param appTypes
     * @return
     */
    List<OpenAppDO> queryServicesByServiceAdmin(FsUserVO user, List<Integer> appTypes);

    boolean existsAppName(String appName, String fsEa, AppCenterEnum.AppType appType);

    List<OpenAppDO> queryByAppName(String appName, String fsEa, AppCenterEnum.AppType appType);
}
