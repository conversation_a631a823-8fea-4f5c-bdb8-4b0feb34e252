package com.facishare.open.app.center.service.impl;

import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.model.vo.ComponentLoginUrlVO;
import com.facishare.open.app.center.api.model.vo.OpenComponentUrlGrayVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenComponentUrlGrayService;
import com.facishare.open.app.center.manager.OpenComponentUrlGrayManager;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * impl.
 * Created by zenglb on 2016/4/13.
 */
@Service("openComponentUrlGrayServiceImpl")
public class OpenComponentUrlGrayServiceImpl implements OpenComponentUrlGrayService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OpenComponentUrlGrayManager openComponentUrlGrayManager;

    @Override
    public BaseResult<OpenComponentUrlGrayVO> queryByComponentId(FsUserVO fsUserVO, String componentId) {
        if (StringUtils.isEmpty(componentId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            OpenComponentUrlGrayVO openComponentUrlGrayVO = openComponentUrlGrayManager.queryByComponentId(componentId);
            return new BaseResult<>(openComponentUrlGrayVO);
        } catch (BizException e) {
            logger.warn("queryByComponentId failed,fsUserVO[" + fsUserVO + "] componentId[" + componentId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("queryByComponentId failed,fsUserVO[" + fsUserVO + "] componentId[" + componentId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<List<OpenComponentUrlGrayVO>> queryByComponentIdsBatch(FsUserVO fsUserVO, List<String> componentIds) {
        if (CollectionUtils.isEmpty(componentIds)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            List<OpenComponentUrlGrayVO> openComponentUrlGrayVOs = openComponentUrlGrayManager.queryByComponentIdsBatch(componentIds);
            return new BaseResult<>(openComponentUrlGrayVOs);
        } catch (BizException e) {
            logger.warn("queryByComponentIdBatch failed,fsUserVO[" + fsUserVO + "] componentIds[" + componentIds + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("queryByComponentIdBatch failed,fsUserVO[" + fsUserVO + "] componentIds[" + componentIds + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<String> saveOpenComponentUrlGray(FsUserVO fsUserVO, OpenComponentUrlGrayVO openComponentUrlGrayVO) {
        if (null == openComponentUrlGrayVO) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            openComponentUrlGrayManager.saveOpenComponentUrlGray(openComponentUrlGrayVO);
            return new BaseResult<>(openComponentUrlGrayVO.getComponentId());
        } catch (BizException e) {
            logger.warn("saveOpenComponentUrlGray failed,fsUserVO[" + fsUserVO + "] openComponentUrlGrayVO[" + openComponentUrlGrayVO + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("saveOpenComponentUrlGray failed,fsUserVO[" + fsUserVO + "] componentIds[" + openComponentUrlGrayVO + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }
}
