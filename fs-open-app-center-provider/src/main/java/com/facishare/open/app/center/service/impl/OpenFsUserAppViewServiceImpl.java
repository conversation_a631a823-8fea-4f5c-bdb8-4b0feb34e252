package com.facishare.open.app.center.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.codahale.metrics.Gauge;
import com.codahale.metrics.Metric;
import com.codahale.metrics.MetricRegistry;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.ad.api.cons.DefaultUserIdConstant;
import com.facishare.open.app.center.api.constants.CommonConstant;
import com.facishare.open.app.center.api.model.*;
import com.facishare.open.app.center.api.model.enums.*;
import com.facishare.open.app.center.api.model.vo.ComponentLoginUrlVO;
import com.facishare.open.app.center.api.model.vo.NotifyUserViewVO;
import com.facishare.open.app.center.api.model.vo.OpenAppComponentVO;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.AppVOListResult;
import com.facishare.open.app.center.api.result.AppViewResult;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.api.utils.ScopeCenter;
import com.facishare.open.app.center.manager.*;
import com.facishare.open.app.common.util.StopWatch;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.oauth.model.enums.AccessTypeEnum;
import com.facishare.open.oauth.service.AppService;
import com.facishare.webpage.customer.api.constant.PaaSStatus;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.ClearTenantCacheArg;
import com.facishare.webpage.customer.api.model.arg.SavePaasAppArg;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.metrics.CounterService;
import com.fxiaoke.metrics.MetricsConfiguration;
import com.google.common.collect.Lists;
import com.google.common.collect.Queues;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2015年8月28日
 */
@Service("openFsUserAppViewServiceImpl")
@Slf4j
public class OpenFsUserAppViewServiceImpl implements OpenFsUserAppViewService {
    Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OpenFsUserAppViewManager openFsUserAppViewManager;

    @Resource
    private OpenDictManager openDictManager;

    @Resource
    private OpenAppManager openAppManager;

    @Resource
    private OpenAppComponentManager openAppComponentManager;

    @Resource
    private ComponentViewManager componentViewManager;

    @Resource
    private QuotaService quotaService;

    @Resource
    private AppService appService;

    @Resource
    private OpenComponentLoginUrlManager openComponentLoginUrlManager;

    @Resource
    private CounterService counterService;

    @Resource
    private AppIconManager appIconManager;

    @Resource
    private AppManager appManager;

    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private PaaSAppRestService paaSAppRestService;

    private Queue<String> queue = Queues.newArrayBlockingQueue(1024);

    @PostConstruct
    void init() {
        String name = MetricRegistry.name(AppService.class, "queueSize");
        MetricRegistry metricRegistry = MetricsConfiguration.METRIC_REGISTRY;
        Map<String, Metric> map = metricRegistry.getMetrics();
        log.info("init queueSize metric, map:{}", JSONObject.toJSONString(map));
        if (null != map && map.containsKey(name)) {
            metricRegistry.remove(name);
            MetricsConfiguration.METRIC_REGISTRY.register(name, new Gauge<Integer>() {
                @Override
                public Integer getValue() {
                    return queue.size();
                }
            });
        }
    }


    @Value("${fs.open.app.center.app.fs.account}")
    private String FS_ACCOUNT;

    @Override
    public BaseResult<Boolean> isInServiceView(FsUserVO fsUserVO, String appId) {
        if (Objects.isNull(fsUserVO) || Objects.isNull(appId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            OpenAppDO openAppDO = openAppManager.loadOpenAppFast(appId);
            if (Objects.isNull(openAppDO)) {
                // 做容错处理，因为企业那边平台应用Session上行消息也会调用此接口判断
                return new BaseResult<>(true);
            }
            if (!Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value())
                    && !Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.SERVICE.value())) {
                // 做容错处理，非互联服务号及企业服务号则直接返回true。
                return new BaseResult<>(true);
            }

            boolean isLinkService = Objects.equals(openAppDO.getAppType(), AppCenterEnum.AppType.LINK_SERVICE.value());
            AppComponentTypeEnum componentTypeEnum = isLinkService ? AppComponentTypeEnum.LINK_SERVICE : AppComponentTypeEnum.SERVICE;
            boolean isComponentMember = openAppComponentManager.isComponentMember(appId, fsUserVO, componentTypeEnum);
            boolean isLinkServiceDownstreamViewMember = false;
            if (isLinkService && !isComponentMember) {
                isLinkServiceDownstreamViewMember =
                        openAppComponentManager.isLinkServiceDownstreamViewMember(fsUserVO, appId, openAppDO.getAppCreater());
            }
            return new BaseResult<>(isLinkServiceDownstreamViewMember || isComponentMember);
        } catch (BizException e) {
            logger.warn("isInServiceView error. fsUserVO[{}], appId[{}]", fsUserVO, appId, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("isInServiceView error. fsUserVO[{}], appId[{}]", fsUserVO, appId, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> saveFsUserAppViewList(FsUserVO fsAdminUser, String componentId, AppComponentTypeEnum viewType,
                                                  AppViewDO view) {
        if (null == fsAdminUser || StringUtils.isBlank(componentId) || null == viewType || null == view) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        //todo 业务监控例子
        try {//debug log
            logger.debug("counterService componentId={}", componentId);
            counterService.inc("com.facishare.open.app.center.service.impl.OpenFsUserAppViewServiceImpl.test.componentId." + 11);
        } catch (Exception e) {
            logger.error("counterService error componentId={}", componentId, e);
        }


        try {
            //使用配额来进行可见范围约束
            final OpenAppComponentDO componentDO = openAppComponentManager.loadOpenAppComponentById(componentId);
            final OpenAppDO appDO = openAppManager.loadOpenApp(componentDO.getAppId());
            EmployeeRange componentView = EmployeeRange.fromAppView(view);

            if (appDO.getAppType() == AppCenterEnum.AppType.DEV_APP.value()
                    && appDO.getPayType() == PayTypeEnum.CHARGE.getPayType()) { //第三方收费应用, 需要计算配额
                final Set<Integer> userIds = componentViewManager.employeeRange2UserIds(fsAdminUser, componentView);// todo step2

                //step2-获取配额并与上面的可见范围包含的员工人数比较
                final com.facishare.open.common.result.BaseResult<Integer> quotaResult = quotaService.queryQuota(fsAdminUser.getEnterpriseAccount(), componentDO.getAppId());
                if (!quotaResult.isSuccess()) {
                    throw new BizException(quotaResult);
                }
                if (quotaResult.getResult() < userIds.size()) { //配额不足
                    com.facishare.open.app.center.api.result.BaseResult<AppViewDO> componentViewResult =
                            loadAppViewByType(fsAdminUser, componentId, viewType);
                    if (!componentViewResult.isSuccess()) {
                        throw new BizException(componentViewResult);
                    }
                    AppViewDO dbAppView = componentViewResult.getResult();
                    Set<Integer> dbUserIds = componentViewManager.employeeRange2UserIds(fsAdminUser, EmployeeRange.fromAppView(dbAppView));
                    //在配额超支的情况下，可见范围可以单向减少，而且期间内不能添加新员工
                    if (!dbUserIds.containsAll(userIds)) {
                        logger.info("new userId found when reducing view. added [{}]",
                                org.apache.commons.collections.CollectionUtils.subtract(userIds, dbUserIds));
                        return new BaseResult<>(AppCenterCodeEnum.QUOTA_INSUFFICIENT);
                    } else {
                        logger.info("save view though view number exceeds quota. removed [{}]",
                                org.apache.commons.collections.CollectionUtils.subtract(dbUserIds, userIds));
                    }
                }
            }

            openFsUserAppViewManager.saveFsUserAppViewList(fsAdminUser, componentId, viewType, view);
//            openFsUserAppViewManager.deleteViewCache(fsAdminUser,componentDO.getAppId());
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("save app visible error！fsAdminUser[" + fsAdminUser + "] componentId[" + componentId + "]viewType["
                    + viewType + "] view[" + view + "]", e);
            return new BaseResult<Void>(e);
        } catch (Exception e) {
            logger.error("save app visible error！fsAdminUser[" + fsAdminUser + "] componentId[" + componentId + "]viewType["
                    + viewType + "] view[" + view + "]", e);
            return new BaseResult<Void>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> saveViewWithoutQuota(String fsEa, String componentId, AppViewDO view) {
        if (null == fsEa || StringUtils.isBlank(componentId) || null == view) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            //使用配额来进行可见范围约束
            final OpenAppComponentDO componentDO = openAppComponentManager.loadOpenAppComponentById(componentId);
            if (null == componentDO) {
                return new BaseResult<>(AppCenterCodeEnum.COMPONENT_NOT_EXIST);
            }
            openFsUserAppViewManager.saveFsUserAppViewList(new FsUserVO(fsEa, DefaultUserIdConstant.DEFAULT_USER_ID), componentId,
                    AppComponentTypeEnum.getByCode(componentDO.getComponentType()), view);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("fail to finish saveViewWithoutQuota, fsEa[{}], componentId[{}], view[{}]",
                    fsEa, componentId, view, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("fail to finish saveViewWithoutQuota, fsEa[{}], componentId[{}], view[{}]",
                    fsEa, componentId, view, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> clearView(FsUserVO fsAdmin, String appId) {
        if (null == fsAdmin || StringUtils.isBlank(appId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            final List<OpenAppComponentDO> componentDOs =
                    openAppComponentManager.queryAppComponentListByAppIdAndStatus(appId, AppComponentStatus.VALID.getStatus());
            EmployeeRange view = new EmployeeRange();
            for (final OpenAppComponentDO componentDO : componentDOs) {
                AppComponentTypeEnum componentType = AppComponentTypeEnum.getByCode(componentDO.getComponentType());
                openFsUserAppViewManager.saveFsUserAppViewList(fsAdmin, componentDO.getComponentId(), componentType, view.toAppView());
            }
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("fail to finish clear view, user={}, appId={}",
                    fsAdmin, appId, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("fail to finish clear view, user={}, appId={}",
                    fsAdmin, appId, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    @Deprecated
    public BaseResult<Void> saveOnlyUserComponentViewList(FsUserVO fsAdminUser, String componentId,
                                                          AppComponentTypeEnum viewType,
                                                          List<Integer> insertUserList, List<Integer> deleteUserList) {
        if (null == fsAdminUser || StringUtils.isBlank(componentId) || null == viewType) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            openFsUserAppViewManager.saveOnlyUserComponentViewList(fsAdminUser, componentId, viewType,
                    insertUserList, deleteUserList);
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn(
                    "saveOnlyUserComponentViewList app visible warn！fsAdminUser[" + fsAdminUser + "] componentId[" + componentId + "]viewType["
                            + viewType, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error(
                    "saveOnlyUserComponentViewList app visible error！fsAdminUser[" + fsAdminUser + "] componentId[" + componentId + "]viewType["
                            + viewType, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    @Deprecated
    public BaseResult<Void> saveOnlyUserAllComponentViewList(FsUserVO fsAdminUser, String appId, List<Integer> insertUserList, List<Integer> deleteUserList) {
        if (null == fsAdminUser || StringUtils.isBlank(appId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            List<OpenAppComponentDO> openAppComponentDOs = openAppComponentManager.queryAppComponentListByAppIdAndStatus(appId,
                    AppComponentStatus.VALID.getStatus());
            for (OpenAppComponentDO openAppComponentDO : openAppComponentDOs) {
                openFsUserAppViewManager.saveOnlyUserComponentViewList(fsAdminUser, openAppComponentDO.getComponentId(),
                        AppComponentTypeEnum.getByCode(openAppComponentDO.getComponentType()), insertUserList, deleteUserList);
            }
            return new BaseResult<>();
        } catch (BizException e) {
            logger.warn("saveOnlyUserAllComponentViewList warn！fsAdminUser[{}] ,appId[{}]", fsAdminUser, appId, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.warn("saveOnlyUserAllComponentViewList error！fsAdminUser[{}] ,appId[{}]", fsAdminUser, appId, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<List<UserCanViewListVO>> queryComponentsByFsUser(FsUserVO fsUser, AppAccessTypeEnum appAccessTypeEnum) {
        if (null == fsUser || null == appAccessTypeEnum) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        AppComponentTypeEnum componentTypeEnum = null;
        //按类型显示头像
        IconType iconType = IconType.ANDROID;
        if (appAccessTypeEnum == AppAccessTypeEnum.ANDROID || appAccessTypeEnum == AppAccessTypeEnum.IOS) {
            componentTypeEnum = AppComponentTypeEnum.APP;
            if (appAccessTypeEnum == AppAccessTypeEnum.IOS) {
                iconType = IconType.IOS;
            }
        }
        if (appAccessTypeEnum == AppAccessTypeEnum.WEB) {
            componentTypeEnum = AppComponentTypeEnum.WEB;
            iconType = IconType.WEB;
        }
        if (appAccessTypeEnum == AppAccessTypeEnum.SERVICE) {
            componentTypeEnum = AppComponentTypeEnum.SERVICE;
            iconType = IconType.WEB;
        }
        if (appAccessTypeEnum == AppAccessTypeEnum.LINK_SERVICE) {
            componentTypeEnum = AppComponentTypeEnum.LINK_SERVICE;
            iconType = IconType.WEB;
        }

        try {
            StopWatch stopWatch = StopWatch.create("queryComponentsByFsUser");

            // 1.获取个人可见组件列表
            List<OpenAppComponentVO> compVOList = openFsUserAppViewManager.queryComponentList(fsUser, Lists.newArrayList(componentTypeEnum));
            if (CollectionUtils.isEmpty(compVOList)) {
                return new BaseResult<>(new ArrayList<>());
            }
            List<UserCanViewListVO> openCanViewComponentVoList = new ArrayList<>();
            List<String> componentIds = compVOList.stream().map(OpenAppComponentVO::getComponentId)
                    .collect(Collectors.toList());
            stopWatch.lap("queryComponentList");

            Map<String, ComponentLoginUrlVO> componentLoginUrlVOMap = new HashMap<>();
            List<ComponentLoginUrlVO> componentLoginUrlVOs = openComponentLoginUrlManager.queryLoginUrlByFsEaAndComponentIdsBatch(fsUser.getEnterpriseAccount(), componentIds);
            componentLoginUrlVOs.forEach(componentLoginUrlVO -> {
                componentLoginUrlVOMap.put(componentLoginUrlVO.getComponentId(), componentLoginUrlVO);
            });
            stopWatch.lap("queryLoginUrlByFsEaAndComponentIdsBatch");

            // 2.获取补充信息
            for (OpenAppComponentVO compVO : compVOList) {
                UserCanViewListVO openCanViewCompVO = new UserCanViewListVO();
                openCanViewCompVO.setIsFsApp(CommonConstant.NO);
                // 是否是纷享公司创建的appId（前端锚点展示）
                if (FS_ACCOUNT.equals(compVO.getAppCreater())) {
                    openCanViewCompVO.setIsFsApp(CommonConstant.YES);
                }
                openCanViewCompVO.setAppId(compVO.getAppId());
                openCanViewCompVO.setComponentId(compVO.getComponentId());
                openCanViewCompVO.setBindTime(compVO.getAppAuthGmtCreate());
                //调整基础应用的授权时间,用于web的排序.do it by lambo@************
                if (compVO.getAppType() == AppCenterEnum.AppType.BASE_APP.value()) {
                    openCanViewCompVO.setBindTime(compVO.getOpenAppComponentDO().getGmtCreate());
                }

                // 使用带灰度的跳转地址接口.add by lambo@********
                ComponentLoginUrlVO componentLoginUrlVO = componentLoginUrlVOMap.get(compVO.getComponentId());
                if (null != componentLoginUrlVO) {
                    openCanViewCompVO.setCallBackUrl(componentLoginUrlVO.getLoginUrl());
                }

                openCanViewCompVO.setAppType(compVO.getAppType());

                // app端根据应用所属的标签进行排序,需要设置组件的iconUrl todo 这里改为批量查询
                if (componentTypeEnum == AppComponentTypeEnum.APP) {
                    Integer appLabel = compVO.getComponentLabel();
                    if (null != appLabel) {
                        OpenDictDO dict = openDictManager.loadOpenDict(CommonConstant.DICT_TYPE_APP_LABEL, appLabel + "");
                        if (null != dict) {
                            openCanViewCompVO.setLabel(dict);
                            openCanViewCompVO.setComponentLabel(dict.getDictValue());
                        }
                    }
                }
                openCanViewCompVO.setImageUrl(appIconManager.queryIconUrlByUser(fsUser, openCanViewCompVO.getComponentId(), iconType)); // todo 改为批量接口
                // 特别说明：app。取服务号相关的名称，web直接应用 名称
                openCanViewCompVO.setComponentName(compVO.getComponentName());
                openCanViewComponentVoList.add(openCanViewCompVO);
            }
            stopWatch.lap("获取补充信息"); // ignoreI18n


            // app侧排序.先使用应用添加时间排序.再使用orderNo排序.
            if (componentTypeEnum == AppComponentTypeEnum.APP) {// app端根据应用所属的标签进行排序
                Collections.sort(openCanViewComponentVoList, (UserCanViewListVO o1, UserCanViewListVO o2) -> {
                    long a = (null == o1.getBindTime()) ? 0 : o1.getBindTime().getTime();
                    long b = (null == o2.getBindTime()) ? 0 : o2.getBindTime().getTime();
                    return a == b ? 0 : (a > b ? 1 : -1);
                });
                Collections.sort(openCanViewComponentVoList, (UserCanViewListVO o1, UserCanViewListVO o2) -> {
                    long a = (null == o1.getLabel()) ? 0 : o1.getLabel().getOrderNo();
                    long b = (null == o2.getLabel()) ? 0 : o2.getLabel().getOrderNo();
                    return a == b ? 0 : (a > b ? 1 : -1);
                });
            }
            stopWatch.lap("app侧排序"); // ignoreI18n

            // 应用埋点.
            Map<String, Object> map = new HashMap<>();
            try {
                map.put("fsEa", fsUser.getEnterpriseAccount());
                map.put("userId", fsUser.getUserId());
                map.put("componentSize", openCanViewComponentVoList.size());
                map.put("appAccessTypeEnum", appAccessTypeEnum);
                DataPersistor.asyncLog(BuriedPointBizEnum.QUERY_COMPONENTS_BY_FS_USER.getAction(), map);
            } catch (Exception e) {
                logger.error("BuriedPoint failed , serviceName[{}], map[{}]", BuriedPointBizEnum.QUERY_COMPONENTS_BY_FS_USER, map, e);
            }
            stopWatch.lap("应用埋点"); // ignoreI18n

            stopWatch.logSlow(500);

            return new BaseResult<>(openCanViewComponentVoList);
        } catch (BizException e) {
            logger.warn("failed to queryComponentsByFsUser: user={}, AppAccessTypeEnum={}",
                    fsUser, appAccessTypeEnum, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to queryComponentsByFsUser: user={}, AppAccessTypeEnum={}",
                    fsUser, appAccessTypeEnum, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public AppViewResult loadAppViewByType(FsUserVO user, String componentId, AppComponentTypeEnum viewType) {
        return loadAppViewByType(user, componentId, viewType, false);
    }

    @Override
    public BaseResult<AppViewDO> newLoadAppViewByType(FsUserVO user, String componentId, AppComponentTypeEnum viewType) {
        return loadAppViewByType(user, componentId, viewType, true);
    }

    private AppViewResult loadAppViewByType(FsUserVO user, String componentId, AppComponentTypeEnum viewType, boolean isNew) {
        AppViewResult result;
        if (null == user || StringUtils.isBlank(componentId) || null == viewType) {
            return new AppViewResult(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            AppViewDO appViewDO;
            if (isNew) {
                appViewDO = openFsUserAppViewManager.newLoadAppViewByType(user, componentId, viewType);
            } else {
                appViewDO = openFsUserAppViewManager.loadAppViewByType(user, componentId, viewType);
            }
            result = new AppViewResult(appViewDO);
        } catch (BizException e) {
            logger.warn(
                    "load app view by type error！user[" + user + "] appId[" + componentId + "]viewType[" + viewType + "]", e);
            result = new AppViewResult(e);
        } catch (Exception e) {
            logger.error("load app view by type error！user[" + user + "] appId[" + componentId + "]viewType[" + viewType
                    + "]", e);
            result = new AppViewResult(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
        return result;
    }

    @Override
    public BaseResult<EmployeeRange> loadComponentView(FsUserVO user, String componentId) {
        BaseResult<EmployeeRange> result = null;
        if (null == user || StringUtils.isBlank(componentId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try { // todo 可以修改为批量查询. add by lambo,to lambo。
            OpenAppComponentDO openAppComponentDO = openAppComponentManager.loadOpenAppComponentById(componentId);
            if (null == openAppComponentDO) {
                return new BaseResult<>(AppCenterCodeEnum.COMPONENT_NOT_EXIST);
            }
            AppComponentTypeEnum componentType = AppComponentTypeEnum.getByCode(openAppComponentDO.getComponentType());
            AppViewDO view = openFsUserAppViewManager.loadAppViewByType(user, componentId, componentType);
            result = new BaseResult<>(EmployeeRange.fromAppView(view));
        } catch (BizException e) {
            logger.warn("failed to loadAppViewByType: user=[" + user + "], componentId=[" + componentId + "]", e);
            result = new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to loadAppViewByType: user=[" + user + "], componentId=[" + componentId + "]", e);
            result = new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
        return result;
    }

    @Override
    public BaseResult<Void> notifyEndUsers(FsUserVO user, String appId, EmployeeRange view) {
        if (null == user || StringUtils.isBlank(appId) || null == view) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        BaseResult<Void> result;
        try {
            openFsUserAppViewManager.notifyUsersView(user, appId, view);
            result = new BaseResult<>();
        } catch (BizException e) {
            logger.warn("failed to openFsUserAppViewManager.notifyUsersView: user=[" + user + "], appId=[" + appId + "], view=[" + view + "]", e);
            result = new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to openFsUserAppViewManager.notifyUsersView: user=[" + user + "], appId=[" + appId + "], view=[" + view + "]", e);
            result = new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
        return result;
    }

    @Override
    @Deprecated
    public AppVOListResult queryAppListByFsUserId(FsUserVO fsUser, AppCenterEnum.ViewType viewType) {
        return null;
    }

    @Override
    public BaseResult<List<OpenAppDO>> queryVisibleAppList(FsUserVO fsUser) {
        if (null == fsUser) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            // 1.获取个人可见组件列表
            List<OpenAppDO> appDOs = openFsUserAppViewManager.queryVisibleAppList(fsUser);
            return new BaseResult<>(appDOs);
        } catch (BizException e) {
            logger.warn("failed to queryVisibleAppList: user=[" + fsUser + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to queryVisibleAppList: user=[" + fsUser + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Boolean> canAccessComponent(FsUserVO fsUser, String componentId) {
        if (null == fsUser || null == componentId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            // 1.判断个人是否可见组件
            return new BaseResult<>(openFsUserAppViewManager.canAccessComponent(fsUser, componentId));
        } catch (BizException e) {
            logger.warn("failed to canAccessComponent: user=[" + fsUser + "], componentId=[" + componentId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to canAccessComponent: user=[" + fsUser + "], componentId=[" + componentId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Integer> queryUsersCount(FsUserVO fsUser, String appId) {
        if (null == fsUser || null == appId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            return new BaseResult<>(openFsUserAppViewManager.queryUsers(fsUser, appId).size());
        } catch (BizException e) {
            logger.warn("failed to queryUsersCount: user=[" + fsUser + "], appId=[" + appId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to queryUsersCount: user=[" + fsUser + "], appId=[" + appId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Set<Integer>> queryUsers(FsUserVO fsUser, String appId) {
        if (null == fsUser || null == appId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            return new BaseResult<>(openFsUserAppViewManager.queryUsers(fsUser, appId));
        } catch (BizException e) {
            logger.warn("failed to queryUsers: user=[" + fsUser + "], appId=[" + appId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to queryUsers: user=[" + fsUser + "], appId=[" + appId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Map<String, Integer>> queryComponentUsersCount(FsUserVO fsUserVO, String componentId) {
        if (null == fsUserVO || StringUtils.isBlank(componentId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            Map<String, Integer> dataMap = new HashMap<>();
            // 获取该组件的可见范围
            final OpenAppComponentDO componentDo = openAppComponentManager.loadOpenAppComponentById(componentId);
            final AppViewDO appViewDO = openFsUserAppViewManager.loadAppViewByType(fsUserVO, componentId, AppComponentTypeEnum.getByCode(componentDo.getComponentType()));
            Set<Integer> userIds = componentViewManager.employeeRange2UserIds(fsUserVO, EmployeeRange.fromAppView(appViewDO));
            dataMap.put(componentDo.getComponentId(), userIds == null ? 0 : userIds.size());
            return new BaseResult<>(dataMap);
        } catch (Exception e) {
            logger.error("failed to queryComponentUsersCount: fsUserVO=[" + fsUserVO + "], componentId=[" + componentId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> notifyCrmShow(String componentId, Set<FsUserVO> fsUserVOs) {
        if (CollectionUtils.isEmpty(fsUserVOs) || null == componentId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            // 1.通知展示/隐藏 crm
            openFsUserAppViewManager.notifyCrmShow(componentId, fsUserVOs);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to notifyCrmShow: user=[" + fsUserVOs + "], componentId=[" + componentId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to notifyCrmShow: user=[" + fsUserVOs + "], componentId=[" + componentId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> deleteViewCache(FsUserVO fsUserVO, String appId) {
        if (null == fsUserVO || null == appId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            // 1.清除可见缓存
            openFsUserAppViewManager.deleteViewCache(fsUserVO, appId);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to deleteViewCache: user=[" + fsUserVO + "], appId=[" + appId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to deleteViewCache: user=[" + fsUserVO + "], appId=[" + appId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }


    @Override
    public BaseResult<Void> notifyUserView(String fsEa, String appId, Set<FsUserVO> fsUserVOs) {
        if (CollectionUtils.isEmpty(fsUserVOs) || null == appId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            // 1.通知终端拉取列表
            openFsUserAppViewManager.notifyUsersView(new FsUserVO(fsEa, DefaultUserIdConstant.DEFAULT_USER_ID), appId, fsUserVOs);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to notifyUserView: user=[" + fsUserVOs + "], appId=[" + appId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to notifyUserView: user=[" + fsUserVOs + "], appId=[" + appId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> updateAndNotifyApp(FsUserVO fsUserVO, String appId, Boolean isOff) {
        if (null == fsUserVO || null == appId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            //
            openFsUserAppViewManager.updateAndNotifyApp(fsUserVO, appId, isOff);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to updateAndNotifyApp: user=[" + fsUserVO + "], appId=[" + appId + "]", e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to updateAndNotifyApp: user=[" + fsUserVO + "], appId=[" + appId + "]", e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> updateAndNotifyApp(FsUserVO fsUserVO, String appId, Set<Integer> insertSessions, Set<Integer> delSessions) {
        if (null == fsUserVO || null == appId) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }

        try {
            final Set<FsUserVO> insertSessionVos = insertSessions.stream()
                    .map(userId -> new FsUserVO(fsUserVO.getEnterpriseAccount(), userId))
                    .collect(Collectors.toSet());
            final Set<FsUserVO> delSessionVos = delSessions.stream()
                    .map(userId -> new FsUserVO(fsUserVO.getEnterpriseAccount(), userId))
                    .collect(Collectors.toSet());
            openFsUserAppViewManager.updateAndNotifyApp(fsUserVO, appId, insertSessionVos, delSessionVos);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to updateAndNotifyApp: user[{}],appId[{}],insertSessions[{}],delSessions[{}]",
                    fsUserVO, appId, insertSessions, delSessions, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.error("failed to updateAndNotifyApp: user[{}],appId[{}],insertSessions[{}],delSessions[{}]",
                    fsUserVO, appId, insertSessions, delSessions, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> restrictView(final String fsEa, final String appId, final int quota) {
        if (StringUtils.isBlank(fsEa) || StringUtils.isBlank(appId) || quota < 0) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            appManager.clearView(fsEa, appId, quota);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to restoreView: fsEa[{}],appId[{}],quota[{}]", fsEa, appId, quota, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.warn("failed to restoreView: fsEa[{}],appId[{}],quota[{}]", fsEa, appId, quota, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> cleanViewCache(String fsEa, String appId) {
        if (StringUtils.isBlank(fsEa) || StringUtils.isBlank(appId)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        try {
            openFsUserAppViewManager.deleteViewCache(new FsUserVO(fsEa, DefaultUserIdConstant.DEFAULT_USER_ID), appId);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to cleanViewCache. fsEa[{}], appId[{}]", fsEa, appId, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.warn("failed to cleanViewCache. fsEa[{}], appId[{}]", fsEa, appId, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public BaseResult<Void> notifyUserViewAsync(FsUserVO fsUser, NotifyUserViewVO notifyUserViewVO) {
        try {
            openFsUserAppViewManager.notifyUserViewAsync(fsUser, notifyUserViewVO);
            return new BaseResult<>(AppCenterCodeEnum.SUCCESS);
        } catch (BizException e) {
            logger.warn("failed to notifyUserViewAsync. fsUser[{}], notifyUserViewVO[{}]", fsUser, notifyUserViewVO, e);
            return new BaseResult<>(e);
        } catch (Exception e) {
            logger.warn("failed to notifyUserViewAsync. fsUser[{}], notifyUserViewVO[{}]", fsUser, notifyUserViewVO, e);
            return new BaseResult<>(AppCenterCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public void saveAppComponentView(FsUserVO user, String componentId, int componentType, AppViewDO viewDo) {
        SavePaasAppArg arg = new SavePaasAppArg();
        int tenantId = eieaConverter.enterpriseAccountToId(user.getEnterpriseAccount());
        arg.setAppId(componentId);
        arg.setUserId(user.getUserId());
        arg.setTenantId(tenantId);//构建scope
        //判断是移动端App还是web端app
        if (componentType == AppComponentTypeEnum.APP.getType()) {
            arg.setAccessType(AccessTypeEnum.APP.getType());
        } else {
            arg.setAccessType(AccessTypeEnum.WEB.getType());
        }
        List<Scope> scopes = ScopeCenter.convertViewToScope(viewDo);
        arg.setScopes(scopes);
        arg.setStatus(PaaSStatus.SYS_STATUS);
        paaSAppRestService.saveTenantPaaSAppInfo(Integer.toString(tenantId), arg);
        //清除缓存
        ClearTenantCacheArg clearTenantCacheArg = ClearTenantCacheArg.builder().tenantId(tenantId).build();
        paaSAppRestService.clearTenantCache(Integer.toString(tenantId), clearTenantCacheArg);
    }

    @Override
    public void saveAppComponentStatus(SavePaasAppArg arg) {
        paaSAppRestService.saveTenantPaaSAppInfo(Integer.toString(arg.getTenantId()), arg);
        //清除缓存
        ClearTenantCacheArg clearTenantCacheArg = ClearTenantCacheArg.builder().tenantId(arg.getTenantId()).build();
        paaSAppRestService.clearTenantCache(Integer.toString(arg.getTenantId()), clearTenantCacheArg);
    }
}
