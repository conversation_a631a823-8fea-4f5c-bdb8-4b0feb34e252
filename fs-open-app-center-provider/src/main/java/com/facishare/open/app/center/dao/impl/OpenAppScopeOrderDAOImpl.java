package com.facishare.open.app.center.dao.impl;

import com.facishare.open.app.center.api.model.OpenAppScopeOrderDO;
import com.facishare.open.app.center.base.CommonDAO;
import com.facishare.open.app.center.dao.OpenAppScopeOrderDAO;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2015年8月28日
 */
@Repository
public class OpenAppScopeOrderDAOImpl extends CommonDAO<OpenAppScopeOrderDO> implements OpenAppScopeOrderDAO {

    @Override
    public List<OpenAppScopeOrderDO> getAppScopeByAppId(String appId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("appId", appId);
        return getList("openAppScopeOrder.getAppScopeByAppId", parameters);
    }

    @Override
    public void saveAppScopeOrder(OpenAppScopeOrderDO openAppScopeOrder) {
        this.save("openAppScopeOrder.saveOpenAppScopeOrder", openAppScopeOrder);
    }

    @Override
    public void insertAppScopeOrders(String appId, List<OpenAppScopeOrderDO> insertList) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("appId", appId);
        parameters.put("insertList", insertList);
        this.save("openAppScopeOrder.insertAppScopeOrders", parameters);
    }

    @Override
    public void deleteAppScopeOrders(String appId, List<OpenAppScopeOrderDO> deleteList) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("appId", appId);
        parameters.put("deleteList", deleteList);
        this.save("openAppScopeOrder.deleteAppScopeOrders", parameters);
    }

    @Override
    public int deleteAppScopesPhysical(String appId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("appId", appId);
        return this.save("openAppScopeOrder.deleteAppScopesPhysical", parameters);
    }
}
