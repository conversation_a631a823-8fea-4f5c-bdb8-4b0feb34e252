package com.facishare.open.app.center.service.impl;


import com.facishare.open.app.center.api.model.EnterpriseSimpleInfo;
import com.facishare.open.app.center.api.model.ShortEnterprisesResult;
import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEnterpriseService;
import com.facishare.open.app.center.manager.AddressBookEnterpriseManager;
import com.facishare.open.common.result.exception.BizException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/16
 */
@Service
public class OpenAppAddressBookEnterpriseServiceImpl implements OpenAppAddressBookEnterpriseService {
    @Resource
    private AddressBookEnterpriseManager addressBookEnterpriseManager;

    @Override
    public BaseResult<List<EnterpriseSimpleInfo>> getEnterpriseSimpleList(List<Integer> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return new BaseResult<>(Lists.newArrayList());
        }
        return new BaseResult<>(addressBookEnterpriseManager.getEnterpriseSimpleList(enterpriseIds));
    }

    @Override
    public BaseResult<ShortEnterprisesResult> getShortEnterprises(String enterpriseAccount, String keyword, int pageSize, int pageNumber) {
        if (StringUtils.isBlank(enterpriseAccount)) {
            throw new BizException(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        return new BaseResult<>(addressBookEnterpriseManager.getShortEnterprises(enterpriseAccount, keyword, pageSize, pageNumber));
    }
}
