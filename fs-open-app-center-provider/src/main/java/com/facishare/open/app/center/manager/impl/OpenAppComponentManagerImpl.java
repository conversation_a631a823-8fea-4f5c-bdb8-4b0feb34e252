package com.facishare.open.app.center.manager.impl;

import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeInfo;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.enums.AppComponentStatus;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.facishare.open.app.center.dao.OpenAppComponentDAO;
import com.facishare.open.app.center.manager.AddressBookEmployeeManager;
import com.facishare.open.app.center.manager.OpenAppComponentManager;
import com.facishare.open.app.center.manager.OpenFsUserAppViewManager;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.exception.BizException;
import com.fxiaoke.enterpriserelation.arg.ListCustomerServiceAuthedEmployeesByEasArg;
import com.fxiaoke.enterpriserelation.common.HeaderObj;
import com.fxiaoke.enterpriserelation.common.RestResult;
import com.fxiaoke.enterpriserelation.service.CustomerServiceService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OpenAppComponentManagerImpl implements OpenAppComponentManager {
    @Resource
    private OpenAppComponentDAO openAppComponentDAO;
    @Resource
    private OpenFsUserAppViewManager openFsUserAppViewManager;
    @Autowired
    private CustomerServiceService customerServiceService;
    @Resource
    private AddressBookEmployeeManager addressBookEmployeeManager;

    @Override
    public Boolean isLinkServiceDownstreamViewMember(FsUserVO fsUserVO, String appId, String upstreamEa) {
        ListCustomerServiceAuthedEmployeesByEasArg arg = ListCustomerServiceAuthedEmployeesByEasArg.builder().operatorEa(upstreamEa).appId(appId).destEas(Lists.newArrayList(fsUserVO.getEnterpriseAccount())).build();
        HeaderObj headerObj = HeaderObj.newInstance(upstreamEa, appId, null, null);
        RestResult<List<String>> result = customerServiceService.listCustomerServiceAuthedEmployeesByEas(headerObj, arg);
        if (!result.isSuccess()) {
            log.error("customerServiceService.listCustomerServiceAuthedEmployeesByEas error. arg[{}], result[{}]", arg, result);
            throw new BizException(result.getErrCode(), result.getErrMsg());
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            return false;
        } else {
            return result.getData().contains(fsUserVO.asStringUser());
        }
    }

    @Override
    public OpenAppComponentDO loadOpenAppComponentById(String componentId) {
        return openAppComponentDAO.loadOpenAppComponentById(componentId);
    }

    @Override
    public List<OpenAppComponentDO> queryOpenAppComponentByIds(List<String> componentIds) {
        if (CollectionUtils.isEmpty(componentIds)) {
            return new ArrayList<>();
        }
        return openAppComponentDAO.queryOpenAppComponentByIds(componentIds);
    }

    @Override
    public List<OpenAppComponentDO> queryAppComponentListByAppIdAndStatus(String appId, Integer status) {
        return openAppComponentDAO.queryAppComponentListByAppIdAndStatus(appId, status);
    }

    @Override
    public List<OpenAppComponentDO> queryAppComponentListByAppIds(List<String> appIds, Integer status, List<Integer> componentTypes) {
        return openAppComponentDAO.queryAppComponentListByAppIds(appIds, status, componentTypes);
    }

    @Override
    public void insertOpenAppComponent(OpenAppComponentDO entity) {
        openAppComponentDAO.insertOpenAppComponent(entity);
    }

    @Override
    public void insertOpenAppComponentBatch(List<OpenAppComponentDO> insertList) {
        openAppComponentDAO.insertOpenAppComponentBatch(insertList);
    }

    @Override
    public void updateOpenAppComponent(OpenAppComponentDO entity) {
        openAppComponentDAO.updateOpenAppComponent(entity);


    }

    @Override
    public void deleteOpenAppComponent(String componentId) {
        OpenAppComponentDO entity = new OpenAppComponentDO();
        entity.setComponentId(componentId);
        entity.setGmtModified(new Date());
        entity.setStatus(AppComponentStatus.INVALID.getStatus());
        openAppComponentDAO.updateOpenAppComponent(entity);
    }

    @Override
    public void deleteComponentPhysical(String componentId) {
        openAppComponentDAO.deleteComponentPhysical(componentId);
    }

    @Override
    public List<OpenAppComponentDO> queryAppComponentByType(String appId, AppComponentTypeEnum componentType) {
        List<OpenAppComponentDO> result = Lists.newArrayList();
        List<OpenAppComponentDO> componentDOList = this.queryAppComponentListByAppIdAndStatus(appId, AppComponentStatus.VALID.getStatus());
        if (!CollectionUtils.isEmpty(componentDOList)) {
            result = componentDOList.stream()
                    .filter(openAppComponentDO -> componentType.getType() == openAppComponentDO.getComponentType())
                    .collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public boolean isComponentMember(String appId, FsUserVO user, AppComponentTypeEnum componentType) {
        return queryAppComponentByType(appId, componentType).stream()
                .anyMatch(openAppComponentDO -> isAppComponentMember(openAppComponentDO, user));
    }

    private boolean isAppComponentMember(OpenAppComponentDO componentDO, FsUserVO user) {
        AppComponentTypeEnum componentType = AppComponentTypeEnum.getByCode(componentDO.getComponentType());
        final AppViewDO appViewDO = openFsUserAppViewManager.loadAppViewByType(user, componentDO.getComponentId(), componentType);
        EmployeeRange employeeRange = EmployeeRange.fromAppView(appViewDO);

        // 全公司直接返回true
        if (employeeRange.includeWholeCompany()) {
            return true;
        }

        // 在范围人员里返回true
        List<Integer> rangeMembers = employeeRange.getMember();
        if (!CollectionUtils.isEmpty(rangeMembers)) {
            if (rangeMembers.contains(user.getUserId())) {
                return true;
            }
        }

        // 在范围部门里则返回true
        List<Integer> rangeDepartmentList = employeeRange.getDepartment();
        if (!CollectionUtils.isEmpty(rangeDepartmentList)) {
            if (isAppComponentDeptMember(user, rangeDepartmentList)) {
                return true;
            }
        }

        return false;
    }

    private boolean isAppComponentDeptMember(FsUserVO user, List<Integer> rangeDepartmentList) {
        List<EmployeeInfo> employeeInfos = addressBookEmployeeManager.getEmployeeInfosNoAdminId(user.getEnterpriseAccount(), Lists.newArrayList(user.getUserId()));

        if (CollectionUtils.isEmpty(employeeInfos)) {
            return false;
        }
        EmployeeInfo employeeInfo = employeeInfos.get(0);
        List<Integer> deptList = employeeInfo.getFlatCirlceIds();
        if (CollectionUtils.isEmpty(deptList)) {
            return false;
        }
        Set<Integer> deptSet = new HashSet<>(deptList);
        return rangeDepartmentList.stream().anyMatch(deptSet::contains);
    }
}
