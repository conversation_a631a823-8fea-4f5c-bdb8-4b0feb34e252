package com.facishare.open.app.center.utils;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;

public class GrayReleaseBiz {
    private static FsGrayReleaseBiz gray = FsGrayRelease.getInstance("app-center");
    public static String useNewPaasAppGray = "useNewPaasAppGray";

    public static String goNewQuery = "goNewQuery";

    //应用中心发送系统消息,企业黑名单
    public static String sendSystemMessageBlackEaList = "sendSystemMessageBlackEaList";

    //应用中心通过纷享应用管家服务号发送图文消息,企业黑名单
    public static String sendImageTextMsgByFsAppButlerAppBlackEaList = "sendImageTextMsgByFsAppButlerAppBlackEaList";

    public static String newPollingGetAppListEaList = "newPollingGetAppListEaList";
    public static String newPollingGetServiceNumListEaList = "newPollingGetServiceNumListEaList";
    //应用中心通过纷享服务号发送文本消息,企业黑名单
    public static String sendTextMsgByFsAppBlackEaList = "sendTextMsgByFsAppBlackEaList";
    //应用中心通过玩转企业服务号发送图文消息,企业黑名单
    public static String sendImageTextMsgByPlayCompanyServiceBlackEaList = "sendImageTextMsgByPlayCompanyServiceBlackEaList";
    public static String newQuotaQueryGrayEas = "newQuotaQueryGrayEas";
    public static String directNewQuotaQueryGrayEas = "directNewQuotaQueryGrayEas";
    public static String licenseQuotaQueryGrayEas = "licenseQuotaQueryGrayEas";
    public static String notSyncCrmViewGrayEas = "notSyncCrmViewGrayEas";

    public static boolean isAllowForEa(String business, String ea) {
        if (Strings.isNullOrEmpty(ea)) {
            return false;
        }
        return gray.isAllow(business, ea);
    }

}
