package com.facishare.open.app.center.service.impl;

import com.facishare.open.app.center.api.model.Employee;
import com.facishare.open.app.center.api.model.EmployeeInfo;
import com.facishare.open.app.center.api.model.enums.AppCenterCodeEnum;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAddressBookEmployeeService;
import com.facishare.open.app.center.manager.AddressBookEmployeeManager;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/16
 */
@Service
public class OpenAppAddressBookEmployeeServiceImpl implements OpenAppAddressBookEmployeeService {
    @Resource
    private AddressBookEmployeeManager addressBookEmployeeManager;

    @Override
    public BaseResult<List<Integer>> filterStopEmployee(String enterpriseAccount, List<Integer> userIdList) {
        if (StringUtils.isBlank(enterpriseAccount)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        return new BaseResult<>(addressBookEmployeeManager.filterStopEmployee(enterpriseAccount, userIdList));
    }

    @Override
    public BaseResult<List<Integer>> getAdminIds(String enterpriseAccount) {
        if (StringUtils.isBlank(enterpriseAccount)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        return new BaseResult<>(addressBookEmployeeManager.getAdminIds(enterpriseAccount));
    }

    @Override
    public BaseResult<Boolean> isAdmin(String enterpriseAccount, Integer employeeId) {
        if (StringUtils.isBlank(enterpriseAccount) || employeeId == null) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        return new BaseResult<>(addressBookEmployeeManager.isAdmin(enterpriseAccount, employeeId));
    }

    @Override
    public BaseResult<List<Employee>> getEmployeesNoAdminId(String enterpriseAccount, List<Integer> employeeIds) {
        if (StringUtils.isBlank(enterpriseAccount)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        if (CollectionUtils.isEmpty(employeeIds)) {
            return new BaseResult<>(Lists.newArrayList());
        }
        return new BaseResult<>(addressBookEmployeeManager.getEmployeesNoAdminId(enterpriseAccount, employeeIds));
    }

    @Override
    public BaseResult<List<Integer>> getAllEmployeeIds(String enterpriseAccount, Integer employeeId, Integer status) {
        if (StringUtils.isBlank(enterpriseAccount) || employeeId == null || status == null) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        return new BaseResult<>(addressBookEmployeeManager.getAllEmployeeIds(enterpriseAccount, employeeId, status));
    }

    @Override
    public BaseResult<List<EmployeeInfo>> getEmployeeInfosNoAdminId(String enterpriseAccount, List<Integer> employeeIds) {
        if (StringUtils.isBlank(enterpriseAccount)) {
            return new BaseResult<>(AppCenterCodeEnum.PARAM_ILLEGAL_EXCEPTION);
        }
        if (CollectionUtils.isEmpty(employeeIds)) {
            return new BaseResult<>(Lists.newArrayList());
        }
        return new BaseResult<>(addressBookEmployeeManager.getEmployeeInfosNoAdminId(enterpriseAccount,employeeIds));
    }
}
