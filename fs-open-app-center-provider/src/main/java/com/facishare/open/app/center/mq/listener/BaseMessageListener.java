package com.facishare.open.app.center.mq.listener;

import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 公共的基础消息配置
 *
 * <AUTHOR>
 * @date 2020/9/8
 */
@Getter
@Setter
public class BaseMessageListener {

	private static final Logger LOGGER = LoggerFactory.getLogger(BaseMessageListener.class);

	/**
	 * 是否每次仅消费一条消息
	 */
	private Boolean isOnlyConsumeOneMessage = true;

	/**
	 * 设置监听器是否每次仅消费一条消息，e.g. 接收了多条消息（msgs.size() >= 1），但只使用第一条（get（0））
	 * 子类可通过重写setIsOnlyConsumeOneMessage = false; 进行一次可消费多条消息
	 */
	@PostConstruct
	protected void init() {
		LOGGER.info("{} set consume one message property every time [{}]", this.getClass().getSimpleName(), isOnlyConsumeOneMessage);
	}
}
