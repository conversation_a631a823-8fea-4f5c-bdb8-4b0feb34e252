package com.facishare.open.app.pay.jobs;

import com.facishare.ibss.utils.generic.DateTimeUtils;
import com.facishare.open.app.pay.api.enums.QuotaType;
import com.facishare.open.app.pay.cons.ExpireStatus;
import com.facishare.open.app.pay.cons.MqConstants;
import com.facishare.open.app.pay.entity.QuotaRecord;
import com.facishare.open.app.pay.mapper.QuotaRecordMapper;
import com.facishare.open.app.pay.utils.CommonThreadPoolUtils;
import com.facishare.open.app.pay.utils.ConfigCenter;
import com.facishare.open.app.pay.utils.QuotaUtils;
import com.facishare.open.app.pay.utils.TimeUtils;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.*;
import com.google.common.eventbus.EventBus;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

// TODO: 2018/7/5 个人试用逻辑清理
/**
 * Created by xialf on 4/8/16.
 *
 * <AUTHOR>
 */
@Component
public class QuotaExpireJob {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "jedisSupport")
    private MergeJedisCmd jedis;

    @Resource
    private QuotaRecordMapper quotaRecordMapper;

//    @Resource
//    private EmployeeTrialMapper employeeTrialMapper;

    @Resource
    private EventBus eventBus;

    private static final String RANDOM_ID = UUID.randomUUID().toString();

    //redis key
    private static final String REDIS_KEY_PREFIX = "open.app.pay";
    private static final String REDIS_JOB_KEY_PREFIX = REDIS_KEY_PREFIX + ".job";
    private static final int REDIS_LOAD_EXPIRED_LOCK_TTL = 300; //5分钟
    private static final String REDIS_LOAD_EXPIRED_LOCK = REDIS_JOB_KEY_PREFIX + ".lock.expired.load";
    private static final String REDIS_WRITING_EXPIRE_LOCK = REDIS_JOB_KEY_PREFIX + ".lock.writing.expired.quota";
    private static final String REDIS_HANDLE_EXPIRED_QUOTA_LOCK = REDIS_JOB_KEY_PREFIX + ".lock.handle.expired.quota";
    private static final String REDIS_HANDLE_EXPIRED_EMP_TRIAL_LOCK = REDIS_JOB_KEY_PREFIX + ".lock.handle.expired.trial";
    private static final String REDIS_EXPIRE_DATA_PREFIX = REDIS_JOB_KEY_PREFIX + ".data.expired.quota";
    private static final String REDIS_EXPIRE_EMP_TRIAL_DATA_PREFIX = REDIS_JOB_KEY_PREFIX + ".data.expired.empTrial";
    private static final String REDIS_WILL_EXPIRE_LOCK = REDIS_JOB_KEY_PREFIX + ".lock.willExpire";
    private static final String REDIS_EXPIRED_QUOTA_KEYS = REDIS_JOB_KEY_PREFIX + ".keys.expired.quota";
    private static final String REDIS_EXPIRED_EMP_TRIAL_KEYS = REDIS_JOB_KEY_PREFIX + ".keys.expired.empTrial";

    private static final String REDIS_WILL_EXPIRE_KEYS_SET = REDIS_JOB_KEY_PREFIX + ".keys.willExpire";
    private static final String REDIS_EXPIRED_WRITTEN_KEYS_SET = REDIS_JOB_KEY_PREFIX + ".keys.expired.written";

    private static final int REDIS_WILL_EXPIRE_TTL = 3 * 60 * 60; //3小时

    private static final String REDIS_DATE_KEY_FMT = "yyyyMMddHHmm";

    //到期预警天数
    private static final ImmutableList<Integer> PURCHASE_EXPIRE_PRE_WARN_DATE = ImmutableList.of(1, 3, 7);
    private static final ImmutableList<Integer> TRIAL_EXPIRE_PRE_WARN_DATE = ImmutableList.of(3, 7);

    //到期任务处理的时间控制
    private static final int REDIS_HANDLE_EXPIRE_INTERVAL_MIU = 10; //处理配额到期的一次任务的时间长度(分钟)
    private static final int REDIS_HANDLE_EXPIRE_LOCK_TTL = REDIS_HANDLE_EXPIRE_INTERVAL_MIU * 60 - 30; //9分半, 比任务时间10分钟少30秒, 允许服务器之间的时间差
    private static final int HANDLE_LOOP_MILLIS = (REDIS_HANDLE_EXPIRE_LOCK_TTL - 30) * 1000; //一次处理的时间, 预留30秒数据处理时间

    private final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    /**
     * 扫描将要过期的配额并发出服务号消息.
     */
    public void scanWillExpire() {
        logger.info("start to try to scan will expire data for quota or trial data, lock name={}",
                REDIS_WILL_EXPIRE_LOCK);
        if (!tryLock(REDIS_WILL_EXPIRE_LOCK, REDIS_WILL_EXPIRE_TTL)) {
            logger.info("job of scanning will expired quota has been started by other application");
            return;
        }
        logger.info("start to scan quota or trial data");
        sendMsgForQuotaWillExpire();

//        sendMsgForEmployeeTrialWillExpire();

        willExpireMarker.set();
    }

    private void sendMsgForQuotaWillExpire() {
        scanWillExpiredQuotaRecords()
                .forEach(quotaRecord -> CommonThreadPoolUtils.getExecutor().execute(() -> {
                    try {
                        //因为多记录存储的方式,有可能一条记录过期并不代表企业购买的应用过期(所以还需要查询更多的记录)
                        if (!isEndQuota(quotaRecord)) {
                            return;
                        }

                        QuotaEvent quotaEvent = new QuotaEvent(MqConstants.EA_WILL_EXPIRE, quotaRecord);
                        eventBus.post(quotaEvent);
                    } catch (Exception e) {
                        logger.warn("fail to handle quota record that will expire, quotaRecord[{}]",
                                quotaRecord, e);
                    }
                }));
    }

//    private void sendMsgForEmployeeTrialWillExpire() {
//
//        final List<EmployeeTrial> employeeTrials = Lists.newArrayList();
//        for (int preDate : TRIAL_EXPIRE_PRE_WARN_DATE) {
//            Calendar calendar = Calendar.getInstance();
//            calendar.add(Calendar.DATE, preDate);
//            employeeTrials.addAll(employeeTrialMapper.queryByOutOfDate(calendar.getTime()));
//        }
//        final Date now = new Date();
//        final List<EmployeeTrial> effectiveTrials = employeeTrials.stream()
//                .filter(trial -> !(TimeUtils.dateDiff(trial.getGmtBegin(), trial.getGmtEnd()) < 20 && TimeUtils.dateDiff(now, trial.getGmtEnd()) == 7))
//                .collect(Collectors.toList());
//
//        for (final EmployeeTrial trial : effectiveTrials) {
//            CommonThreadPoolUtils.getExecutor().execute(() -> {
//                try {
//                    if (!isEndTrial(trial)) {
//                        return;
//                    }
//
//                    EmployeeTrialEvent employeeTrialEvent = new EmployeeTrialEvent(MqConstants.USER_WILL_EXPIRE, trial);
//                    eventBus.post(employeeTrialEvent);
//                } catch (Exception e) {
//                    logger.error("fail to handle employee trial will expire, trial[{}]", trial, e);
//                }
//            });
//        }
//    }

    /**
     * 加载当天可能过期的配额记录到redis.
     */
    @PostConstruct
    public void loadExpiredToRedis() {
        logger.info("start to try to scan expired data, lock name={}", REDIS_LOAD_EXPIRED_LOCK);
        if (!tryLock(REDIS_LOAD_EXPIRED_LOCK, REDIS_LOAD_EXPIRED_LOCK_TTL)) {
            logger.info("task of loading expired has been started by other application");
            return;
        }

        logger.info("start to scan expired quota");

        Multimap<String, String> expiredQuota = HashMultimap.create();

        Calendar calendar = Calendar.getInstance();
        String nowTime = DateTimeUtils.formatDate(calendar.getTime(), DATE_FORMAT);
        String dateBeginTime = DateTimeUtils.formatDate(TimeUtils.getDayBeginTime(calendar), DATE_FORMAT);
        String dateEndTime = DateTimeUtils.formatDate(TimeUtils.getDayEndTime(calendar), DATE_FORMAT);
        final List<QuotaRecord> quotaRecords = quotaRecordMapper.queryByOutOfDate(nowTime, dateBeginTime, dateEndTime);
        quotaRecords.stream()
                .filter(QuotaUtils::isValidUnhandled)
                .forEach(qr -> expiredQuota.put(DateFormatUtils.format(qr.getGmtEnd(), REDIS_DATE_KEY_FMT), qr.getId().toString()));

//        Multimap<String, String> expiredEmployeeTrials = HashMultimap.create();
//        final List<EmployeeTrial> employeeTrials = employeeTrialMapper.queryByOutOfDate(new Date());
//        employeeTrials.stream()
//                .filter(EmployeeTrialUtils::isValidUnhandled)
//                .forEach(empT -> expiredEmployeeTrials.put(DateFormatUtils.format(empT.getGmtEnd(), REDIS_DATE_KEY_FMT), empT.getId().toString()));
        //判断是否已经写入redis 以及是否有其他程序正在装载进redis
        if (!expWrittenMarker.isSet() && !jedis.exists(REDIS_WRITING_EXPIRE_LOCK)) {
            if (tryLock(REDIS_WRITING_EXPIRE_LOCK, REDIS_LOAD_EXPIRED_LOCK_TTL)) {
                Set<String> quotaKeys = Sets.newHashSet();
                expiredQuota.asMap().forEach((date, quotaIds) -> {
                    if (!quotaIds.isEmpty()) {
                        quotaKeys.add(REDIS_EXPIRE_DATA_PREFIX + "." + date);
                        jedis.sadd(REDIS_EXPIRE_DATA_PREFIX + "." + date, quotaIds.toArray(new String[quotaIds.size()]));
                    }
                });
                if (!quotaKeys.isEmpty()) {
                    jedis.sadd(REDIS_EXPIRED_QUOTA_KEYS, quotaKeys.toArray(new String[quotaKeys.size()]));
                }

//                Set<String> expTrialKeys = Sets.newHashSet();
//                expiredEmployeeTrials.asMap().forEach((date, empTrialIds) -> {
//                    if (!empTrialIds.isEmpty()) {
//                        expTrialKeys.add(REDIS_EXPIRE_EMP_TRIAL_DATA_PREFIX + "." + date);
//                        jedis.sadd(REDIS_EXPIRE_EMP_TRIAL_DATA_PREFIX + "." + date, empTrialIds.toArray(new String[empTrialIds.size()]));
//                    }
//                });
//                if (!expTrialKeys.isEmpty()) {
//                    jedis.sadd(REDIS_EXPIRED_EMP_TRIAL_KEYS, expTrialKeys.toArray(new String[expTrialKeys.size()]));
//                }
                expWrittenMarker.set();
            } else {
                logger.info("other application is loading data to redis");
            }
        }
        unlock(REDIS_WRITING_EXPIRE_LOCK);
    }

    private final DailyJobMarker expWrittenMarker = new DailyJobMarker(REDIS_EXPIRED_WRITTEN_KEYS_SET);
    private final DailyJobMarker willExpireMarker = new DailyJobMarker(REDIS_WILL_EXPIRE_KEYS_SET);

    private class DailyJobMarker {
        private final String keyName;

        private DailyJobMarker(String keyName) {
            this.keyName = keyName;
        }

        public boolean isSet() {
            return jedis.sismember(keyName, DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        }

        public void set() {
            jedis.sadd(keyName, DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        }
    }

    /**
     * 处理已经加载进redis的候选过期配额数据(还需要验证).
     */
    public void handleExpiredFromRedisJob() {
        // 数据是否已经加载完毕
        if (!expWrittenMarker.isSet()) {
            logger.info("data has not been loaded to redis yet, exit this handle job");
            return;
        }

        // 加数据处理锁
        if (!tryLock(REDIS_HANDLE_EXPIRED_QUOTA_LOCK, REDIS_HANDLE_EXPIRE_LOCK_TTL)) {
            logger.info("job of handling expire data is locked, exit this job");
            return;
        }

        try {
            handleKeySet(REDIS_EXPIRED_QUOTA_KEYS, HANDLE_LOOP_MILLIS, id -> {
                QuotaRecord quotaRecord = quotaRecordMapper.findById(id);
                // 是否已经写入
                if (isEndQuota(quotaRecord)) {
                    try {
                        QuotaEvent quotaEvent = new QuotaEvent(MqConstants.EA_EXPIRED, quotaRecord);
                        eventBus.post(quotaEvent);

                        quotaRecordMapper.updateExpireStatus(quotaRecord.getId(), ExpireStatus.SENT.getCode());
                    } catch (Exception e) {
                        logger.warn("fail to handle expired quota[{}]", quotaRecord, e);
                        quotaRecordMapper.updateExpireStatus(quotaRecord.getId(), ExpireStatus.SENT_FAILED.getCode());
                    }
                } else {
                    quotaRecordMapper.updateExpireStatus(quotaRecord.getId(), ExpireStatus.NOT_END.getCode());
                }
            });
        } catch (Exception e) {
            logger.error("fail to handleExpiredFromRedisJob", e);
        }
    }

    /**
     * 处理redis中的key set.
     *
     * @param keySetName keySet的名称
     * @param timeout    最多可以处理的时间（毫秒）（时间到了之后需要退出任务）
     * @param consumer   消费keySet中的id
     * @throws InterruptedException 收到中断信后后，会抛出中断异常
     */
    private void handleKeySet(final String keySetName, final int timeout, Consumer<Long> consumer) throws InterruptedException {
        final long startTimeMillis = System.currentTimeMillis(); //开始时间

        // 获取所有的keys
        final Set<String> keys = jedis.smembers(keySetName);

        // 按照顺序处理每个Key
        List<String> sortedKeys = Lists.newArrayList(keys);
        sortedKeys.sort(Comparator.naturalOrder());
        for (final String key : sortedKeys) {
            //这里还需要增加key的时间判断(只处理已经到期的key), 否则会把redis中所有的key都处理掉
            // TODO: 3/20/17 这里可能存在一些问题，如果前面一天的key没有处理完，第二天就不能及时处理，而是要等到第二天相同的时间点了。 需要做一些特殊处理
            String nowKey = DateFormatUtils.format(getTimeForExpiration(), REDIS_DATE_KEY_FMT);
            final String minAfterKey = key.substring(key.lastIndexOf('.') + 1); //key到期时间的下一分钟，保证key所包含的所有id都已经过期
            while (nowKey.compareTo(minAfterKey) <= 0) {
                if (System.currentTimeMillis() - startTimeMillis >= timeout) {
                    return;
                }
                Thread.sleep(5000);
                nowKey = DateFormatUtils.format(getTimeForExpiration(), REDIS_DATE_KEY_FMT);
            }

            if (System.currentTimeMillis() - startTimeMillis >= timeout) {
                return;
            }

            String strId = jedis.spop(key);
            while (strId != null) {
                final long id;
                try {
                    id = Long.parseLong(strId);
                } catch (NumberFormatException e) {
                    logger.error("fail to handle with id={} which read from redis", strId, e);
                    return;
                }
                consumer.accept(id);

                if (System.currentTimeMillis() - startTimeMillis >= timeout) {
                    return;
                }
                if (Thread.interrupted()) {
                    Thread.currentThread().interrupt();
                    throw new InterruptedException();
                }
                strId = jedis.spop(key);
            }
            jedis.srem(keySetName, key);
        }
    }

//    /**
//     * 处理到期的个人试用.
//     */
//    public void handleExpiredEmployeeTrialFromRedisJob() {
//        logger.info("start the job of handleExpiredEmployeeTrialFromRedisJob");
//        // 数据是否已经加载完毕
//        if (!expWrittenMarker.isSet()) {
//            logger.info("data has not been loaded to redis yet, exit this handle job");
//            return;
//        }
//
//        // 加数据处理锁
//        if (!tryLock(REDIS_HANDLE_EXPIRED_EMP_TRIAL_LOCK, REDIS_HANDLE_EXPIRE_LOCK_TTL)) {
//            logger.info("job of handling expire data is locked, exit this job");
//            return;
//        }
//
//        try {
//            handleKeySet(REDIS_EXPIRED_EMP_TRIAL_KEYS, HANDLE_LOOP_MILLIS, id -> {
//                final EmployeeTrial employeeTrial = employeeTrialMapper.findById(id);
//                // 是否已经写入
//                if (isEndTrial(employeeTrial)) {
//                    try {
//                        EmployeeTrialEvent employeeTrialEvent = new EmployeeTrialEvent(MqConstants.USER_EXPIRED, employeeTrial);
//                        eventBus.post(employeeTrialEvent);
//                        employeeTrialMapper.updateExpireStatus(employeeTrial.getId(), ExpireStatus.SENT.getCode());
//                    } catch (Exception e) {
//                        logger.warn("fail to handle expired trial[{}]", employeeTrial, e);
//                        employeeTrialMapper.updateExpireStatus(employeeTrial.getId(), ExpireStatus.SENT_FAILED.getCode());
//                    }
//                } else {
//                    employeeTrialMapper.updateExpireStatus(employeeTrial.getId(), ExpireStatus.NOT_END.getCode());
//                }
//            });
//        } catch (Exception e) {
//            logger.error("fail to handleExpiredEmployeeTrialFromRedisJob", e);
//        }
//    }

//    private boolean isEndTrial(final EmployeeTrial thisTrial) {
//        final List<EmployeeTrial> employeeTrials =
//                employeeTrialMapper.queryTrialInfo(thisTrial.getFsEa(), thisTrial.getUserId(), thisTrial.getAppId());
//        for (final EmployeeTrial trial : employeeTrials) {
//            if (trial.getGmtBegin().getTime() - thisTrial.getGmtEnd().getTime() <= 1000
//                    && trial.getGmtEnd().after(thisTrial.getGmtEnd())) { //1s: 开始时间和结束时间都是闭区间, 那么区间[12, 13],[14, 20]是连续的
//                return false;
//            }
//        }
//        return true;
//    }


    private boolean isEndQuota(final QuotaRecord quotaRecord) {
        final List<QuotaRecord> multiRecords =
                quotaRecordMapper.queryQuotaRecords(quotaRecord.getFsEa(), quotaRecord.getAppId(), null);
        return QuotaUtils.isEndQuota(quotaRecord, multiRecords);
    }

    private List<QuotaRecord> scanWillExpiredQuotaRecords() {
        List<QuotaRecord> outOfDateRecord = Lists.newArrayList();
        for (int preDate : PURCHASE_EXPIRE_PRE_WARN_DATE) {
            outOfDateRecord.addAll(selectWillExpireQuotaRecords(QuotaType.PURCHASE, preDate));
        }
        //CRM还需要提前30天发出提醒
        selectWillExpireQuotaRecords(QuotaType.PURCHASE, 30).stream()
                .filter(qr -> qr.getAppId().equals(ConfigCenter.getCrmAppId()))
                .forEach(outOfDateRecord::add);

        //先把所有可能的试用到期选出来, 然后排除试用天数小于20且7天后到期的试用配额(因为只有20天以上的试用,才会提前7天发送告警消息)
        List<QuotaRecord> outOfDateTrialRecord = Lists.newArrayList();
        for (int preDate : TRIAL_EXPIRE_PRE_WARN_DATE) {
            outOfDateTrialRecord.addAll(selectWillExpireQuotaRecords(QuotaType.TRIAL, preDate));
        }

        final Date now = new Date();
        final List<QuotaRecord> effectiveOutOfDateTrial = outOfDateTrialRecord.stream()
                .filter(qr -> !(TimeUtils.dateDiff(qr.getGmtBegin(), qr.getGmtEnd()) < 20 && TimeUtils.dateDiff(now, qr.getGmtEnd()) == 7))
                .collect(Collectors.toList());

        outOfDateRecord.addAll(effectiveOutOfDateTrial);

        return outOfDateRecord;
    }

    private List<QuotaRecord> selectWillExpireQuotaRecords(QuotaType quotaType, int preDate) {
        Calendar calendar = Calendar.getInstance();
        Date now = calendar.getTime();
        calendar.add(Calendar.DATE, preDate);

        String nowTime = DateTimeUtils.formatDate(now, DATE_FORMAT);
        String dateBeginTime = DateTimeUtils.formatDate(TimeUtils.getDayBeginTime(calendar), DATE_FORMAT);
        String dateEndTime = DateTimeUtils.formatDate(TimeUtils.getDayEndTime(calendar), DATE_FORMAT);

        return quotaRecordMapper.selectWillExpired(quotaType.getCode(), nowTime, dateBeginTime, dateEndTime);
    }

    private boolean tryLock(final String lock, final int duration) {
        final String setResult = jedis.set(lock, RANDOM_ID, "nx", "ex", duration);
        logger.info("tryLock lock[{}],result[{}]", lock, setResult);
        return setResult != null && "OK".equalsIgnoreCase(setResult);
    }

    private boolean unlock(final String lock) {
        final String lockValue = jedis.get(lock);
        if (lockValue != null && RANDOM_ID.equals(lockValue)) {
            jedis.del(lock);
            return true;
        }
        return false;
    }

    /**
     * 获取用于到期处理的当前时间(主要是考虑到机器之间的时间不同步, 所以会晚一点时间进行到期处理, 否则推送时, 应用还没有到期).
     */
    private Date getTimeForExpiration() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, -10);
        return calendar.getTime();
    }
}
