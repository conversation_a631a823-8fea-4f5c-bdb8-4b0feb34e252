package com.facishare.open.app.center.mq.item;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 此消息仅用于主站更新应用缓存列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppViewChangeEvent {
    public static final String TOPIC = "AppViewChange";

    private String fsEa;
    private String appId;
    private String componentId;
    /**
     * 添加全公司可见
     */
    private Boolean addEaAuth;
    /**
     * 删除全公司可见
     */
    private Boolean delEaAuth;
    /**
     * 添加可见部门
     */
    private List<Integer> insertDepartmentList;
    /**
     * 删除可见部门
     */
    private List<Integer> deleteDepartmentList;
    /**
     * 添加可见人员
     */
    private List<Integer> insertUserList;
    /**
     * 删除可见人员
     */
    private List<Integer> deleteUserList;
    /**
     * @see AppViewChangeTypeEnum
     */
    private Integer changeType;

}
