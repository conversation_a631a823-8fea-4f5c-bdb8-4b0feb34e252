<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="eaVisible">
	<insert id="save" parameterType="java.util.List">
		INSERT INTO ea_visible(app_id,fs_ea,ea_name,type,gmt_create)
		VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.appId},#{item.fsEa},#{item.eaName},#{item.type},#{item.gmtCreate})
		</foreach>
	</insert>

	<delete id="deleteByAppId">
		delete from ea_visible where app_id
		= #{appId}
	</delete>

	<select id="queryByEaAndAppIds" resultType="com.facishare.open.app.center.api.model.EaVisibleDO">
		select app_id,type,fs_ea,ea_name,gmt_create
		from ea_visible
		where fs_ea= #{fsEa}
		and app_id in
		<foreach collection="appIds" item="appId" open="(" separator="," close=")">
			#{appId}
		</foreach>
	</select>

	<select id="queryLastVisibleByEaAndAppIds" resultType="com.facishare.open.app.center.api.model.EaVisibleDO">
		select  max(id) id,
		type,
		fs_ea,
		ea_name,
		app_id,
		gmt_create
		from ea_visible
		where fs_ea= #{fsEa}
		and app_id in
		<foreach collection="appIds" item="appId" open="(" separator="," close=")">
			#{appId}
		</foreach>
		GROUP BY
		app_id
	</select>

	<select id="queryByAppIds" resultType="com.facishare.open.app.center.api.model.EaVisibleDO">
		select
		app_id,type,fs_ea,ea_name,gmt_create
		from ea_visible
		where app_id = #{appId}
	</select>

	<delete id="deleteByEaAndAppId">
		delete from ea_visible where app_id=#{appId} and fs_ea=#{fsEa}
	</delete>
</mapper>