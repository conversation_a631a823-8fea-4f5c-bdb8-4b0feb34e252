<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="monthActivityEa">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO month_activity_ea(enterprise_id,ea) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.enterpriseId},#{item.ea})
        </foreach>
    </insert>

    <select id="queryPagerAllEaList" resultType="String">
        SELECT ea FROM month_activity_ea WHERE ea IS NOT NULL AND ea !=''
        GROUP BY ea
        LIMIT ${limit} OFFSET ${offset}
    </select>

    <delete id="deleteAll">
        TRUNCATE month_activity_ea
    </delete>

    <select id="countAll" resultType="Long">
        SELECT COUNT(*) FROM month_activity_ea
    </select>
</mapper>