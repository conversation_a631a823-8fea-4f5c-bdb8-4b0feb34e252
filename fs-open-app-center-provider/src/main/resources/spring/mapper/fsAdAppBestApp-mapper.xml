<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.open.app.ad.dao.FsAdAppBestAppDAO">
    <sql id="selectAllColumn">
        id,app_id,`index`,gmt_create,gmt_modified
    </sql>
    <select id="queryBestApps" resultType="com.facishare.open.app.ad.api.model.FsAdBestAppDO">
        SELECT
        <include refid="selectAllColumn"/>
        FROM
        fs_ad_best_app AS t
        ORDER  by `index`
    </select>

    <insert id="addBestApp" parameterType="com.facishare.open.app.ad.api.model.FsAdBestAppDO">
        insert into  fs_ad_best_app(<include refid="selectAllColumn"/>)
        values (#{id},#{appId},#{index},now(),now())
    </insert>

    <update id="deleteBestApp">
        DELETE FROM fs_ad_best_app  where id = #{id}
    </update>
    
    <select id="queryByIndex" resultType="com.facishare.open.app.ad.api.model.FsAdBestAppDO">
        SELECT
           <include refid="selectAllColumn"/>
        FROM
        fs_ad_best_app AS t
        WHERE t.`index` = #{index}
        limit 1
    </select>

    <update id="updateBestAppIndex">
        UPDATE fs_ad_best_app set `index`=#{index},gmt_modified=now() where id = #{id}
    </update>
</mapper>