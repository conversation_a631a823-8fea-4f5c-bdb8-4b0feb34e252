<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.open.app.pay.mapper.QuotaRecordMapper">
    <select id="queryQuotaRecords" resultType="com.facishare.open.app.pay.entity.QuotaRecord">
        SELECT * FROM quota_record WHERE status=1
        <if test="fsEa != null">
            AND fs_ea=#{fsEa}
        </if>
        <if test="appId != null">
            AND app_id=#{appId}
        </if>
        <if test="orderId != null">
            AND order_id=#{orderId}
        </if>
    </select>

    <update id="fakeDeleteByIds" parameterType="List">
        UPDATE quota_record
        SET status=0, gmt_modified=NOW()
        WHERE status != 0
        AND id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>

    <delete id="physicDeleteFakeDeletedQuotaRecord">
        DELETE FROM quota_record WHERE status=0 and fs_ea=#{fsEa}
        <if test="appId != null">
            AND app_id=#{appId}
        </if>
    </delete>

</mapper>
