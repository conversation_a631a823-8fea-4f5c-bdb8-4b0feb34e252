package com.facishare.open.app.center.dao;

import com.facishare.open.BaseTest;
import com.facishare.open.app.center.api.model.OpenAppComponentDO;
import com.facishare.open.app.center.api.model.OpenComponentUrlGrayDO;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:test-open-app-center-provider-dao.xml"})
public class OpenComponentUrlGrayDAOTest extends BaseTest {

    @Autowired
    private OpenComponentUrlGrayDAO dao;

    @Test
    public void testInsertOpenAppComponent() {
        String componentId = "xx_test_comp_001";
        OpenComponentUrlGrayDO entity = new OpenComponentUrlGrayDO();
        entity.setGrayId("113131");
        entity.setComponentId(componentId);
        entity.setGmtCreate(new Date());
        entity.setAppId("aaa");
        String grayJson = "";
        entity.setGrayJson(grayJson);

        int i = dao.insertOpenComponentUrlGray(entity);
        assertEquals(1, i);
        OpenComponentUrlGrayDO db = dao.loadOpenComponentUrlGrayById(componentId);
        assertEquals(grayJson, db.getGrayJson());
        assertEquals(entity, db);
    }

    @Test
    public void testUpdateOpenAppComponent() {
        String componentId = "xx_test_comp_001";
        OpenComponentUrlGrayDO entity = new OpenComponentUrlGrayDO();
        entity.setGrayId("113131");
        entity.setComponentId(componentId);
        entity.setGmtCreate(new Date());
        entity.setAppId("aaa");
        String grayJson = "1111";
        entity.setGrayJson(grayJson);

        int i = dao.insertOpenComponentUrlGray(entity);
        assertEquals(1, i);
        entity.setGrayJson("2222");
        int j = dao.updateOpenComponentUrlGray(entity);
        assertEquals(1, j);
        OpenComponentUrlGrayDO db = dao.loadOpenComponentUrlGrayById(componentId);
        assertEquals("2222", db.getGrayJson());
        assertEquals(entity, db);
    }
}
