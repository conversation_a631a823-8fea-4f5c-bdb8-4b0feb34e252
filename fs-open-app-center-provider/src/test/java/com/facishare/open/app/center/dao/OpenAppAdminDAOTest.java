//package com.facishare.open.app.center.dao;
//
//import com.facishare.open.app.center.api.model.OpenAppAdminDO;
//import com.facishare.open.app.center.api.model.enums.AppCenterEnum;
//import com.facishare.open.oauth.model.enums.AppTypeEnum;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.transaction.TransactionConfiguration;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.Date;
//import java.util.List;
//
///**
// * Created by xialf on 10/13/15.
// */
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = {"classpath:test-open-app-center-provider-dao.xml"})
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)
//@Transactional
//public class OpenAppAdminDAOTest {
//    public static final String APP_ID = "app_123";
//    public static final int APP_TYPE = AppCenterEnum.AppType.CUSTOM_APP.value();
//    public static final String FS_EA = "1989";
//    public static final int FS_USER_ID = 987;
//
//    public static final String FS_USER_ID_STRING = "E." + FS_EA + FS_USER_ID;
//
//    @Resource
//    private OpenAppAdminDAO openAppAdminDAO;
//
//    @Before
//    public void setUp() {
//        openAppAdminDAO.deleteAll();
//    }
//
//    @Test
//    public void testIsAppAdmin_Success_Zero() {
//        int appsCount = openAppAdminDAO.getOpenAppsCount(FS_USER_ID_STRING);
//        Assert.assertEquals(0, appsCount);
//
//        List<String> openAppIds = openAppAdminDAO.queryOpenAppIdList(FS_USER_ID_STRING);
//        Assert.assertEquals(0, openAppIds.size());
//
//        List<String> appAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(0, appAdminIds.size());
//    }
//
//    @Test
//    public void testIsAppAdmin_Success_One() {
//        OpenAppAdminDO openAppAdminDO = new OpenAppAdminDO();
//        openAppAdminDO.setAppId(APP_ID);
//        openAppAdminDO.setFsEa(FS_EA);
//        openAppAdminDO.setFsUserId(FS_USER_ID_STRING);
//        openAppAdminDO.setGmtCreate(new Date());
//        openAppAdminDO.setAppType(AppTypeEnum.TEST_APP.getValue());
//        openAppAdminDAO.save(openAppAdminDO);
//
//        int appsCount = openAppAdminDAO.getOpenAppsCount(FS_USER_ID_STRING);
//        Assert.assertEquals(1, appsCount);
//
//        List<String> openAppDOs = openAppAdminDAO.queryOpenAppIdList(FS_USER_ID_STRING);
//        Assert.assertEquals(1, openAppDOs.size());
//
//        List<String> appAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(1, appAdminIds.size());
//    }
//
//    @Test
//    public void testIsAppAdmin_Success_Two() {
//        OpenAppAdminDO openAppAdminDO = createAppAdminDO();
//
//        openAppAdminDAO.save(openAppAdminDO);
//        openAppAdminDO.setAppId(APP_ID + 1);
//        openAppAdminDAO.save(openAppAdminDO);
//
//        int appsCount = openAppAdminDAO.getOpenAppsCount(FS_USER_ID_STRING);
//        Assert.assertEquals(2, appsCount);
//
//        List<String> openAppDOs = openAppAdminDAO.queryOpenAppIdList(FS_USER_ID_STRING);
//        Assert.assertEquals(2, openAppDOs.size());
//
//        List<String> appAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(1, appAdminIds.size());
//
//        appAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID + 1);
//        Assert.assertEquals(1, appAdminIds.size());
//
//        //删除一个后, 就只有一个了,也同时测试了delete接口
//        openAppAdminDAO.delete(FS_EA, APP_ID, Collections.singletonList(FS_USER_ID_STRING));
//        appsCount = openAppAdminDAO.getOpenAppsCount(FS_USER_ID_STRING);
//        Assert.assertEquals(1, appsCount);
//
//
//    }
//
//    @Test
//    public void queryOpenAppIdList_NoApp_NoApp() {
//        List<String> openAppIds = openAppAdminDAO.queryOpenAppIdList(FS_USER_ID_STRING);
//        Assert.assertEquals(0, openAppIds.size());
//    }
//
//    @Test
//    public void queryOpenAppIdList_OneApp_OneApp() {
//        OpenAppAdminDO openAppAdminDO = createAppAdminDO();
//        openAppAdminDAO.save(openAppAdminDO);
//        List<String> openAppIds = openAppAdminDAO.queryOpenAppIdList(FS_USER_ID_STRING);
//        Assert.assertEquals(1, openAppIds.size());
//    }
//
//    @Test
//    public void queryOpenAppIdList_TwoApps_TwoApps() {
//        OpenAppAdminDO openAppAdminDO = createAppAdminDO();
//        openAppAdminDAO.save(openAppAdminDO);
//
//        openAppAdminDO.setAppId(APP_ID + 1);
//        openAppAdminDAO.save(openAppAdminDO);
//
//        List<String> openAppIds = openAppAdminDAO.queryOpenAppIdList(FS_USER_ID_STRING);
//        Assert.assertEquals(2, openAppIds.size());
//    }
//
//    @Test
//    public void queryAppAdminIds_NoAdmin_NoAdmin() {
//        List<String> openAppAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(0, openAppAdminIds.size());
//    }
//
//    @Test
//    public void queryAppAdminIds_OneAdmin_OneAdmin() {
//        OpenAppAdminDO openAppAdminDO = createAppAdminDO();
//        openAppAdminDAO.save(openAppAdminDO);
//        List<String> openAppAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(1, openAppAdminIds.size());
//
//        openAppAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID + "other");
//        Assert.assertEquals(0, openAppAdminIds.size());
//    }
//
//    @Test
//    public void queryAppAdminIds_TwoAdmins_TwoAdmins() {
//        openAppAdminDAO.insert(FS_EA, APP_ID, APP_TYPE, Arrays.asList(FS_USER_ID_STRING, FS_USER_ID_STRING + "OTHER"));
//        List<String> openAppAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(2, openAppAdminIds.size());
//
//        openAppAdminDAO.delete(FS_EA, APP_ID, Collections.singletonList(FS_USER_ID_STRING));
//        openAppAdminIds = openAppAdminDAO.queryAppAdminIds(FS_EA, APP_ID);
//        Assert.assertEquals(1, openAppAdminIds.size());
//    }
//
//    private OpenAppAdminDO createAppAdminDO() {
//        OpenAppAdminDO openAppAdminDO = new OpenAppAdminDO();
//        openAppAdminDO.setAppId(APP_ID);
//        openAppAdminDO.setAppType(AppCenterEnum.AppType.CUSTOM_APP.value());
//        openAppAdminDO.setFsEa(FS_EA);
//        openAppAdminDO.setFsUserId(FS_USER_ID_STRING);
//        openAppAdminDO.setGmtCreate(new Date());
//        return openAppAdminDO;
//    }
//}
