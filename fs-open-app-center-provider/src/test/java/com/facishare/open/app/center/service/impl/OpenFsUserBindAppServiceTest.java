package com.facishare.open.app.center.service.impl;

import com.facishare.open.app.center.api.service.OpenFsUserBindAppService;
import com.facishare.open.common.model.FsUserVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:test-open-app-center-api.xml"})
@Slf4j
public class OpenFsUserBindAppServiceTest {

    @Resource
    OpenFsUserBindAppService openFsUserBindAppService;

    @Test
    public void setBindStatusOff_Success() {
        FsUserVO fsUserVO = FsUserVO.newFakeUser("53424");

        openFsUserBindAppService.setBindStatusOff(fsUserVO, Lists.newArrayList("FSAID_9897b2", "FSAID_9897af"));
    }
}
