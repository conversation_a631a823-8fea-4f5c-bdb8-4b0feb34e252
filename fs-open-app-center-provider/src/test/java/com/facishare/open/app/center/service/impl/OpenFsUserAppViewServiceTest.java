package com.facishare.open.app.center.service.impl;

/**
 * <AUTHOR>
 * @date on 2017/5/2.
 */


import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.utils.ConfigCenter;
import com.facishare.open.common.model.FsUserVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-common-test.xml"})
public class OpenFsUserAppViewServiceTest {


    @Resource
    OpenFsUserAppViewService openFsUserAppViewService;

    @Test
    public void queryComponentsByFsUser(){

        FsUserVO fsUserVO = new FsUserVO();
        fsUserVO.setEnterpriseAccount("2");
        fsUserVO.setUserId(1123);

        BaseResult<List<UserCanViewListVO>> listBaseResult = openFsUserAppViewService
                .queryComponentsByFsUser(fsUserVO, AppAccessTypeEnum.WEB);

        for (UserCanViewListVO vo: listBaseResult.getResult()){
            if(vo.getAppId().equals(ConfigCenter.TRAIN_APP_ID)){
                System.out.println("==============="+vo.getComponentName());
                System.out.println("==============="+vo.getImageUrl());
            }
        }
    }



}
