//package com.facishare.open.operating.center.manager.impl;
//
//import com.facishare.open.common.model.FsUserVO;
//import com.facishare.open.common.storage.mysql.dao.Pager;
//import com.facishare.open.operating.center.api.model.OperationLogVO;
//import com.facishare.open.operating.center.manager.ServiceOperationLogManager;
//import com.facishare.open.operating.center.model.OperationLogDO;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * Created by liqiulin on 2016/8/5.
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath:spring/unit-test.xml")
//public class OperationLogManagerTest {
//    @Resource
//    ServiceOperationLogManager serviceOperationLogManager;
//
//    @Test
//    public void isFirstTime_Success() {
//
//        FsUserVO user = new FsUserVO("fsfte2a", 1276);
//        Pager<OperationLogVO> pager = new Pager<>();
//        pager.setCurrentPage(1);
//        pager.setPageSize(5);
//        List<OperationLogDO> result = serviceOperationLogManager.findServiceOperationLog(pager, user, "FSAID_bebc5ce");
//        System.out.println(result);
//
//    }
//}
