package com.facishare.open.custom.menu.utils;

import com.facishare.open.custom.menu.api.enums.CustomMenuStatusEnum;
import com.facishare.open.custom.menu.api.enums.CustomMenuTypeEnum;
import com.facishare.open.custom.menu.api.enums.PrefixEnum;
import com.facishare.open.custom.menu.api.enums.ResponseEnum;
import com.facishare.open.custom.menu.api.model.CustomMenuDO;
import com.facishare.open.custom.menu.api.model.Menu;
import com.facishare.open.custom.menu.api.model.vo.OpenMenuVO;
import com.facishare.open.custom.menu.utils.MenuUtils;
import com.facishare.open.msg.model.CustomMenuVO;
import com.facishare.open.msg.model.MenuVO;
import com.google.gson.Gson;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date on 2015/12/10.
 */
public class MenuUtilsTest extends TestCase {

    private OpenMenuVO openMenuVO = new OpenMenuVO();

    public String TEST_APP_ID = "TEST_APP_ID";
    public String TEST_APP_NAME = "TEST_APP_NAME";
    public String TEST_ACTION_PARAMS = "TEST_ACTION_PARAMS";
    public int TEST_TYPE = CustomMenuTypeEnum.TEXT.getCode();
    public int TEST_RESPONSE = ResponseEnum.OPEN_RESPONSE.getCode();

    public String TEST_NAME = "TEST_NAME";


    @Before
    public void setUp() {
        openMenuVO.setAppId(TEST_APP_ID);

        List<Menu> menus = new ArrayList<>();

        Menu mainA = new Menu();
        mainA.setActionParam(TEST_ACTION_PARAMS);
        mainA.setType(TEST_TYPE);
        mainA.setIsOpenResponse(TEST_RESPONSE);
        mainA.setName(TEST_NAME);

        List<Menu> subMenusA = new ArrayList<>();

        Menu subA = new Menu();
        subA.setActionParam(TEST_ACTION_PARAMS);
        subA.setType(TEST_TYPE);
        subA.setIsOpenResponse(TEST_RESPONSE);
        subA.setName(TEST_NAME);

        Menu subB = new Menu();
        subB.setActionParam(TEST_ACTION_PARAMS);
        subB.setType(TEST_TYPE);
        subB.setIsOpenResponse(TEST_RESPONSE);
        subB.setName(TEST_NAME);

        subMenusA.add(subA);
        subMenusA.add(subB);

        mainA.setChildren(subMenusA);
        Menu mainB = new Menu();
        mainB.setActionParam(TEST_ACTION_PARAMS);
        mainB.setType(TEST_TYPE);
        mainB.setIsOpenResponse(TEST_RESPONSE);
        mainB.setName(TEST_NAME);

        Menu mainC = new Menu();
        mainC.setActionParam(TEST_ACTION_PARAMS);
        mainC.setType(TEST_TYPE);
        mainC.setIsOpenResponse(TEST_RESPONSE);
        mainC.setName(TEST_NAME);

        menus.add(mainA);
        menus.add(mainB);
        menus.add(mainC);

        openMenuVO.setMenus(menus);
    }


    @Test
    public void test_openMenuVOToCustomMenuVO() {
        CustomMenuVO customMenuVO = MenuUtils.openMenuVOToCustomMenuVO(openMenuVO);

        Assert.assertTrue(openMenuVO.getMenus().size() == customMenuVO.getMenuList().size());

        MenuVO menuVO = customMenuVO.getMenuList().get(1);
        Assert.assertTrue((PrefixEnum.TEXT_PREFIX.getPrefix() + openMenuVO.getMenus().get(1).getActionParam()).equals(
                menuVO.getActionParam()));


        Assert.assertTrue(
                customMenuVO.getMenuList().get(0).getSubMenuList().size() == openMenuVO.getMenus().get(0).getChildren()
                        .size()
        );
    }


    @Test
    public void test_customMenuDOToOpenMenuVO() {
        CustomMenuDO customMenuDO = new CustomMenuDO();
        customMenuDO.setAppId(openMenuVO.getAppId());
        customMenuDO.setStatus(CustomMenuStatusEnum.NORMAL.getCode());
        customMenuDO.setContent(new Gson().toJson(openMenuVO));

        OpenMenuVO openMenuVO2 = MenuUtils.customMenuDOToOpenMenuVO(customMenuDO);

        Assert.assertTrue(openMenuVO.getMenus().size() == openMenuVO2.getMenus().size());
    }

    @Test
    public void test_customMenuDOToCustomMenuVO() {
        CustomMenuDO customMenuDO = new CustomMenuDO();
        customMenuDO.setAppId(openMenuVO.getAppId());
        customMenuDO.setStatus(CustomMenuStatusEnum.NORMAL.getCode());
        customMenuDO.setContent(new Gson().toJson(openMenuVO));

        CustomMenuVO customMenuVO = MenuUtils.customMenuDOToCustomMenuVO(customMenuDO);
        Assert.assertTrue(openMenuVO.getMenus().size() == customMenuVO.getMenuList().size());
    }
}
