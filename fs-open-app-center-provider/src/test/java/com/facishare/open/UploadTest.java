package com.facishare.open;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 测试用
 *
 * <AUTHOR>
 * @date 2015年9月2日
 */
public class UploadTest {

    public static void main(String[] args) {
//		for (File f : new File("D:/x/ios").listFiles()) {
//			System.out.println(f.getName());
//		}

        Map<String, String> ss = new HashMap<String, String>();
        ss.put("FSAID_9896c4", "function_attendance");//FSAID_9896c4	考勤签到
        ss.put("FSAID_9896ca", "function_baichuan_notification");//FSAID_9896ca	百川通知
        ss.put("FSAID_9896cc", "function_chayicha");//FSAID_9896cc	查一查
        ss.put("FSAID_9896cd", "function_chayicha");//FSAID_9896cd	查一查(仅纷享用户使用)
//		ss.put("", "function_department");//
        ss.put("FSAID_9896c9", "function_disk");//FSAID_9896c9	百川网盘
//		ss.put("", "function_infor");//
        ss.put("FSAID_9896c8", "function_information");//FSAID_9896c8	报数系统
        ss.put("FSAID_9896c6", "function_marketing");//FSAID_9896c6	微营销
        ss.put("FSAID_9896c1", "function_signin");//FSAID_9896c1	外勤签到
        ss.put("FSAID_9896c5", "function_statistics");//FSAID_9896c5	考勤统计
        ss.put("FSAID_9896c7", "function_workreport");//FSAID_9896c7	工作简报
        String appId = null;
        File base = new File("D:/x/android");
        File[] fs = base.listFiles();
        if (null != fs) {
            for (File f : fs) {
                appId = null;
                for (Entry<String, String> entry : ss.entrySet()) {
                    if (f.getName().indexOf(entry.getValue()) > -1) {
                        appId = entry.getKey();
                        break;
                    }
                }
                System.out.println(appId + " >>> " + f.getName());
            }
        }
    }
}
