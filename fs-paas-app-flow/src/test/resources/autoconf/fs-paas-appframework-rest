{"CRM_SFA": {"address": "**********:9120", "serviceName": "CRM_SFA", "socketTimeOut": 20000, "connectionTimeOut": 20000}, "PAAS-FLOWASSIGNEES": {"address": "************:8002/fs-paas-workflow", "serviceName": "审批流审批例外人服务", "socketTimeOut": 5000, "connectionTimeOut": 2000}, "PAAS-FLOW": {"address": "************:8008", "serviceName": "工作流引擎", "socketTimeOut": 10000, "connectionTimeOut": 2000}, "PAAS-ORG": {"address": "************:8005/fs-paas-org", "serviceName": "组织架构服务", "socketTimeOut": 10000, "connectionTimeOut": 2000}, "Log": {"address": "*************:8001/log", "serviceName": " 日志服务", "socketTimeOut": 5000, "connectionTimeOut": 2000}, "PAAS-PRIVILEGE": {"address": "************:8003/fs-paas-auth", "serviceName": "权限服务", "socketTimeOut": 10000, "connectionTimeOut": 2000}}