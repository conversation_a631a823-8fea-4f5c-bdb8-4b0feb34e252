package com.facishare.paas.appframework.flow;

import com.facishare.paas.appframework.core.model.User;

import java.util.Map;

/**
 * Created by yusb on 2017/8/28.
 */
public interface WorkFlowService {

    void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData);

    void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData, String eventId);

    void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData, boolean batch);

    void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData, String eventId, boolean batch);

    void initializeWorkFlow(String apiName, User user);
}
