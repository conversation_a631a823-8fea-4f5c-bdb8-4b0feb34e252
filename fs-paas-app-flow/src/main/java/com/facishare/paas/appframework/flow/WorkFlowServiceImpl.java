package com.facishare.paas.appframework.flow;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.dto.InitializeWorkFlow;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

@Slf4j
@Service("workFlowService")
public class WorkFlowServiceImpl implements WorkFlowService {

    @Autowired
    private WorkflowProducer workflowProducer;
    @Autowired
    private WorkFlowRestProxy proxy;

    @Override
    public void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData) {
        startWorkFlow(dataId, apiName, operationType, user, triggerData, RequestUtil.getEventId(), RequestUtil.isBatch());
    }

    /**
     * 工作流启动通过MQ进行触发
     * http://git.firstshare.cn/bpm/fs-workflow/wikis/docs/startWorkflow
     *
     * @param dataId
     * @param apiName
     * @param operationType 触发类型,新建/作废
     * @param user
     * @param triggerData   字段变更后的值
     */
    @Override
    public void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData, String eventId) {
        startWorkFlow(dataId, apiName, operationType, user, triggerData, eventId, RequestUtil.isBatch());
    }

    @Override
    public void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData, boolean batch) {
        String eventId = null;
        if (RequestContextManager.getContext() != null) {
            eventId = RequestContextManager.getContext().getEventId();
        }

        startWorkFlow(dataId, apiName, operationType, user, triggerData, eventId, batch);
    }

    @Override
    public void startWorkFlow(String dataId, String apiName, String operationType, User user, Map<String, Object> triggerData, String eventId, boolean batch) {
        if (StringUtils.isEmpty(eventId)) {
            eventId = UUID.randomUUID().toString();
        }
        WorkflowProducer.WorkFlowMessage message = new WorkflowProducer.WorkFlowMessage();
        message.setEntityId(apiName);
        message.setObjectId(dataId);
        message.setTriggerType(operationType);
        message.setTriggerData(triggerData);
        message.setEventId(eventId);
        message.setBatch(batch);

        WorkflowProducer.WorkFlowMessageContext context = new WorkflowProducer.WorkFlowMessageContext();
        context.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        context.setTenantId(user.getTenantId());
        context.setUserId(getUserId(user));
        if (user.isOutUser()) {
            context.setOutAppId(RequestUtil.getAppId());
            context.setOutTenantId(user.getOutTenantId());
            context.setOutUserId(user.getOutUserId());
            context.setUpstreamOwnerId(user.getUpstreamOwnerId());
        }
        message.setContext(context);
        workflowProducer.startWorkFlow(message);
    }

    @Override
    public void initializeWorkFlow(String apiName, User user) {
        InitializeWorkFlow.Arg arg = new InitializeWorkFlow.Arg();
        arg.setEntityId(apiName);
        proxy.initializeWorkFlow(arg, buildHeaders(user));
    }

    private Map<String, String> buildHeaders(User user) {
        Map<String, String> ret = RestUtils.buildHeaders(user);
        ret.put("x-tenant-id", String.valueOf(user.getTenantId()));
        ret.put("x-user-id", user.getUserIdWithFlowGray());
        return ret;
    }

    private String getUserId(User user) {
        return user.getUserIdWithFlowGray();
    }

}
