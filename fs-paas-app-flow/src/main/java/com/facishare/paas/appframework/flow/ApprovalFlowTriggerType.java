package com.facishare.paas.appframework.flow;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;

/**
 * 审批流触发类型
 * <p>
 * Created by liyiguang on 2017/8/14.
 */
public enum ApprovalFlowTriggerType {
    CREATE("Create", "1", I18NKey.action_add, "Add"),//"新建"
    UPDATE("Update", "2", I18NKey.action_edit, "Edit"),//"编辑"
    INVALID("Invalid", "3", I18NKey.action_invalid, "Invalid"),//"作废"
    DELETE("Delete", "4", I18NKey.action_delete),
    CHANGE_OWNER("ChangeOwner", "changeOwner", I18NKey.action_change_owner, "ChangeOwner"),
    CHANGE_PARTNER("ChangePartner", "changePartner", I18NKey.action_change_partner, "ChangePartner"),//更换合作伙伴
    DELETE_PARTNER("DeletePartner", "deletePartner", I18NKey.action_delete_partner, "DeletePartner"),//删除合作伙伴
    CHANGE_PARTNER_OWNER("ChangePartnerOwner_button_default", "changePartnerOwner", I18NKey.action_change_partner_owner, "ChangePartnerOwner"),//更换外部负责人
    CHOOSE("Choose", "choose", I18NKey.action_choose, "Choose"),
    RETURN("Return", "return", I18NKey.action_return, "Return"),
    CLOSE("Unavailability", "unavailability", I18NKey.action_close, "Close"),
    CUSTOM_BUTTON("CustomButton", "__c", I18NKey.custom_action),
    STAGE_CHANGE("StageChange", "stageChange", I18NKey.stage_change),
    TRANSFER("Transfer", "transfer", I18NKey.action_transfer, "Transfer"),
    EXTEND_EXPIRETIME("ExtendExpireTime", "extendExpireTime", I18NKey.action_extend_expireTime),
    CLOSE_TPM_ACTIVITY("CloseTPMActivity_button_default", "CloseTPMActivity_button_default", "paas.udobj.action.close.tpm.activity", "CloseTPMActivity"),

    TEST_CAR_SELL("TestCarSell", "testCarSell", "TestCarSell", "TestCarSell"),
    CLOSE_ACTIVITY_AGREEMENT("CloseActivityAgreement_button_default", "CloseActivityAgreement_button_default", I18NKey.CLOSE_ACTIVITY_AGREEMENT, "CloseActivityAgreement"),
    ;// 申请延期

    private String id;
    private String triggerTypeCode;
    private String triggerTypeName;
    private String actionCode;

    ApprovalFlowTriggerType(String id, String triggerTypeCode, String triggerTypeName) {
        this.id = id;
        this.triggerTypeCode = triggerTypeCode;
        this.triggerTypeName = triggerTypeName;
    }

    ApprovalFlowTriggerType(String id, String triggerTypeCode, String triggerTypeName, String actionCode) {
        this.id = id;
        this.triggerTypeCode = triggerTypeCode;
        this.triggerTypeName = triggerTypeName;
        this.actionCode = actionCode;
    }

    public String getTriggerTypeCode() {
        return triggerTypeCode;
    }

    public String getTriggerTypeName() {
        return I18N.text(triggerTypeName);
    }

    public String getActionCode() {
        return actionCode;
    }

    private final static Map<String, ApprovalFlowTriggerType> typeMaps = Maps.newHashMap();
    private final static Map<String, ApprovalFlowTriggerType> actionCode2FlowTriggerMaps = Maps.newHashMap();

    static {
        Arrays.stream(values()).forEach(type -> {
            typeMaps.put(type.getTriggerTypeCode(), type);
            typeMaps.put(type.getId(), type);
            if (!Strings.isNullOrEmpty(type.actionCode)) {
                actionCode2FlowTriggerMaps.put(type.actionCode, type);
            }
        });
    }

    public static ApprovalFlowTriggerType getType(String typeCodeOrId) {
        if (typeCodeOrId.endsWith(CUSTOM_BUTTON.getTriggerTypeCode())) {
            return CUSTOM_BUTTON;
        }
        return typeMaps.get(typeCodeOrId);
    }

    public static ApprovalFlowTriggerType getByActionCode(String actionCode) {
        if (StringUtils.endsWith(actionCode, CUSTOM_BUTTON.getTriggerTypeCode())) {
            return CUSTOM_BUTTON;
        }
        return actionCode2FlowTriggerMaps.get(actionCode);

    }

    public String getId() {
        return id;
    }

    public boolean supportWorkFlow() {
        return this != CUSTOM_BUTTON && this != STAGE_CHANGE;
    }
}
