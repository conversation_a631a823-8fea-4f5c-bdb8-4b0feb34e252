package com.facishare.webpage.customer.core.util;

import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.common.release.GrayReleaseBiz;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * switch 开关优先级最高, switch = allow 时才会继续下面的开关逻辑
 * switch = deny 或 switch 配置不存在 就不存在灰度逻辑 即使后年有灰度的配置
 * switch 其实是灰度的配置是否生效的开关
 * <p>
 * Created by liyiguang on 16/1/18.
 */
public class WebPageGraySwitch {

    public static final String ON_TIME_OPTIMIZE_EI ="on_time_optimize_ei" ;
    public static final String PRINT_MULTI_LANG_LOG = "print_multi_lang_log";

    public interface GrayKeys {
        String PARALLEL_UTILS_GRAY_EI = "parallelUtilsGrayEI";
        String MENU_GROUP_GET_REAL_NAME_GRAY_EI = "menuGroupGetRealNameGrayEI";
    }

    private static GrayReleaseBiz biz = GrayRelease.getInstance("webPage");
    private static FsGrayReleaseBiz gray = FsGrayRelease.getInstance("webPage");

    private static final String filterAll = "filterAll";

    private static final String dropList = "dropList";

    public static final String WebMainChannel = "WebMainChannel";

    public static final String NewWebMainChannel = "NewWebMainChannel";

    public static final String hasFilter = "HasFilter";

    public static final String CREATE_ENTERPRISE_SWITCH = "CreateEnterpriseSwitch";

    public static final String USE_V3_DROP_LIST = "USE_V3_DROP_LIST";

    public static final String MenuWidgetEiGraySuffix = "-MenuWidgetEiGray";
    public static final String useNewAuthInterfaceGray = "useNewAuthInterfaceGray";
    public static final String mobileAbstractLayoutGrayEas = "mobileAbstractLayoutGrayEas";
    public static final String multiCrossPortalGrayEis = "multiCrossPortalGrayEis";
    public static final String checkGoNewCrmSwitch = "checkGoNewCrmSwitch";
    public static final String hiddenHelpCenter = "hiddenHelpCenter";
    public static final String appTemplateNPathGray = "appTemplateNPathGray";
    public static final String queryMainCusMenuWithScope = "queryMainCusMenuWithScope";
    public static final String synLinkAppGrayEaByShuxian = "synLinkAppGrayEaByShuxian";
    public static final String defaultAcceptShowLineGrayEis = "defaultAcceptShowLineGrayEis";
    public static final String webpageAndUserextGrayEi = "webpageAndUserextGrayEi";
    public static final String notQueryUserFilterGrayEis = "notQueryUserFilterGrayEis";

    public static final String useDealHomeFlagGrayEi = "useDealHomeFlagGrayEi";

    public static final String ListLayoutToRecordGrayEi = "listLayoutToRecordGrayEi";
    public static final String getTransValueByOldKeyGrayEi = "getTransValueByOldKeyGrayEi";

    public static final String getTabsTransValueByOldKeyGrayEi = "getTabsTransValueByOldKeyGrayEi";

    /**
     * 修复菜单配置了非对象的functionCode 没有实体对象functionCode 导致菜单不能正常下发的问题
     */
    public static final String ClientMenuFunctionCodeBugfixGrayEi = "clientmenufunctioncodebugfixgrayei";

    public static final String LOAD_OBJECT_DESCRIBE_WITH_CACHE_GRAY_EI = "loadObjectDescribeWithCacheGrayEi";
    public static final String GET_MENUS_BY_IDS_GRAY_EI = "getMenusByIdsGrayEi";
    public static final String ADD_TENANT_ID_FOR_CUSTOMER_LINK_APP_GRAY_EI = "addTenantIdForCustomerLinkAppGrayEi";
    public static final String CHANGE_ORDER_CAN_NOT_ALLOCATE_DETAIL_LAYOUT = "change_order_can_not_allocate_detail_layout";
    public static final String MD5_VERSION_INCLUDE_LAYOUT_LIST = "md5_version_include_layout_list";
    public static final String SIET_DIFF_PARALLEL_DIFF = "siet_diff_parallel_diff";

    @Deprecated
    public static boolean isAllowByBusiness(String business, int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(business + "-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowForEi(String business, String ei) {
        if (StringUtils.isBlank(ei)) {
            return false;
        }
        return gray.isAllow(business, ei);
    }

    public static boolean isAllowForEi(String business, Integer ei) {
        if (ei == null || ei < 0) {
            return false;
        }
        return gray.isAllow(business, ei.toString());
    }

    public static boolean isAllowForEa(String business, String ea) {
        if (StringUtils.isBlank(ea)) {
            return false;
        }
        return gray.isAllow(business, ea);
    }

    public static boolean isAllow(String business, Integer ei, Integer employeeId) {
        if (ei == null || ei < 0) {
            return false;
        }
        if (employeeId == null || employeeId <= 0) {
            boolean ret = gray.isAllow(business, ei.toString());
            return ret;
        }
        String fullEmployeeId = ei + "." + employeeId;
        return gray.isAllow(business, fullEmployeeId);
    }

    public static boolean isAllowForAddTenantIdForCustomerLinkAppGrayEi(Integer ei) {
        return isAllowForEi(ADD_TENANT_ID_FOR_CUSTOMER_LINK_APP_GRAY_EI, ei);
    }

    public static boolean isMD5VersionIncludeLayoutList(Integer ei) {
        return isAllowForEi(MD5_VERSION_INCLUDE_LAYOUT_LIST, ei);
    }

    public static boolean isAllowForLoadObjectDescribeWithCacheGrayEi(Integer ei) {
        return isAllowForEi(LOAD_OBJECT_DESCRIBE_WITH_CACHE_GRAY_EI, ei);
    }

    public static boolean isChangeOrderCanNotAllocateDetailLayout(Integer ei) {
        return isAllowForEi(CHANGE_ORDER_CAN_NOT_ALLOCATE_DETAIL_LAYOUT, ei);
    }

    public static boolean isAllowForMenuWidget(String business, Integer ei, Integer employeeId) {
        return isAllow(business + MenuWidgetEiGraySuffix, ei, employeeId);
    }

    public static boolean isAllowUseNewAuthInterfaceGray(Integer ei) {
        return isAllowForEi(useNewAuthInterfaceGray, ei);
    }

    public static boolean isAllowSynLinkAppGrayEaByShuxian(String ea) {
        return isAllowForEa(synLinkAppGrayEaByShuxian, ea);
    }

    public static boolean isAllowDefaultAcceptShowLineGrayEis(Integer ei) {
        return isAllowForEi(defaultAcceptShowLineGrayEis, ei);
    }

    public static boolean isAllowWebpageAndUserextGrayEi(Integer ei) {
        return isAllowForEi(webpageAndUserextGrayEi, ei);
    }

    public static boolean isAllownotQueryUserFilterGrayEis(Integer ei) {
        return isAllowForEi(notQueryUserFilterGrayEis, ei);
    }

    public static boolean isGetTabsTransValueByOldKeyGrayEi(String ei) {
        return isAllowForEi(getTabsTransValueByOldKeyGrayEi, ei) && isAllowForEi(getTransValueByOldKeyGrayEi, ei);
    }

    public static boolean isAllowMobileAbstractLayoutGrayEasGray(Integer ei) {
        return isAllowForEi(mobileAbstractLayoutGrayEas, ei);
    }

    public static boolean isAllowMultiCrossPortalGrayEisGray(Integer ei) {
        return isAllowForEi(multiCrossPortalGrayEis, ei);
    }


    public static boolean isListLayoutToRecordGrayEi(Integer ei) {
        return isAllowForEi(ListLayoutToRecordGrayEi, ei);
    }

    public static boolean isAllowClientMenuFunctionCodeBugfixGrayEi(Integer ei) {
        return isAllowForEi(ClientMenuFunctionCodeBugfixGrayEi, ei);
    }

    public static boolean isAllowUrlByBusiness(String apiName, int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(apiName + "-url-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowComponentByBusiness(String componentId, int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(componentId + "-component-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowNavigateMenuByBusiness(String menuId, int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(menuId + "-navigate-menu-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowFilterAllByBusiness(int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(filterAll + "-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowComponentByType(int tenantId, String appId, String dropListId, String type) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(appId + "-" + type + "-" + dropListId + "-" + dropList + "-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowWebMainChannelMenuByBusiness(String appId, int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(appId + "-mainChannel-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    public static boolean isAllowCloseMenuByBusiness(String appId, int tenantId) {
        if (!biz.isAllow("switch", "")) {
            return true;
        } else if (biz.isAllow(appId + "-close-menu-enterprise", tenantId)) {
            return true;
        }
        return false;
    }

    /**
     * 是否灰度了通过EnterpriseConfig开启新版CRM
     *
     * @param enterpriseId 租户id
     * @return true-灰度 false-未灰度
     */
    public static boolean isGrayOpenNewCrmByEnterpriseConfig(Integer enterpriseId) {
        if (Objects.isNull(enterpriseId)) {
            return false;
        }
        return gray.isAllow("openNewCrmByEnterpriseConfig", String.valueOf(enterpriseId));
    }

}
