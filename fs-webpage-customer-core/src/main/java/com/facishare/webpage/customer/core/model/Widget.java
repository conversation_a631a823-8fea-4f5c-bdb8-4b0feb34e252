package com.facishare.webpage.customer.core.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 19/12/10.
 */
@Data
public class Widget {

    @JSONField(ordinal = 1)
    private String id;

    @JSONField(ordinal = 2)
    private String name;

    @JSONField(ordinal = 3)
    private String nameI18nKey;

    @JSONField(ordinal = 4)
    private int widgetType;

    @JSONField(ordinal = 5)
    private String cardId;

    @JSONField(ordinal = 6)
    private int height;

    @JSONField(ordinal = 7)
    private TenantPrivilege tenantPrivilege;    // 组件定义配置文件处的权限

    @JSONField(ordinal = 8)
    private String icon;

    @JSONField(ordinal = 9)
    private int limit = 1;

    @JSONField(ordinal = 10)
    private Map<String, String> widgetScope = Maps.newHashMap();

    @JSONField(ordinal = 11)
    private Boolean unDeletable;

    @JSONField(ordinal = 12)
    private String apiName; // 是为了支持对象的组件, 一般还是id

    @JSONField(ordinal = 13)
    private Map<String, List<FieldSection>> fieldSectionMap;

    @JSONField(ordinal = 14)
    private JSONObject extProp;

    /**
     * 用于控制灰度企业在页面布局中组件的可选个数
     * 业务侧自行判断是否灰度企业 来选择limit或者grayLimit字段
     */
    @JSONField(ordinal = 15)
    private int grayLimit = 1;
}
