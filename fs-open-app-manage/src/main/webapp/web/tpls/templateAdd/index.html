<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>纷享开放平台</title>
	
	<link rel="shortcut icon" href="/web/favicon.ico">

    <!-- Bootstrap Core CSS -->
    <link href="/web/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/web/assets/style/sb-admin.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="/web/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
	
	<!-- Common CSS -->
    <link href="/web/assets/style/common.css" rel="stylesheet">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
            <!--#include virtual="/web/modules/header/header.html"-->    
        </nav>

        <div id="page-wrapper">

            <div class="container-fluid">

                <!-- Page Heading -->
                <div class="row">
                    <div class="col-lg-12">
                        <ol class="breadcrumb">
                            <li class="back">
                                <a href="/web/tpls/appList/index.html">应用列表列表</a>
                            </li>
							<li>
                                <a id="breadcrumb" href="#">应用资料查看</a>
                            </li>
                            <li class="active">
                                <span>申请模板消息</span>
                            </li>
                        </ol>
                    </div>
					<div class="col-lg-6">
						<form class="form-horizontal template-add-form" id="form">
							
							<div class="form-group">
								<label class="col-sm-2 control-required-label">标题</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" name="title" placeholder="2-10个字">
								</div>
							</div>
							
							<div class="form-group">
								<label class="col-sm-2 control-required-label">内容</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" name="firstContent" value="{{first.DATA}}" readonly="readonly">
								</div>
							</div>
							
							<div class="js-keyword form-group">
								<div class="col-sm-offset-2 col-sm-10 form-inline">
									<input type="text" class="js-input form-control col-md-6" placeholder="填写关键字">
									<input type="text" class="form-control col-md-6 pull-right" value="{{keyword1.DATA}}" readonly="readonly">
								</div>
							</div>
							
							<div class="form-group">
								<div class="col-sm-offset-2 col-sm-10">
									<a href="javascript:;" class="js-add">增加关键字</a>
								</div>
							</div>
							
							<div class="form-group">
								<div class="col-sm-offset-2 col-sm-10">
									<input type="text" class="form-control" name="remarkValue" value="{{remark.DATA}}" readonly="readonly">
								</div>
							</div>
							
							<div class="form-group">
								<div class="col-sm-offset-2 col-sm-10">
									<p>注：灰色部门是模板参数，参数使用详见<a href="">接口文档</a></p>
								</div>
							</div>
							
							<div class="form-group">
								<div class="col-sm-offset-2 col-sm-10">
									<button type="submit" class="btn btn-info" data-loading-text="正在提交...">提交</button>
								</div>
							</div>
							
						</form>
					</div>
                </div>
                <!-- /.row -->

            </div>
            <!-- /.container-fluid -->

        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->

    <script src="/web/assets/libs/seajs/2.2.0/sea.js"></script>
	<script src="/web/assets/js/common.js"></script>
	<script>
		seajs.use('/web/tpls/templateAdd/main');
	</script>

</body>

</html>
