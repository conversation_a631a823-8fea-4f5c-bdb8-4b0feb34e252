define(function(require, exports, module) {
	var Side = require('side'),
		side = new Side({"nav":"developer", "subNav" :"developer-list"});
		side.init();
	
	require('bootstrap');	
	require('ajaxSubmit');
	require('validator');
	var util = require('util'),
		dialog = require('dialog');
	
	var Page = function(){
		this.devId = util.getTplQueryParams('id') ? util.getTplQueryParams('id') : -1;
	}
	Page.prototype = {
		init : function(){
			this.load();
		},
		load : function(){
			var me = this;
			$.ajax({ 
				url : '/open/manage/rest/openDev/loadOpenDevByDevId',
				timeout : 10000, //超时时间设置，单位毫秒
				data : {
					devId : me.devId
				},
				success : function(response){
					if(response.errCode == 0){
						var data = response.data;				
						me.renderHtml(data);
					}else{
						dialog.open({
							container : {
								content : '请求失败'
							},
							autoClose : 2000,
							type : 2,
							overlay : false
						});
					}				
				},
				error: function () {
                    dialog.open({
						container : {
							content : '网络链接失败，请稍后重试！'
						},
						autoClose : 2000,
						type : 3,
						overlay : false
					});
                }
			});
		},
		renderHtml : function(data){
			var me = this;
			$('tbody').html(_.template($('#table_tpl').html())(data));
			me.renderLogo();
		},
		renderLogo : function(){
			var me = this;
			setTimeout(function() {
				var images = $('.business-license-img');
				_.each(images, function(img) {
					var $img = $(img),
						imgDefaultSrc = '/web/assets/images/license-default.jpg',
						imgSrc = $img.attr('data-src');
					img.onerror = function() {
						img.src = imgDefaultSrc;
					};
					img.src = imgSrc;
				});
			}, 0);              
		}
	}
	var page = new Page();
	page.init();
});