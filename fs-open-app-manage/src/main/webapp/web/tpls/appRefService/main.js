define(function (require, exports, module) {
    var Side = require('side');
    var side = new Side({ "nav": "app", "subNav": "app-list" });
    side.init();

    require('bootstrap');
    require('ajaxSubmit');
    require('validator');
    var util = require('util');
    var dialog = require('dialog');

    var Page = function () {
        this.appId = util.getTplQueryParams('id') ? util.getTplQueryParams('id') : -1;
    }
    Page.prototype = {
        init: function () {
            this.initBreadCrumb();
            this.load();
        },
        initBreadCrumb: function () {
            $('#breadcrumb').attr('href', '/web/tpls/appDetail/index.html?id=' + this.appId);
        },
        load: function () {
            var me = this;
            $.ajax({
                url: '/open/manage/rest/openApp/loadAppByAppId',
                timeout: 10000, //超时时间设置，单位毫秒
                data: {
                    appId: me.appId
                },
                success: function (response) {
                    if (response.errCode == 0) {
                        var data = response.data;
                        me.appCreater = data.appCreater;
                        me.hasRefService = data.hasRefService;
                        me.renderHtml(data);
                        if (data.hasRefService == 2) {
                            me.initServiceList();
                            me.bindEvent();
                        }
                    } else {
                        dialog.open({
                            container: {
                                content: '请求失败'
                            },
                            autoClose: 2000,
                            type: 2,
                            overlay: false
                        });
                    }
                },
                error: function () {
                    dialog.open({
                        container: {
                            content: '网络链接失败，请稍后重试！'
                        },
                        autoClose: 2000,
                        type: 3,
                        overlay: false
                    });
                }
            });
        },
        initServiceList: function () {
            var me = this;
            $.ajax({
                url: '/open/manage/rest/openService/queryServices',
                timeout: 10000, //超时时间设置，单位毫秒
                success: function (response) {
                    if (response.errCode == 0) {
                        var data = response.data;
                        me.renderServiceList(data.services)
                    } else {
                        dialog.open({
                            container: {
                                content: '请求失败'
                            },
                            autoClose: 2000,
                            type: 2,
                            overlay: false
                        });
                    }
                },
                error: function () {
                    dialog.open({
                        container: {
                            content: '网络链接失败，请稍后重试！'
                        },
                        autoClose: 2000,
                        type: 3,
                        overlay: false
                    });
                }
            });
        },
        renderServiceList: function (list) {
            var serviceList = '';
            list.forEach(function (service) {
                serviceList += '<option value="' + service.appId + '">' + service.appName + '(AppID: ' + service.appId + ')' + '</option>'
            });
            $('#service_list').append(serviceList);
        },
        renderHtml: function (data) {
            var me = this;
            $('form').html(_.template($('#form_tpl').html())(data));
        },
        bindEvent: function () {
            var me = this;

            // 取消
            $('.js-cancel').on('click', function (evt) {
                evt.stopPropagation();
                location.href = location.href;
            });

            // 更新，保存
            $('#form').bootstrapValidator({
                fields: {
                    serviceId: {
                        message: '请先选择关联服务号',
                        validators: {
                            notEmpty: {
                                message: '请先选择关联服务号',
                            },
                        }
                    },
                }
            }).on('success.form.bv', function (e) {
                e.preventDefault();
                $('button[type="submit"]').button('loading');

                var $form = $(e.target);
                var data = {
                    appId: me.appId,
                    serviceId: $form.find('#service_list').val(),
                }
                var options = {
                    type: 'POST',
                    url: '/open/manage/rest/openApp/addServiceBind',
                    data: JSON.stringify(data),
                    contentType: 'application/json;charset=utf-8',
                    success: function (response) {
                        $('button[type="submit"]').button('reset');
                        if (response.errCode == 0) {
                            dialog.open({
                                container: {
                                    content: '修改成功'
                                },
                                autoClose: 500,
                                type: 1,
                                overlay: false,
                                callback: function () {
                                    util.redirect('/web/tpls/appDetail/index.html?id=' + me.appId);
                                },
                            });
                        } else {
                            dialog.open({
                                container: {
                                    content: '修改失败'
                                },
                                autoClose: 2000,
                                type: 2,
                                overlay: false
                            });
                        }
                    },
                    error: function () {
                        $('button[type="submit"]').button('reset');
                        dialog.open({
                            container: {
                                content: '网络链接失败，请稍后重试！'
                            },
                            autoClose: 2000,
                            type: 3,
                            overlay: false
                        });
                    }
                };
                $.ajax(options);
                // $form.ajaxSubmit(options);
            });
        }
    }
    var page = new Page();
    page.init();
});