<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="nimo">
    <title>纷享开放平台</title>
    <link rel="shortcut icon" href="/web/favicon.ico">
    <!-- Bootstrap Core CSS -->
    <link href="/web/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/web/assets/style/sb-admin.css" rel="stylesheet">
    <!-- Custom Fonts -->
    <link href="/web/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <!-- Common CSS -->
    <link href="/web/assets/style/common.css" rel="stylesheet">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
    <div id="wrapper">
        <!-- Navigation -->
        <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
            <!--#include virtual="/web/modules/header/header.html"-->
        </nav>

        <div id="page-wrapper">
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="row">
                    <div class="col-lg-12">
                        <ol class="breadcrumb">
                            <li class="back"><a href="/web/tpls/serviceList/index.html">服务号列表</a></li>
                            <li><a id="breadcrumb" href="#">服务号资料查看</a></li>
                            <li class="active"><span>修改资料</span></li>
                        </ol>
                    </div>
                    <div class="col-lg-6">
                        <form id="form" class="form-app-modify form-horizontal">
                        </form>
                    </div>
                </div>
                <!-- /.row -->
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- /#page-wrapper -->
    </div>
    <!-- /#wrapper -->

    <script id="form_tpl" type="text/template">		
		<div class="form-group">
			<label class="col-sm-2 control-required-label">登录授权发起页域名</label>
			<div class="col-sm-10">
				<input type="text" class="form-control" value="<%= obj.callBackDomain %>" name="callBackDomain">
			</div>
		</div>

		<div class="form-group">
			<label class="col-sm-2 control-required-label">消息与事件接收URL</label>
			<div class="col-sm-10">
				<input class="form-control" value="<%= obj.callBackMsgUrl %>" name="callBackMsgUrl">
			</div>
		</div>

		<div class="form-group">
			<label class="col-sm-2 control-un-required-label">IP白名单</label>
			<div class="col-sm-10">
				<input class="form-control ipwhitelist-input" value="<%= obj.ipWhiteList ? obj.ipWhiteList.join(';') : '' %>">
				<p class="ipwhitelist-text-tips text-muted">多个白名单地址使用“;”分隔</p>
			</div>
		</div>
		
		<div class="form-group">
			<label class="col-sm-2 control-required-label">token</label>
			<div class="col-sm-10 form-inline">
				<input class="form-control col-md-11" value="<%= obj.token %>" name="token">
				<a href="javascript:;" class="btn btn-info pull-right js-random" data-value="token">随机获取</a>
			</div>
		</div>
		
		<div class="form-group">
			<label class="col-sm-2 control-required-label">EncodingAESKey</label>
			<div class="col-sm-10 form-inline">
				<input class="form-control col-md-11" value="<%= obj.encodingAesKey %>" name="encodingAesKey">
				<a href="javascript:;" class="btn btn-info pull-right js-random" data-value="encodingAesKey">随机获取</a>
			</div>
		</div>
		
		<div class="form-group">
			<div class="col-sm-offset-2 col-sm-10">
				<button type="button" class="btn btn-default js-cancel">取消</button>
				<button type="submit" class="btn btn-info" data-loading-text="正在保存...">保存</button>
			</div>
		</div>
	</script>

    <script src="/web/assets/libs/seajs/2.2.0/sea.js"></script>
    <script src="/web/assets/js/common.js"></script>
    <script>
		seajs.use('/web/tpls/serviceDevModify/main');
	</script>
</body>

</html>