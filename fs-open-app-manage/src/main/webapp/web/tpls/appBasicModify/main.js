define(function(require, exports, module) {
	var Side = require('side'),	
		side = new Side({"nav":"app", "subNav" :"app-list"});
		side.init();
		
	require('bootstrap');
	require('ajaxSubmit');
	require('validator');
	var util = require('util'),
		ajaxSubmit = require('ajaxSubmit'),
		dialog = require('dialog');


	var Page = function(){
		this.appId = util.getTplQueryParams('id') ? util.getTplQueryParams('id') : -1;
		$('#breadcrumb').attr('href', '/web/tpls/appDetail/index.html?id=' + this.appId);
	}
	Page.prototype = {
		init : function(){
			this.load();
			this.uploadImage();
		},
		load : function(){
			var me = this;
			$.ajax({ 
				url : '/open/manage/rest/openApp/loadAppByAppId',
				timeout : 10000, //超时时间设置，单位毫秒
				data : {
					appId : me.appId
				},
				success : function(response){
					if(response.errCode == 0){
						var data = response.data;
						me.appClass = data.appClass;
						me.appCreater = data.appCreater;
						data = _.extend({trialDays:15},data);
						me.renderHtml(data);
						me.bindEvent();
						me.loadAppClass();
						me.valid();
					}else{
						dialog.open({
							container : {
								content : '请求失败'
							},
							autoClose : 2000,
							type : 2,
							overlay : false
						});
					}				
				},
				error: function () {
                    dialog.open({
						container : {
							content : '网络链接失败，请稍后重试！'
						},
						autoClose : 2000,
						type : 3,
						overlay : false
					});
                }
			});
		},
		loadAppClass : function(){
			var me = this;
			$.ajax({ 
				url : '/open/manage/rest/openApp/listAppClass',
				timeout : 10000, //超时时间设置，单位毫秒
				success : function(response){
					if(response.errCode == 0){
						me.renderAppClassHtml(response.data);					
					}else{
						dialog.open({
							container : {
								content : '请求失败'
							},
							autoClose : 2000,
							type : 2,
							overlay : false
						});
					}				
				},
				error: function () {
                    dialog.open({
						container : {
							content : '网络链接失败，请稍后重试！'
						},
						autoClose : 2000,
						type : 3,
						overlay : false
					});
                }
			});
		},
		renderHtml : function(data){
			var me = this;
			$('.form-app-modify').html(_.template($('#form_tpl').html())(data));
			me.renderLogo();
		},
		renderAppClassHtml : function(data){
			var me = this,
				html = '';
			_.each(data, function(item){
				html += '<option value="' + item.dictKey + '">' + item.dictValue + '</option>';
			});
			$('#app_list').append(html).val(me.appClass);
		},
		renderLogo : function(){
			var me = this;
			setTimeout(function() {
				var logo = $('.app-logo'),
					picture = $('.modify-picture');
				function setSrc(img,imgDefaultSrc){
					var $img = $(img),
						imgSrc = $img.attr('data-src');
					img.onerror = function() {
						img.src = imgDefaultSrc;
					};
					img.src = imgSrc;
				}
				_.each(logo, function(img) {
					setSrc(img,'/web/assets/images/default.jpg');
				});
				_.each(picture, function(img) {
					setSrc(img,'/web/assets/images/default2.png');
				});
			}, 0);              
		},
		uploadImage : function(){
			var me = this;
			$('.ImgUploadFile').on('change', function(evt){
				evt.stopPropagation();
				if(1){
					var options = {
						type: 'POST',
						url: '/support/assets/images/upload',
						resetForm: false,
						timeout: 6000,
						dataType: 'json',
						traditional: true,
						data: {
						},
						beforeSerialize: function() {
						},
						success: function(data) {
							if (data.errCode === 0) {
								$(me.imgboxtarget).find('img').attr('src',data.data);
								$(me.imgboxtarget).find('input').val(data.data);
							} else {
								dialog.open({
									container : {
										content : data.errMsg || '图片添加失败，请找林锋解决问题！'
									},
									autoClose : 2000,
									type : 2,
									overlay : false
								});
							}
						},
						error: function() {
							dialog.open({
								container : {
									content :  '网络链接失败，请稍后重试！'
								},
								autoClose : 2000,
								type : 2,
								overlay : false
							});
						}
					};
					/**
					 * ajaxFom ，jquery插件方式去实现ajax提交表单行为
					 *
					 * @options 各种配置
					 */
					$('.imageUpload').ajaxSubmit(options);
				}
			});
		},
		valid : function(){
			var me = this;
			$('#form').bootstrapValidator({
				excluded: [':disabled', ':hidden', ':not(:visible)'],
				fields: {				
					appName: {
						message: '请输入应用名称',
						validators: {
							notEmpty: {
								message: '请输入应用名称'
							}
						}
					},
					appIntro: {
						message: '请输入应用简介',
						validators: {
							notEmpty: {
								message: '请输入应用简介'
							},
							stringLength: {
								max: 20,
								message: '简介字数不能超过20字'
							}
						}
					},
					appClass: {
						message: '请选择应用类型',
						validators: {
							notEmpty: {
								message: '请选择应用类型'
							}
						}
					},
					appDesc: {
						message: '请选择应用描述',
						validators: {
							notEmpty: {
								message: '请选择应用描述'
							},
							stringLength: {
								max: 500,
								message: '详情字数不能超过500字'
							}
						}
					},
					payDesc: {
						message: '请输入付费描述',
						validators: {
							notEmpty: {
								message: '请输入付费描述'
							}
						}
					},
					trialDays: {
						message: '请输入试用天数',
						validators: {
							notEmpty: {
								message: '请输入试用天数'
							}
						}
					}
				}
			})
			.on('success.form.bv', function(e) {
				e.preventDefault();
				$('button[type="submit"]').button('loading');
				var $form = $(e.target);			
				var options = {
					type: 'POST',
					url: '/open/manage/rest/openApp/updateDevAppBase',
					data : {
						appId : me.appId,
						appCreater : me.appCreater
					},
					success : function(response) {
						$('button[type="submit"]').button('reset');
						if(response.errCode == 0){  
							dialog.open({
								container : {
									content : '修改成功'
								},
								autoClose : 2000,
								type : 1,
								overlay : false,
								callback : function(){
									location.href = '/web/tpls/appDetail/index.html?id=' + me.appId;
								}
							});
						}else{                      
							dialog.open({
								container : {
									content : '修改失败, ' + response.errMsg
								},
								autoClose : 2000,
								type : 2,
								overlay : false
							});
						}           
					},
					error: function () {
						$('button[type="submit"]').button('reset');
						dialog.open({
							container : {
								content : '网络链接失败，请稍后重试！'
							},
							autoClose : 2000,
							type : 3,
							overlay : false
						});
					}
				};
				$form.ajaxSubmit(options); 
			});
		},
		bindEvent : function(){
			var me = this;
			//上传logo
			$('input[type="file"]').on('change', function(evt) {        
				evt.stopPropagation();
				var $target = $(evt.currentTarget),
					$logo = $target.closest('tr').find('td:first-child .app-logo');
					fileType = ['image/jpeg', 'image/png'];
				$target.attr('name', $target.attr('data-name'));
				if (window.FileReader) {
					var f = this.files[0];                  
					var reader = new FileReader();
					reader.onload = function() {
						$logo.attr('src', this.result);
					};
					reader.readAsDataURL(f);
				} else {
					var logo = $logo.get(0);
					var value = $target.val();
					if (logo) {
						logo.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = value;
					}
				}
			});
			
			//取消
			$('.js-cancel').on('click', function(evt) { 
				evt.stopPropagation();
				location.href = location.href;
			});

			//图片box 点击上传图片
			$('.imgBoxDiv').on('click','.imgBtn_Save',function(evt){
				evt.stopPropagation();
				me.imgboxtarget = $(evt.currentTarget).parents('.imgBox');
				$('.ImgUploadFile').trigger('click');
			});

			//图片box 点击删除
			$('.imgBoxDiv').on('click','.imgBtn_Del',function(evt){
				evt.stopPropagation();
				var $this = $(this);
				dialog.open({
					container: {
						header: '删除详情图片',
						content: '你确定要删除此图片吗？',
						yesFn: function () {
							$this.parents('.imgBox').remove();
						},
						noFn: true
					},
					fixed: false
				});
			});

			//图片box 点击添加一个BOX
			$('.imgBox_Add').on('click', function (evt) {
				evt.stopPropagation();
				var imgBox =
					"<div class='imgBox'>" +
					"<table style='width: 100%;'>" +
					"<tbody>" +
					"<tr>" +
					"<td>" +
					"<div class='imgDisplay'>" +
					"<span>" +
					"<img class='modify-picture' data-src>" +
					"</span>" +
					"</div>" +
					"</td>" +
					"<td>&nbsp;&nbsp;</td>" +
					"<td>&nbsp;&nbsp;</td>" +
					"<td>" +
					"<div class='imgBtns'>" +
					"<div class='input-group' style='width: 100%;'>" +
					"<input class='form-control' name='detailImageUrls' readonly value=>" +
					"<div class='input-group-btn uploadImgBtn'>" +
					"<div class='btn btn-primary imgBtn_Save'><i class='glyphicon glyphicon-folder-open'></i><span>上传</span></div>" +
					"</div>" +
					"</div>" +
					"<button type='button' class='btn btn-info imgBtn_Del'>删除</button>" +
					"</div>" +
					"</td>" +
					"</tr>" +
					"</tbody>" +
					"</table>" +
					"</div>";
				$('.imgAddBox').before(imgBox);
			});

			/**
			 * 付费类型选择后的动作：显示相关说明
			 * val = 1 付费类型，需要设置 试用类型
			 * val = 2 免费类型，不需要其他设置
			 */
			$('.j-payType input').on('change', function() {
				var val = $(this).val();
				if (val == 1) {
					$('.j-payDesc').removeClass('hide');
					$('.try-list').removeClass('hide');
					if($('#try_list').val() != 1){
						$('.try-days').removeClass('hide');
					}
					$('.j-purchaseOnline').removeClass('hide');
				} else {
					$('.j-payDesc').addClass('hide');
					$('.try-list').addClass('hide');
					$('.try-days').addClass('hide');
					$('.j-purchaseOnline').addClass('hide');
					$('#form').data('bootstrapValidator').disableSubmitButtons(false);
				}
			});

			$('#try_list').on('change',function(evt){
				if(this.value == 1){
					$('.try-days').addClass('hide');
					$('#form').data('bootstrapValidator').disableSubmitButtons(false);
				}else{
					$('.try-days').removeClass('hide');
				}
			})
		}
	};

	var page = new Page();
	page.init();
});