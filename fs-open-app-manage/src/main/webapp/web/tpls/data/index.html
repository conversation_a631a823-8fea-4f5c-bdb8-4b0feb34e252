<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>纷享开放平台</title>
	
	<link rel="shortcut icon" href="/web/favicon.ico">

    <!-- Bootstrap Core CSS -->
    <link href="/web/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/web/assets/style/sb-admin.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="/web/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css">
	
	<!-- Common CSS -->
    <link href="/web/assets/style/common.css" rel="stylesheet">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

</head>

<body>

    <div id="wrapper">

        <!-- Navigation -->
        <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
            <!--#include virtual="/web/modules/header/header.html"-->    
        </nav>

        <div id="page-wrapper">

            <div class="container-fluid">

                <!-- Page Heading -->
                <div class="row">
                    <div class="col-lg-12">
                        <ol class="breadcrumb">
                            <li class="active">
                                <span>应用数据</span>
                            </li>
                        </ol>
						
						<!--<div class="margin-b-20px text-right">
							<div class="form-inline">						
								<div class="input-group">
									<input type="text" class="form-control" placeholder="根据ID或名称搜索应用" id="search_text">
									<span class="btn-search glyphicon glyphicon-search" aria-hidden="true"></span>
								</div>							
							</div>
						</div>-->
						<table class="table table-bordered data-table">
							<thead>
								<tr>
									<th>应用名称</th>
									<th>累计开启企业</th>
									<th>累计使用企业</th>
									<th>累计使用用户</th>
									<th>昨日活跃企业</th>
									<th>昨日活跃用户</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								
							</tbody>
						</table>								
						
						<div class="clearfix">
							<div class="total-count pull-left"></div>
							<div class="pull-right">
								<ul class="pagination" id="pageUl"></ul>
							</div>	
						</div>
						
                    </div>
                </div>
                <!-- /.row -->

            </div>
            <!-- /.container-fluid -->

        </div>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->
	
	<script id="table_tpl" type="text/template">
		<%_.each(obj, function(item) {%>
			<tr>
				<td><%=item.appName %></td>
				<td><%=item.cumulativeOpenEaNum %></td>
				<td><%=item.cumulativeUseEaNum %></td>
				<td><%=item.cumulativeUserNum %></td>
				<td><%=item.yesterdayActiveEaNum %></td>
				<td><%=item.yesterdayActiveUserNum %></td>
				<td>
					<a href="/web/tpls/dataDetail/index.html?id=<%=item.appId %>">明细数据</a>
				</td>
			</tr>
		<%});%>
		
		<%if(obj.length == 0) {%>
			<tr>
				<td class="text-center" colspan="7">数据不存在</td>				
			</tr>
		<%};%>
	</script>

    <script src="/web/assets/libs/seajs/2.2.0/sea.js"></script>
	<script src="/web/assets/js/common.js"></script>
	<script>
		seajs.use('/web/tpls/data/main');
	</script>

</body>

</html>
