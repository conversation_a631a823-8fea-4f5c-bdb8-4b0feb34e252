define(function(require, exports, module) {
	var Side = require('side'),
		side = new Side({"nav":"developer", "subNav" :"developer-list"});
		side.init();
	
	require('bootstrap');
	require('paginator');
	require('ajaxSubmit');
	require('validator');
	var util = require('util'),
		dialog = require('dialog');


		
	var Page = function(){
		this.currentPage = 1;	
	};
	Page.prototype = {
		init : function(){
			this.load();
		},
		load : function(){
			var me = this;
			$.ajax({
				url : '/open/manage/rest/openDev/queryPager',
				timeout : 10000, //超时时间设置，单位毫秒
				data : {
					currentPage : me.currentPage,
					pageSize : 10
				},
				success : function(response){
					if(response.errCode == 0){
						var data = response.data;
						var opts = {
							currentPage : data.currentPage,
							totalPages : data.pageTotal
						};
						$('.total-count').text('共计' + data.recordSize + '个');
						me.renderHtml(data.data);
						me.renderPage(opts);
					}else{
						dialog.open({
							container : {
								content : '请求失败'
							},
							autoClose : 2000,
							type : 2,
							overlay : false
						});
					}
				},
				error: function () {
                    dialog.open({
						container : {
							content : '网络链接失败，请稍后重试！'
						},
						autoClose : 2000,
						type : 3,
						overlay : false
					});
                }
			});
		},
		renderHtml : function(data){
			var me = this;
			data.helper = function (time, fmt) { //author: meizz 
				var date = new Date(time);
				var o = {
					"M+": date.getMonth() + 1, //月份 
					"d+": date.getDate(), //日 
					"h+": date.getHours(), //小时 
					"m+": date.getMinutes(), //分 
					"s+": date.getSeconds(), //秒 
					"q+": Math.floor((date.getMonth() + 3) / 3), //季度 
					"S": date.getMilliseconds() //毫秒 
				};
				if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
				for (var k in o)
				if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
				return fmt;
			}
			$('tbody').html(_.template($('#table_tpl').html())(data));
		},
		renderPage : function(opts){
			var me = this;
			var options = {
				currentPage : opts.currentPage || 1,
				totalPages : opts.totalPages,
				numberOfPages : 5,
				onPageClicked : function(e, e, type, page){
					me.currentPage = page;
					me.load();
				}
			}
			$('#pageUl').bootstrapPaginator(options);		
		}
	}		
	var page = new Page();
	page.init();
});

