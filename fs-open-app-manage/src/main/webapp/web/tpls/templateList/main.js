define(function(require, exports, module) {
	var Side = require('side'),
		side = new Side({"nav":"app", "subNav" :"app-template"});
		side.init();
	
	require('bootstrap');
	require('paginator');
	
	var dialog = require('dialog');
	
	var Page = function(){
		this.currentPage = 1;	
	}
	Page.prototype = {
		init : function(){
			this.load();
		},
		load : function(){
			var me = this;
			$.ajax({ 
				url : '/open/manage/rest/template/templateList',
				timeout : 10000, //超时时间设置，单位毫秒
				data : {
					currentPage : me.currentPage,
					pageSize : 10
				},
				success : function(response){
					if(response.errCode == 0){
						var data = response.data;
						var opts = {
							currentPage : data.currentPage,
							totalPages : data.pageTotal
						};
						$('.total-count').text('共计' + data.recordSize + '个');
						me.renderHtml(data.data);
						me.renderPage(opts);
					}else{
						dialog.open({
							container : {
								content : '请求失败'
							},
							autoClose : 2000,
							type : 2,
							overlay : false
						});
					}					
				},
				error: function () {
                    dialog.open({
						container : {
							content : '网络链接失败，请稍后重试！'
						},
						autoClose : 2000,
						type : 3,
						overlay : false
					});
                }
			});
		},
		delete : function(){
			var me = this;
			$.ajax({ 
				url : '/open/manage/rest/template/templateDelete',
				timeout : 10000, //超时时间设置，单位毫秒
				data : {
					id : me.id
				},
				success : function(response){
					if(response.errCode == 0){
						dialog.open({
							container : {
								content : '删除成功'
							},
							autoClose : 2000,
							type : 1,
							overlay : true
						});
					}else{
						dialog.open({
							container : {
								content : '删除失败'
							},
							autoClose : 2000,
							type : 2,
							overlay : true
						});
					}
				},
				error: function () {
                    dialog.open({
						container : {
							content : '网络链接失败，请稍后重试！'
						},
						autoClose : 2000,
						type : 3,
						overlay : true
					});
                }
			});
		},
		renderHtml : function(data){
			var me = this;
			$('tbody').html(_.template($('#table_tpl').html())(data));
			me.bindEvent();
		},
		renderPage : function(opts){
			var me = this;
			var options = {
				currentPage : opts.currentPage || 1,
				totalPages : opts.totalPages,
				numberOfPages : 5,
				onPageClicked : function(e, e, type, page){
					me.currentPage = page;
					me.load();
				}
			}
			$('#pageUl').bootstrapPaginator(options);		
		},
		bindEvent : function(){
			var me = this;
			
			var btnFn = function(evt){
				evt.stopPropagation();
				me.delete();
				return false;
			};
			
			$('.js-delete').on('click', function(evt){
				evt.stopPropagation();
				dialog.open({
					container : {
						header : '提示',
						content : '您确认删除“' + $(evt.target).attr('data-name') + '”模板吗？',
						yesFn : btnFn,
						noFn : true
					},
					fixed : false
				});
				me.id = $(evt.target).attr('data-value');
			});
			
		}
	}
	var page = new Page();
	page.init();
});