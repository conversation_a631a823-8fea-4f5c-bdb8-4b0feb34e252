/* 
 * 开平后台页面重构
 * date: 2017-02-04
 */
.g-doc {
  font-size: 12px;
  font-family: "microsoft yahei", "Helvetica Neue", "Helvetica", "Arial", "sans-serif";
}
.g-hd {
  border-bottom-width: 0;
  background-color: #d7dde4;
}
.g-hd .container-fluid {
  padding-left: 0;
}
.g-hd .h-brand {
  display: block;
  width: 225px;
  height: 50px;
  padding: 0 0 0 52px;
  font-size: 16px;
  line-height: 50px;
  font-weight: 600;
  color: #fff;
  text-decoration: none;
  background: #384655 url(../images/logo.png) no-repeat scroll 18px 8px;
}
.g-hd .h-user {
  padding-right: 30px;
}
.g-hd .h-dropdown {
  padding-left: 15px;
  line-height: 50px;
  font-weight: 600;
  color: #4f5f6f;
  font-size: 13px;
}
.g-hd .h-logout {
  padding-left: 0;
  color: #4f5f6f;
}
.g-hd .h-logout:hover {
  background-color: #d7dde4;
}
.g-bd {
  padding-top: 51px;
}
@media (min-width: 768px) {
  .g-sd {
    position: fixed;
    top: 50px;
    left: 0;
    bottom: 0;
    width: 225px;
    background-color: #4f5f6f;
  }
  .g-content {
    margin-left: 225px;
  }
}
@media (max-width: 768px) {
  .g-sd {
    display: none;
  }
}
.g-content {
  margin-bottom: 40px;
  padding: 10px;
  background-color: #fff;
}
.m-tips {
  padding: 20px;
  margin: 20px 0;
  border: 1px solid #eee;
  border-radius: 3px;
  border-left-width: 5px;
}
.m-tips.warning {
  border-left-color: #aa6708;
}
.m-sider {
  width: 100%;
}
.m-sider .sd-toggle {
  float: initial;
  width: 100%;
  margin: 0;
  border-width: 0;
  font-weight: 600;
  background-color: #4f5f6f;
}
.m-sider .sd-panel-hd {
  display: block;
  width: 100%;
  padding-left: 32px;
  font-size: 14px;
  color: #9d9d9d;
}
.m-sider .sd-panel-hd:hover,
.m-sider .sd-panel-hd:focus {
  color: #fff;
  background-color: #586979;
}
.m-sider .sd-panel-hd::before {
  padding-right: 6px;
  vertical-align: -3px;
}
.m-sider .developer .sd-panel-hd::before {
  content: url(../images/developer.png);
}
.m-sider .app .sd-panel-hd::before,
.m-sider .service .sd-panel-hd::before {
  content: url(../images/app.png);
}
.m-sider .data .sd-panel-hd::before {
  content: url(../images/data.png);
}
.m-sider .content .sd-panel-hd::before,
.m-sider .devQuery .sd-panel-hd::before,
.m-sider .esign .sd-panel-hd::before,
.m-sider .stocks .sd-panel-hd::before {
  content: url(../images/content.png);
}
.m-sider .sd-toggle-icon {
  float: right;
  margin-right: 10px;
  color: #eee;
  font-size: 13px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: transform 0.5s ease-in-out;
  -webkit-transition: -webkit-transform 0.5s ease-in-out;
}
.m-sider .sd-toggle-icon.sd-toggled {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.m-sider .sd-panel-collapse {
  padding: 0;
  list-style-type: none;
}
.m-sider .sd-panel-collapse a {
  display: block;
  padding: 10px 15px 10px 62px;
  text-decoration: none;
  font-size: 12px;
  color: #fff;
}
.m-sider .sd-panel-collapse a:hover,
.m-sider .sd-panel-collapse a:focus {
  background-color: #586979;
}
.m-sider .sd-panel-collapse a.sd-on {
  background-color: #59c2e6;
}
#service-statis .breadcrumb {
  padding: 8px 0;
  font-size: 18px;
  background-color: #fff;
}
#service-statis .queryteam {
  margin-bottom: 20px;
  padding: 8px 30px;
  border: 1px solid #e7e7e7;
  border-radius: 4px;
  text-align: right;
  background-color: #F1F5FA;
}
#service-statis table {
  font-size: 14px;
}
