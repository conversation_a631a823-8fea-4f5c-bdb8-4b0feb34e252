define("arale/detector/1.1.0/detector",[],function(e,n,r){function o(e){return Object.prototype.toString.call(e)}function i(e){return"[object Object]"===o(e)}function t(e){return"[object Function]"===o(e)}function a(e){return"[object Array]"===o(e)}function s(e,n){if(a(e))for(var r=0,o=e.length;o>r&&n.call(e,e[r],r)!==!1;r++);}function c(e){if(!w.test(e))return null;var n,r,o,i,t;-1!==e.indexOf("trident/")&&(n=/\btrident\/([0-9.]+)/.exec(e),n&&n.length>=2&&(o=n[1],v_version=n[1].split("."),v_version[0]=parseInt(v_version[0],10)+4,t=v_version.join("."))),n=w.exec(e),i=n[1];var a=n[1].split(".");return t===void 0&&(t=i),a[0]=parseInt(a[0],10)-4,r=a.join("."),o===void 0&&(o=r),{browserVersion:t,browserMode:i,engineVersion:o,engineMode:r,compatible:o!==r}}function b(e,n,r){r===void 0&&(r=l);var a=t(n)?n.call(null,r):n;if(!a)return null;var s={name:e,version:"-1",codename:""},c=o(a);if(a===!0)return s;if("[object String]"===c){if(-1!==r.indexOf(a))return s}else{if(i(a))return a.hasOwnProperty("version")&&(s.version=a.version),s;if(a.exec){var b=a.exec(r);if(b)return s.version=b.length>=2&&b[1]?b[1].replace(/_/g,"."):"-1",s}}}function u(e,n,r,o){var i=h;s(n,function(n){var r=b(n[0],n[1],e);return r?(i=r,!1):void 0}),r.call(o,i.name,i.version)}var d={},l=navigator.userAgent||"";navigator.platform||"",navigator.vendor||"";var f=window.external,w=/\b(?:msie|ie) ([0-9.]+)/,v=[["nokia",function(e){return-1!==e.indexOf("nokia ")?/\bnokia ([0-9]+)?/:/\bnokia[\d]/.test(e)?/\bnokia(\d+)/:"nokia"}],["wp",function(e){return-1!==e.indexOf("windows phone ")||-1!==e.indexOf("xblwp")||-1!==e.indexOf("zunewp")||-1!==e.indexOf("windows ce")}],["pc","windows"],["ipad","ipad"],["ipod","ipod"],["iphone","iphone"],["mac","macintosh"],["mi",function(e){return-1!==e.indexOf("mi-one plus")?{version:"1s"}:/\bmi ([0-9.as]+)/}],["aliyun","aliyunos"],["meizu",/\bm([0-9]+)\b/],["nexus",/\bnexus ([0-9.]+)/],["android","android"],["blackberry","blackberry"]],p=[["wp",function(e){return-1!==e.indexOf("windows phone ")?/\bwindows phone (?:os )?([0-9.]+)/:-1!==e.indexOf("xblwp")?/\bxblwp([0-9.]+)/:-1!==e.indexOf("zunewp")?/\bzunewp([0-9.]+)/:"windows phone"}],["windows",/\bwindows nt ([0-9.]+)/],["macosx",/\bmac os x ([0-9._]+)/],["ios",/\bcpu(?: iphone)? os ([0-9._]+)/],["yunos",/\baliyunos ([0-9.]+)/],["android",/\bandroid[ -]([0-9.]+)/],["chromeos",/\bcros i686 ([0-9.]+)/],["linux","linux"],["windowsce",/\bwindows ce(?: ([0-9.]+))?/],["symbian",/\bsymbianos\/([0-9.]+)/],["blackberry","blackberry"]],x=[["trident",w],["webkit",/\bapplewebkit\/([0-9.+]+)/],["gecko",/\bgecko\/(\d+)/],["presto",/\bpresto\/([0-9.]+)/]],m=[["360",function(){if(f)try{return f.twGetVersion(f.twGetSecurityID(window))}catch(e){try{return-1!==f.twGetRunPath.toLowerCase().indexOf("360se")||!!f.twGetSecurityID(window)}catch(e){}}return/\b360(?:se|ee|chrome)/}],["mx",function(){if(f)try{return(f.mxVersion||f.max_version).split(".")}catch(e){}return/\bmaxthon(?:[ \/]([0-9.]+))?/}],["sg",/ se ([0-9.x]+)/],["tw",function(){if(f)try{return-1!==f.twGetRunPath.toLowerCase().indexOf("theworld")}catch(e){}return"theworld"}],["green","greenbrowser"],["qq",/\bqqbrowser\/([0-9.]+)/],["tt",/\btencenttraveler ([0-9.]+)/],["lb",function(e){if(-1===e.indexOf("lbbrowser"))return!1;var n="-1";if(window.external&&window.external.LiebaoGetVersion)try{n=window.external.LiebaoGetVersion()}catch(r){}return{version:n}}],["tao",/\btaobrowser\/([0-9.]+)/],["fs",/\bcoolnovo\/([0-9.]+)/],["sy","saayaa"],["baidu",/\bbidubrowser[ \/]([0-9.x]+)/],["mi",/\bmiuibrowser\/([0-9.]+)/],["ie",w],["chrome",/ (?:chrome|crios|crmo)\/([0-9.]+)/],["android",function(e){return-1!==e.indexOf("android")?/\bversion\/([0-9.]+(?: beta)?)/:void 0}],["safari",/\bversion\/([0-9.]+(?: beta)?)(?: mobile(?:\/[a-z0-9]+)?)? safari\//],["firefox",/\bfirefox\/([0-9.ab]+)/],["opera",/\bopera.+version\/([0-9.ab]+)/],["uc",function(e){return-1!==e.indexOf("ucbrowser")?/\bucbrowser\/([0-9.]+)/:/\bucweb([0-9.]+)/}]],h={name:"na",version:"-1"},g=function(e){e=(e||"").toLowerCase();var n={};u(e,v,function(e,r){var o=parseFloat(r);n.device={name:e,version:o,fullVersion:r},n.device[e]=o},n),u(e,p,function(e,r){var o=parseFloat(r);n.os={name:e,version:o,fullVersion:r},n.os[e]=o},n);var r=c(e);return u(e,x,function(e,o){var i=o;r&&(o=r.engineVersion||r.engineMode,i=r.engineMode);var t=parseFloat(o);n.engine={name:e,version:t,fullVersion:o,mode:parseFloat(i),fullMode:i,compatible:r?r.compatible:!1},n.engine[e]=t},n),u(e,m,function(e,o){var i=o;r&&("ie"===e&&(o=r.browserVersion),i=r.browserMode);var t=parseFloat(o);n.browser={name:e,version:t,fullVersion:o,mode:parseFloat(i),fullMode:i,compatible:r?r.compatible:!1},n.browser[e]=t},n),n};d=g(l),d.detect=g,r.exports=d});
