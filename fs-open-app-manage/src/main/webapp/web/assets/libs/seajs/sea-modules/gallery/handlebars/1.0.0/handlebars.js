define("gallery/handlebars/1.0.0/handlebars",[],function(t,e,s){this.Handlebars={},function(t){t.VERSION="1.0.0-rc.3",t.COMPILER_REVISION=2,t.REVISION_CHANGES={1:"<= 1.0.rc.2",2:">= 1.0.0-rc.3"},t.helpers={},t.partials={},t.registerHelper=function(t,e,s){s&&(e.not=s),this.helpers[t]=e},t.registerPartial=function(t,e){this.partials[t]=e},t.registerHelper("helperMissing",function(t){if(2===arguments.length)return void 0;throw Error("Could not find property '"+t+"'")});var e=Object.prototype.toString,s="[object Function]";t.registerHelper("blockHelperMissing",function(i,n){var r=n.inverse||function(){},a=n.fn,o=e.call(i);return o===s&&(i=i.call(this)),i===!0?a(this):i===!1||null==i?r(this):"[object Array]"===o?i.length>0?t.helpers.each(i,n):r(this):a(i)}),t.K=function(){},t.createFrame=Object.create||function(e){t.K.prototype=e;var s=new t.K;return t.K.prototype=null,s},t.logger={DEBUG:0,INFO:1,WARN:2,ERROR:3,level:3,methodMap:{0:"debug",1:"info",2:"warn",3:"error"},log:function(e,s){if(e>=t.logger.level){var i=t.logger.methodMap[e];"undefined"!=typeof console&&console[i]&&console[i].call(console,s)}}},t.log=function(e,s){t.logger.log(e,s)},t.registerHelper("each",function(e,s){var i,n=s.fn,r=s.inverse,a=0,o="";if(s.data&&(i=t.createFrame(s.data)),e&&"object"==typeof e)if(e instanceof Array)for(var h=e.length;h>a;a++)i&&(i.index=a),o+=n(e[a],{data:i});else for(var l in e)e.hasOwnProperty(l)&&(i&&(i.key=l),o+=n(e[l],{data:i}),a++);return 0===a&&(o=r(this)),o}),t.registerHelper("if",function(i,n){var r=e.call(i);return r===s&&(i=i.call(this)),!i||t.Utils.isEmpty(i)?n.inverse(this):n.fn(this)}),t.registerHelper("unless",function(e,s){var i=s.fn,n=s.inverse;return s.fn=n,s.inverse=i,t.helpers["if"].call(this,e,s)}),t.registerHelper("with",function(t,e){return e.fn(t)}),t.registerHelper("log",function(e,s){var i=s.data&&null!=s.data.level?parseInt(s.data.level,10):1;t.log(i,e)})}(this.Handlebars);var i=function(){function t(){this.yy={}}var e={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,simpleInverse:6,statements:7,statement:8,openInverse:9,closeBlock:10,openBlock:11,mustache:12,partial:13,CONTENT:14,COMMENT:15,OPEN_BLOCK:16,inMustache:17,CLOSE:18,OPEN_INVERSE:19,OPEN_ENDBLOCK:20,path:21,OPEN:22,OPEN_UNESCAPED:23,OPEN_PARTIAL:24,partialName:25,params:26,hash:27,DATA:28,param:29,STRING:30,INTEGER:31,BOOLEAN:32,hashSegments:33,hashSegment:34,ID:35,EQUALS:36,PARTIAL_NAME:37,pathSegments:38,SEP:39,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"CONTENT",15:"COMMENT",16:"OPEN_BLOCK",18:"CLOSE",19:"OPEN_INVERSE",20:"OPEN_ENDBLOCK",22:"OPEN",23:"OPEN_UNESCAPED",24:"OPEN_PARTIAL",28:"DATA",30:"STRING",31:"INTEGER",32:"BOOLEAN",35:"ID",36:"EQUALS",37:"PARTIAL_NAME",39:"SEP"},productions_:[0,[3,2],[4,2],[4,3],[4,2],[4,1],[4,1],[4,0],[7,1],[7,2],[8,3],[8,3],[8,1],[8,1],[8,1],[8,1],[11,3],[9,3],[10,3],[12,3],[12,3],[13,3],[13,4],[6,2],[17,3],[17,2],[17,2],[17,1],[17,1],[26,2],[26,1],[29,1],[29,1],[29,1],[29,1],[29,1],[27,1],[33,2],[33,1],[34,3],[34,3],[34,3],[34,3],[34,3],[25,1],[21,1],[38,3],[38,1]],performAction:function(t,e,s,i,n,r){var a=r.length-1;switch(n){case 1:return r[a-1];case 2:this.$=new i.ProgramNode([],r[a]);break;case 3:this.$=new i.ProgramNode(r[a-2],r[a]);break;case 4:this.$=new i.ProgramNode(r[a-1],[]);break;case 5:this.$=new i.ProgramNode(r[a]);break;case 6:this.$=new i.ProgramNode([],[]);break;case 7:this.$=new i.ProgramNode([]);break;case 8:this.$=[r[a]];break;case 9:r[a-1].push(r[a]),this.$=r[a-1];break;case 10:this.$=new i.BlockNode(r[a-2],r[a-1].inverse,r[a-1],r[a]);break;case 11:this.$=new i.BlockNode(r[a-2],r[a-1],r[a-1].inverse,r[a]);break;case 12:this.$=r[a];break;case 13:this.$=r[a];break;case 14:this.$=new i.ContentNode(r[a]);break;case 15:this.$=new i.CommentNode(r[a]);break;case 16:this.$=new i.MustacheNode(r[a-1][0],r[a-1][1]);break;case 17:this.$=new i.MustacheNode(r[a-1][0],r[a-1][1]);break;case 18:this.$=r[a-1];break;case 19:this.$=new i.MustacheNode(r[a-1][0],r[a-1][1]);break;case 20:this.$=new i.MustacheNode(r[a-1][0],r[a-1][1],!0);break;case 21:this.$=new i.PartialNode(r[a-1]);break;case 22:this.$=new i.PartialNode(r[a-2],r[a-1]);break;case 23:break;case 24:this.$=[[r[a-2]].concat(r[a-1]),r[a]];break;case 25:this.$=[[r[a-1]].concat(r[a]),null];break;case 26:this.$=[[r[a-1]],r[a]];break;case 27:this.$=[[r[a]],null];break;case 28:this.$=[[new i.DataNode(r[a])],null];break;case 29:r[a-1].push(r[a]),this.$=r[a-1];break;case 30:this.$=[r[a]];break;case 31:this.$=r[a];break;case 32:this.$=new i.StringNode(r[a]);break;case 33:this.$=new i.IntegerNode(r[a]);break;case 34:this.$=new i.BooleanNode(r[a]);break;case 35:this.$=new i.DataNode(r[a]);break;case 36:this.$=new i.HashNode(r[a]);break;case 37:r[a-1].push(r[a]),this.$=r[a-1];break;case 38:this.$=[r[a]];break;case 39:this.$=[r[a-2],r[a]];break;case 40:this.$=[r[a-2],new i.StringNode(r[a])];break;case 41:this.$=[r[a-2],new i.IntegerNode(r[a])];break;case 42:this.$=[r[a-2],new i.BooleanNode(r[a])];break;case 43:this.$=[r[a-2],new i.DataNode(r[a])];break;case 44:this.$=new i.PartialNameNode(r[a]);break;case 45:this.$=new i.IdNode(r[a]);break;case 46:r[a-2].push(r[a]),this.$=r[a-2];break;case 47:this.$=[r[a]]}},table:[{3:1,4:2,5:[2,7],6:3,7:4,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],22:[1,14],23:[1,15],24:[1,16]},{1:[3]},{5:[1,17]},{5:[2,6],7:18,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,6],22:[1,14],23:[1,15],24:[1,16]},{5:[2,5],6:20,8:21,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],20:[2,5],22:[1,14],23:[1,15],24:[1,16]},{17:23,18:[1,22],21:24,28:[1,25],35:[1,27],38:26},{5:[2,8],14:[2,8],15:[2,8],16:[2,8],19:[2,8],20:[2,8],22:[2,8],23:[2,8],24:[2,8]},{4:28,6:3,7:4,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],20:[2,7],22:[1,14],23:[1,15],24:[1,16]},{4:29,6:3,7:4,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],20:[2,7],22:[1,14],23:[1,15],24:[1,16]},{5:[2,12],14:[2,12],15:[2,12],16:[2,12],19:[2,12],20:[2,12],22:[2,12],23:[2,12],24:[2,12]},{5:[2,13],14:[2,13],15:[2,13],16:[2,13],19:[2,13],20:[2,13],22:[2,13],23:[2,13],24:[2,13]},{5:[2,14],14:[2,14],15:[2,14],16:[2,14],19:[2,14],20:[2,14],22:[2,14],23:[2,14],24:[2,14]},{5:[2,15],14:[2,15],15:[2,15],16:[2,15],19:[2,15],20:[2,15],22:[2,15],23:[2,15],24:[2,15]},{17:30,21:24,28:[1,25],35:[1,27],38:26},{17:31,21:24,28:[1,25],35:[1,27],38:26},{17:32,21:24,28:[1,25],35:[1,27],38:26},{25:33,37:[1,34]},{1:[2,1]},{5:[2,2],8:21,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,2],22:[1,14],23:[1,15],24:[1,16]},{17:23,21:24,28:[1,25],35:[1,27],38:26},{5:[2,4],7:35,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,4],22:[1,14],23:[1,15],24:[1,16]},{5:[2,9],14:[2,9],15:[2,9],16:[2,9],19:[2,9],20:[2,9],22:[2,9],23:[2,9],24:[2,9]},{5:[2,23],14:[2,23],15:[2,23],16:[2,23],19:[2,23],20:[2,23],22:[2,23],23:[2,23],24:[2,23]},{18:[1,36]},{18:[2,27],21:41,26:37,27:38,28:[1,45],29:39,30:[1,42],31:[1,43],32:[1,44],33:40,34:46,35:[1,47],38:26},{18:[2,28]},{18:[2,45],28:[2,45],30:[2,45],31:[2,45],32:[2,45],35:[2,45],39:[1,48]},{18:[2,47],28:[2,47],30:[2,47],31:[2,47],32:[2,47],35:[2,47],39:[2,47]},{10:49,20:[1,50]},{10:51,20:[1,50]},{18:[1,52]},{18:[1,53]},{18:[1,54]},{18:[1,55],21:56,35:[1,27],38:26},{18:[2,44],35:[2,44]},{5:[2,3],8:21,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,3],22:[1,14],23:[1,15],24:[1,16]},{14:[2,17],15:[2,17],16:[2,17],19:[2,17],20:[2,17],22:[2,17],23:[2,17],24:[2,17]},{18:[2,25],21:41,27:57,28:[1,45],29:58,30:[1,42],31:[1,43],32:[1,44],33:40,34:46,35:[1,47],38:26},{18:[2,26]},{18:[2,30],28:[2,30],30:[2,30],31:[2,30],32:[2,30],35:[2,30]},{18:[2,36],34:59,35:[1,60]},{18:[2,31],28:[2,31],30:[2,31],31:[2,31],32:[2,31],35:[2,31]},{18:[2,32],28:[2,32],30:[2,32],31:[2,32],32:[2,32],35:[2,32]},{18:[2,33],28:[2,33],30:[2,33],31:[2,33],32:[2,33],35:[2,33]},{18:[2,34],28:[2,34],30:[2,34],31:[2,34],32:[2,34],35:[2,34]},{18:[2,35],28:[2,35],30:[2,35],31:[2,35],32:[2,35],35:[2,35]},{18:[2,38],35:[2,38]},{18:[2,47],28:[2,47],30:[2,47],31:[2,47],32:[2,47],35:[2,47],36:[1,61],39:[2,47]},{35:[1,62]},{5:[2,10],14:[2,10],15:[2,10],16:[2,10],19:[2,10],20:[2,10],22:[2,10],23:[2,10],24:[2,10]},{21:63,35:[1,27],38:26},{5:[2,11],14:[2,11],15:[2,11],16:[2,11],19:[2,11],20:[2,11],22:[2,11],23:[2,11],24:[2,11]},{14:[2,16],15:[2,16],16:[2,16],19:[2,16],20:[2,16],22:[2,16],23:[2,16],24:[2,16]},{5:[2,19],14:[2,19],15:[2,19],16:[2,19],19:[2,19],20:[2,19],22:[2,19],23:[2,19],24:[2,19]},{5:[2,20],14:[2,20],15:[2,20],16:[2,20],19:[2,20],20:[2,20],22:[2,20],23:[2,20],24:[2,20]},{5:[2,21],14:[2,21],15:[2,21],16:[2,21],19:[2,21],20:[2,21],22:[2,21],23:[2,21],24:[2,21]},{18:[1,64]},{18:[2,24]},{18:[2,29],28:[2,29],30:[2,29],31:[2,29],32:[2,29],35:[2,29]},{18:[2,37],35:[2,37]},{36:[1,61]},{21:65,28:[1,69],30:[1,66],31:[1,67],32:[1,68],35:[1,27],38:26},{18:[2,46],28:[2,46],30:[2,46],31:[2,46],32:[2,46],35:[2,46],39:[2,46]},{18:[1,70]},{5:[2,22],14:[2,22],15:[2,22],16:[2,22],19:[2,22],20:[2,22],22:[2,22],23:[2,22],24:[2,22]},{18:[2,39],35:[2,39]},{18:[2,40],35:[2,40]},{18:[2,41],35:[2,41]},{18:[2,42],35:[2,42]},{18:[2,43],35:[2,43]},{5:[2,18],14:[2,18],15:[2,18],16:[2,18],19:[2,18],20:[2,18],22:[2,18],23:[2,18],24:[2,18]}],defaultActions:{17:[2,1],25:[2,28],38:[2,26],57:[2,24]},parseError:function(t){throw Error(t)},parse:function(t){function e(){var t;return t=s.lexer.lex()||1,"number"!=typeof t&&(t=s.symbols_[t]||t),t}var s=this,i=[0],n=[null],r=[],a=this.table,o="",h=0,l=0,c=0;this.lexer.setInput(t),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,this.lexer.yylloc===void 0&&(this.lexer.yylloc={});var p=this.lexer.yylloc;r.push(p);var u=this.lexer.options&&this.lexer.options.ranges;"function"==typeof this.yy.parseError&&(this.parseError=this.yy.parseError);for(var d,f,g,m,y,b,v,k,S,x={};;){if(g=i[i.length-1],this.defaultActions[g]?m=this.defaultActions[g]:((null===d||d===void 0)&&(d=e()),m=a[g]&&a[g][d]),m===void 0||!m.length||!m[0]){var H="";if(!c){S=[];for(b in a[g])this.terminals_[b]&&b>2&&S.push("'"+this.terminals_[b]+"'");H=this.lexer.showPosition?"Parse error on line "+(h+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+S.join(", ")+", got '"+(this.terminals_[d]||d)+"'":"Parse error on line "+(h+1)+": Unexpected "+(1==d?"end of input":"'"+(this.terminals_[d]||d)+"'"),this.parseError(H,{text:this.lexer.match,token:this.terminals_[d]||d,line:this.lexer.yylineno,loc:p,expected:S})}}if(m[0]instanceof Array&&m.length>1)throw Error("Parse Error: multiple actions possible at state: "+g+", token: "+d);switch(m[0]){case 1:i.push(d),n.push(this.lexer.yytext),r.push(this.lexer.yylloc),i.push(m[1]),d=null,f?(d=f,f=null):(l=this.lexer.yyleng,o=this.lexer.yytext,h=this.lexer.yylineno,p=this.lexer.yylloc,c>0&&c--);break;case 2:if(v=this.productions_[m[1]][1],x.$=n[n.length-v],x._$={first_line:r[r.length-(v||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(v||1)].first_column,last_column:r[r.length-1].last_column},u&&(x._$.range=[r[r.length-(v||1)].range[0],r[r.length-1].range[1]]),y=this.performAction.call(x,o,l,h,this.yy,m[1],n,r),y!==void 0)return y;v&&(i=i.slice(0,2*-1*v),n=n.slice(0,-1*v),r=r.slice(0,-1*v)),i.push(this.productions_[m[1]][0]),n.push(x.$),r.push(x._$),k=a[i[i.length-2]][i[i.length-1]],i.push(k);break;case 3:return!0}}return!0}},s=function(){var t={EOF:1,parseError:function(t,e){if(!this.yy.parser)throw Error(t);this.yy.parser.parseError(t,e)},setInput:function(t){return this._input=t,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var t=this._input[0];this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);return e?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},unput:function(t){var e=t.length,s=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e-1),this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var n=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[n[0],n[0]+this.yyleng-e]),this},more:function(){return this._more=!0,this},less:function(t){this.unput(this.match.slice(t))},pastInput:function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var t=this.match;return 20>t.length&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var t=this.pastInput(),e=Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var t,e,s,i,n;this._more||(this.yytext="",this.match="");for(var r=this._currentRules(),a=0;r.length>a&&(s=this._input.match(this.rules[r[a]]),!s||e&&!(s[0].length>e[0].length)||(e=s,i=a,this.options.flex));a++);return e?(n=e[0].match(/(?:\r\n?|\n).*/g),n&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],t=this.performAction.call(this,this.yy,this,r[i],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),t?t:void 0):""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var t=this.next();return t!==void 0?t:this.lex()},begin:function(t){this.conditionStack.push(t)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(t){this.begin(t)}};return t.options={},t.performAction=function(t,e,s,i){switch(s){case 0:if("\\"!==e.yytext.slice(-1)&&this.begin("mu"),"\\"===e.yytext.slice(-1)&&(e.yytext=e.yytext.substr(0,e.yyleng-1),this.begin("emu")),e.yytext)return 14;break;case 1:return 14;case 2:return"\\"!==e.yytext.slice(-1)&&this.popState(),"\\"===e.yytext.slice(-1)&&(e.yytext=e.yytext.substr(0,e.yyleng-1)),14;case 3:return e.yytext=e.yytext.substr(0,e.yyleng-4),this.popState(),15;case 4:return this.begin("par"),24;case 5:return 16;case 6:return 20;case 7:return 19;case 8:return 19;case 9:return 23;case 10:return 23;case 11:this.popState(),this.begin("com");break;case 12:return e.yytext=e.yytext.substr(3,e.yyleng-5),this.popState(),15;case 13:return 22;case 14:return 36;case 15:return 35;case 16:return 35;case 17:return 39;case 18:break;case 19:return this.popState(),18;case 20:return this.popState(),18;case 21:return e.yytext=e.yytext.substr(1,e.yyleng-2).replace(/\\"/g,'"'),30;case 22:return e.yytext=e.yytext.substr(1,e.yyleng-2).replace(/\\'/g,"'"),30;case 23:return e.yytext=e.yytext.substr(1),28;case 24:return 32;case 25:return 32;case 26:return 31;case 27:return 35;case 28:return e.yytext=e.yytext.substr(1,e.yyleng-2),35;case 29:return"INVALID";case 30:break;case 31:return this.popState(),37;case 32:return 5}},t.rules=[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|$)))/,/^(?:[\s\S]*?--\}\})/,/^(?:\{\{>)/,/^(?:\{\{#)/,/^(?:\{\{\/)/,/^(?:\{\{\^)/,/^(?:\{\{\s*else\b)/,/^(?:\{\{\{)/,/^(?:\{\{&)/,/^(?:\{\{!--)/,/^(?:\{\{![\s\S]*?\}\})/,/^(?:\{\{)/,/^(?:=)/,/^(?:\.(?=[} ]))/,/^(?:\.\.)/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}\}\})/,/^(?:\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@[a-zA-Z]+)/,/^(?:true(?=[}\s]))/,/^(?:false(?=[}\s]))/,/^(?:[0-9]+(?=[}\s]))/,/^(?:[a-zA-Z0-9_$-]+(?=[=}\s\/.]))/,/^(?:\[[^\]]*\])/,/^(?:.)/,/^(?:\s+)/,/^(?:[a-zA-Z0-9_$-/]+)/,/^(?:$)/],t.conditions={mu:{rules:[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,32],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[3],inclusive:!1},par:{rules:[30,31],inclusive:!1},INITIAL:{rules:[0,1,32],inclusive:!0}},t}();return e.lexer=s,t.prototype=e,e.Parser=t,new t}();Handlebars.Parser=i,Handlebars.parse=function(t){return t.constructor===Handlebars.AST.ProgramNode?t:(Handlebars.Parser.yy=Handlebars.AST,Handlebars.Parser.parse(t))},Handlebars.print=function(t){return(new Handlebars.PrintVisitor).accept(t)},function(){Handlebars.AST={},Handlebars.AST.ProgramNode=function(t,e){this.type="program",this.statements=t,e&&(this.inverse=new Handlebars.AST.ProgramNode(e))},Handlebars.AST.MustacheNode=function(t,e,s){this.type="mustache",this.escaped=!s,this.hash=e;var i=this.id=t[0],n=this.params=t.slice(1),r=this.eligibleHelper=i.isSimple;this.isHelper=r&&(n.length||e)},Handlebars.AST.PartialNode=function(t,e){this.type="partial",this.partialName=t,this.context=e};var t=function(t,e){if(t.original!==e.original)throw new Handlebars.Exception(t.original+" doesn't match "+e.original)};Handlebars.AST.BlockNode=function(e,s,i,n){t(e.id,n),this.type="block",this.mustache=e,this.program=s,this.inverse=i,this.inverse&&!this.program&&(this.isInverse=!0)},Handlebars.AST.ContentNode=function(t){this.type="content",this.string=t},Handlebars.AST.HashNode=function(t){this.type="hash",this.pairs=t},Handlebars.AST.IdNode=function(t){this.type="ID",this.original=t.join(".");for(var e=[],s=0,i=0,n=t.length;n>i;i++){var r=t[i];if(".."===r||"."===r||"this"===r){if(e.length>0)throw new Handlebars.Exception("Invalid path: "+this.original);".."===r?s++:this.isScoped=!0}else e.push(r)}this.parts=e,this.string=e.join("."),this.depth=s,this.isSimple=1===t.length&&!this.isScoped&&0===s,this.stringModeValue=this.string},Handlebars.AST.PartialNameNode=function(t){this.type="PARTIAL_NAME",this.name=t},Handlebars.AST.DataNode=function(t){this.type="DATA",this.id=t},Handlebars.AST.StringNode=function(t){this.type="STRING",this.string=t,this.stringModeValue=t},Handlebars.AST.IntegerNode=function(t){this.type="INTEGER",this.integer=t,this.stringModeValue=Number(t)},Handlebars.AST.BooleanNode=function(t){this.type="BOOLEAN",this.bool=t,this.stringModeValue="true"===t},Handlebars.AST.CommentNode=function(t){this.type="comment",this.comment=t}}();var n=["description","fileName","lineNumber","message","name","number","stack"];Handlebars.Exception=function(){for(var t=Error.prototype.constructor.apply(this,arguments),e=0;n.length>e;e++)this[n[e]]=t[n[e]]},Handlebars.Exception.prototype=Error(),Handlebars.SafeString=function(t){this.string=t},Handlebars.SafeString.prototype.toString=function(){return""+this.string},function(){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},e=/[&<>"'`]/g,s=/[&<>"'`]/,i=function(e){return t[e]||"&amp;"};Handlebars.Utils={escapeExpression:function(t){return t instanceof Handlebars.SafeString?""+t:null==t||t===!1?"":s.test(t)?t.replace(e,i):t},isEmpty:function(t){return t||0===t?"[object Array]"===Object.prototype.toString.call(t)&&0===t.length?!0:!1:!0}}}(),Handlebars.Compiler=function(){},Handlebars.JavaScriptCompiler=function(){},function(t,e){t.prototype={compiler:t,disassemble:function(){for(var t,e,s,i=this.opcodes,n=[],r=0,a=i.length;a>r;r++)if(t=i[r],"DECLARE"===t.opcode)n.push("DECLARE "+t.name+"="+t.value);else{e=[];for(var o=0;t.args.length>o;o++)s=t.args[o],"string"==typeof s&&(s='"'+s.replace("\n","\\n")+'"'),e.push(s);n.push(t.opcode+" "+e.join(" "))}return n.join("\n")},equals:function(t){var e=this.opcodes.length;if(t.opcodes.length!==e)return!1;for(var s=0;e>s;s++){var i=this.opcodes[s],n=t.opcodes[s];if(i.opcode!==n.opcode||i.args.length!==n.args.length)return!1;for(var r=0;i.args.length>r;r++)if(i.args[r]!==n.args[r])return!1}return!0},guid:0,compile:function(t,e){this.children=[],this.depths={list:[]},this.options=e;var s=this.options.knownHelpers;if(this.options.knownHelpers={helperMissing:!0,blockHelperMissing:!0,each:!0,"if":!0,unless:!0,"with":!0,log:!0},s)for(var i in s)this.options.knownHelpers[i]=s[i];return this.program(t)},accept:function(t){return this[t.type](t)},program:function(t){var e,s=t.statements;this.opcodes=[];for(var i=0,n=s.length;n>i;i++)e=s[i],this[e.type](e);return this.isSimple=1===n,this.depths.list=this.depths.list.sort(function(t,e){return t-e}),this},compileProgram:function(t){var e,s=(new this.compiler).compile(t,this.options),i=this.guid++;this.usePartial=this.usePartial||s.usePartial,this.children[i]=s;for(var n=0,r=s.depths.list.length;r>n;n++)e=s.depths.list[n],2>e||this.addDepth(e-1);return i},block:function(t){var e=t.mustache,s=t.program,i=t.inverse;s&&(s=this.compileProgram(s)),i&&(i=this.compileProgram(i));var n=this.classifyMustache(e);"helper"===n?this.helperMustache(e,s,i):"simple"===n?(this.simpleMustache(e),this.opcode("pushProgram",s),this.opcode("pushProgram",i),this.opcode("emptyHash"),this.opcode("blockValue")):(this.ambiguousMustache(e,s,i),this.opcode("pushProgram",s),this.opcode("pushProgram",i),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},hash:function(t){var e,s,i=t.pairs;this.opcode("pushHash");for(var n=0,r=i.length;r>n;n++)e=i[n],s=e[1],this.options.stringParams?this.opcode("pushStringParam",s.stringModeValue,s.type):this.accept(s),this.opcode("assignToHash",e[0]);this.opcode("popHash")},partial:function(t){var e=t.partialName;this.usePartial=!0,t.context?this.ID(t.context):this.opcode("push","depth0"),this.opcode("invokePartial",e.name),this.opcode("append")},content:function(t){this.opcode("appendContent",t.string)},mustache:function(t){var e=this.options,s=this.classifyMustache(t);"simple"===s?this.simpleMustache(t):"helper"===s?this.helperMustache(t):this.ambiguousMustache(t),t.escaped&&!e.noEscape?this.opcode("appendEscaped"):this.opcode("append")},ambiguousMustache:function(t,e,s){var i=t.id,n=i.parts[0],r=null!=e||null!=s;this.opcode("getContext",i.depth),this.opcode("pushProgram",e),this.opcode("pushProgram",s),this.opcode("invokeAmbiguous",n,r)},simpleMustache:function(t){var e=t.id;"DATA"===e.type?this.DATA(e):e.parts.length?this.ID(e):(this.addDepth(e.depth),this.opcode("getContext",e.depth),this.opcode("pushContext")),this.opcode("resolvePossibleLambda")},helperMustache:function(t,e,s){var i=this.setupFullMustacheParams(t,e,s),n=t.id.parts[0];if(this.options.knownHelpers[n])this.opcode("invokeKnownHelper",i.length,n);else{if(this.knownHelpersOnly)throw Error("You specified knownHelpersOnly, but used the unknown helper "+n);this.opcode("invokeHelper",i.length,n)}},ID:function(t){this.addDepth(t.depth),this.opcode("getContext",t.depth);var e=t.parts[0];e?this.opcode("lookupOnContext",t.parts[0]):this.opcode("pushContext");for(var s=1,i=t.parts.length;i>s;s++)this.opcode("lookup",t.parts[s])},DATA:function(t){this.options.data=!0,this.opcode("lookupData",t.id)},STRING:function(t){this.opcode("pushString",t.string)},INTEGER:function(t){this.opcode("pushLiteral",t.integer)},BOOLEAN:function(t){this.opcode("pushLiteral",t.bool)},comment:function(){},opcode:function(t){this.opcodes.push({opcode:t,args:[].slice.call(arguments,1)})},declare:function(t,e){this.opcodes.push({opcode:"DECLARE",name:t,value:e})},addDepth:function(t){if(isNaN(t))throw Error("EWOT");0!==t&&(this.depths[t]||(this.depths[t]=!0,this.depths.list.push(t)))},classifyMustache:function(t){var e=t.isHelper,s=t.eligibleHelper,i=this.options;if(s&&!e){var n=t.id.parts[0];i.knownHelpers[n]?e=!0:i.knownHelpersOnly&&(s=!1)}return e?"helper":s?"ambiguous":"simple"},pushParams:function(t){for(var e,s=t.length;s--;)e=t[s],this.options.stringParams?(e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",e.stringModeValue,e.type)):this[e.type](e)},setupMustacheParams:function(t){var e=t.params;return this.pushParams(e),t.hash?this.hash(t.hash):this.opcode("emptyHash"),e},setupFullMustacheParams:function(t,e,s){var i=t.params;return this.pushParams(i),this.opcode("pushProgram",e),this.opcode("pushProgram",s),t.hash?this.hash(t.hash):this.opcode("emptyHash"),i}};var s=function(t){this.value=t};e.prototype={nameLookup:function(t,s){return/^[0-9]+$/.test(s)?t+"["+s+"]":e.isValidJavaScriptVariableName(s)?t+"."+s:t+"['"+s+"']"},appendToBuffer:function(t){return this.environment.isSimple?"return "+t+";":{appendToBuffer:!0,content:t,toString:function(){return"buffer += "+t+";"}}},initializeBuffer:function(){return this.quotedString("")},namespace:"Handlebars",compile:function(t,e,s,i){this.environment=t,this.options=e||{},Handlebars.log(Handlebars.logger.DEBUG,this.environment.disassemble()+"\n\n"),this.name=this.environment.name,this.isChild=!!s,this.context=s||{programs:[],environments:[],aliases:{}},this.preamble(),this.stackSlot=0,this.stackVars=[],this.registers={list:[]},this.compileStack=[],this.inlineStack=[],this.compileChildren(t,e);var n,r=t.opcodes;for(this.i=0,a=r.length;a>this.i;this.i++)n=r[this.i],"DECLARE"===n.opcode?this[n.name]=n.value:this[n.opcode].apply(this,n.args);return this.createFunctionContext(i)},nextOpcode:function(){var t=this.environment.opcodes;return t[this.i+1]},eat:function(){this.i=this.i+1},preamble:function(){var t=[];if(this.isChild)t.push("");else{var e=this.namespace,s="helpers = helpers || "+e+".helpers;";this.environment.usePartial&&(s=s+" partials = partials || "+e+".partials;"),this.options.data&&(s+=" data = data || {};"),t.push(s)}this.environment.isSimple?t.push(""):t.push(", buffer = "+this.initializeBuffer()),this.lastContext=0,this.source=t},createFunctionContext:function(t){var e=this.stackVars.concat(this.registers.list);if(e.length>0&&(this.source[1]=this.source[1]+", "+e.join(", ")),!this.isChild)for(var s in this.context.aliases)this.source[1]=this.source[1]+", "+s+"="+this.context.aliases[s];this.source[1]&&(this.source[1]="var "+this.source[1].substring(2)+";"),this.isChild||(this.source[1]+="\n"+this.context.programs.join("\n")+"\n"),this.environment.isSimple||this.source.push("return buffer;");for(var i=this.isChild?["depth0","data"]:["Handlebars","depth0","helpers","partials","data"],n=0,r=this.environment.depths.list.length;r>n;n++)i.push("depth"+this.environment.depths.list[n]);var a=this.mergeSource();if(!this.isChild){var o=Handlebars.COMPILER_REVISION,h=Handlebars.REVISION_CHANGES[o];a="this.compilerInfo = ["+o+",'"+h+"'];\n"+a}if(t)return i.push(a),Function.apply(this,i);var l="function "+(this.name||"")+"("+i.join(",")+") {\n  "+a+"}";return Handlebars.log(Handlebars.logger.DEBUG,l+"\n\n"),l},mergeSource:function(){for(var t,e="",s=0,i=this.source.length;i>s;s++){var n=this.source[s];n.appendToBuffer?t=t?t+"\n    + "+n.content:n.content:(t&&(e+="buffer += "+t+";\n  ",t=void 0),e+=n+"\n  ")}return e},blockValue:function(){this.context.aliases.blockHelperMissing="helpers.blockHelperMissing";var t=["depth0"];this.setupParams(0,t),this.replaceStack(function(e){return t.splice(1,0,e),"blockHelperMissing.call("+t.join(", ")+")"})},ambiguousBlockValue:function(){this.context.aliases.blockHelperMissing="helpers.blockHelperMissing";var t=["depth0"];this.setupParams(0,t);var e=this.topStack();t.splice(1,0,e),t[t.length-1]="options",this.source.push("if (!"+this.lastHelper+") { "+e+" = blockHelperMissing.call("+t.join(", ")+"); }")},appendContent:function(t){this.source.push(this.appendToBuffer(this.quotedString(t)))},append:function(){this.flushInline();var t=this.popStack();this.source.push("if("+t+" || "+t+" === 0) { "+this.appendToBuffer(t)+" }"),this.environment.isSimple&&this.source.push("else { "+this.appendToBuffer("''")+" }")},appendEscaped:function(){this.context.aliases.escapeExpression="this.escapeExpression",this.source.push(this.appendToBuffer("escapeExpression("+this.popStack()+")"))},getContext:function(t){this.lastContext!==t&&(this.lastContext=t)},lookupOnContext:function(t){this.push(this.nameLookup("depth"+this.lastContext,t,"context"))},pushContext:function(){this.pushStackLiteral("depth"+this.lastContext)},resolvePossibleLambda:function(){this.context.aliases.functionType='"function"',this.replaceStack(function(t){return"typeof "+t+" === functionType ? "+t+".apply(depth0) : "+t})},lookup:function(t){this.replaceStack(function(e){return e+" == null || "+e+" === false ? "+e+" : "+this.nameLookup(e,t,"context")})},lookupData:function(t){this.push(this.nameLookup("data",t,"data"))},pushStringParam:function(t,e){this.pushStackLiteral("depth"+this.lastContext),this.pushString(e),"string"==typeof t?this.pushString(t):this.pushStackLiteral(t)},emptyHash:function(){this.pushStackLiteral("{}"),this.options.stringParams&&this.register("hashTypes","{}")},pushHash:function(){this.hash={values:[],types:[]}},popHash:function(){var t=this.hash;this.hash=void 0,this.options.stringParams&&this.register("hashTypes","{"+t.types.join(",")+"}"),this.push("{\n    "+t.values.join(",\n    ")+"\n  }")},pushString:function(t){this.pushStackLiteral(this.quotedString(t))},push:function(t){return this.inlineStack.push(t),t},pushLiteral:function(t){this.pushStackLiteral(t)},pushProgram:function(t){null!=t?this.pushStackLiteral(this.programExpression(t)):this.pushStackLiteral(null)},invokeHelper:function(t,e){this.context.aliases.helperMissing="helpers.helperMissing";var s=this.lastHelper=this.setupHelper(t,e,!0);this.push(s.name),this.replaceStack(function(t){return t+" ? "+t+".call("+s.callParams+") "+": helperMissing.call("+s.helperMissingParams+")"})},invokeKnownHelper:function(t,e){var s=this.setupHelper(t,e);this.push(s.name+".call("+s.callParams+")")},invokeAmbiguous:function(t,e){this.context.aliases.functionType='"function"',this.pushStackLiteral("{}");var s=this.setupHelper(0,t,e),i=this.lastHelper=this.nameLookup("helpers",t,"helper"),n=this.nameLookup("depth"+this.lastContext,t,"context"),r=this.nextStack();this.source.push("if ("+r+" = "+i+") { "+r+" = "+r+".call("+s.callParams+"); }"),this.source.push("else { "+r+" = "+n+"; "+r+" = typeof "+r+" === functionType ? "+r+".apply(depth0) : "+r+"; }")},invokePartial:function(t){var e=[this.nameLookup("partials",t,"partial"),"'"+t+"'",this.popStack(),"helpers","partials"];this.options.data&&e.push("data"),this.context.aliases.self="this",this.push("self.invokePartial("+e.join(", ")+")")},assignToHash:function(t){var e,s=this.popStack();this.options.stringParams&&(e=this.popStack(),this.popStack());var i=this.hash;e&&i.types.push("'"+t+"': "+e),i.values.push("'"+t+"': ("+s+")")},compiler:e,compileChildren:function(t,e){for(var s,i,n=t.children,r=0,a=n.length;a>r;r++){s=n[r],i=new this.compiler;var o=this.matchExistingProgram(s);null==o?(this.context.programs.push(""),o=this.context.programs.length,s.index=o,s.name="program"+o,this.context.programs[o]=i.compile(s,e,this.context),this.context.environments[o]=s):(s.index=o,s.name="program"+o)}},matchExistingProgram:function(t){for(var e=0,s=this.context.environments.length;s>e;e++){var i=this.context.environments[e];if(i&&i.equals(t))return e}},programExpression:function(t){if(this.context.aliases.self="this",null==t)return"self.noop";for(var e,s=this.environment.children[t],i=s.depths.list,n=[s.index,s.name,"data"],r=0,a=i.length;a>r;r++)e=i[r],1===e?n.push("depth0"):n.push("depth"+(e-1));return 0===i.length?"self.program("+n.join(", ")+")":(n.shift(),"self.programWithDepth("+n.join(", ")+")")
},register:function(t,e){this.useRegister(t),this.source.push(t+" = "+e+";")},useRegister:function(t){this.registers[t]||(this.registers[t]=!0,this.registers.list.push(t))},pushStackLiteral:function(t){return this.push(new s(t))},pushStack:function(t){this.flushInline();var e=this.incrStack();return t&&this.source.push(e+" = "+t+";"),this.compileStack.push(e),e},replaceStack:function(t){var e,i="",n=this.isInline();if(n){var r=this.popStack(!0);if(r instanceof s)e=r.value;else{var a=this.stackSlot?this.topStackName():this.incrStack();i="("+this.push(a)+" = "+r+"),",e=this.topStack()}}else e=this.topStack();var o=t.call(this,e);return n?((this.inlineStack.length||this.compileStack.length)&&this.popStack(),this.push("("+i+o+")")):(/^stack/.test(e)||(e=this.nextStack()),this.source.push(e+" = ("+i+o+");")),e},nextStack:function(){return this.pushStack()},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var t=this.inlineStack;if(t.length){this.inlineStack=[];for(var e=0,i=t.length;i>e;e++){var n=t[e];n instanceof s?this.compileStack.push(n):this.pushStack(n)}}},isInline:function(){return this.inlineStack.length},popStack:function(t){var e=this.isInline(),i=(e?this.inlineStack:this.compileStack).pop();return!t&&i instanceof s?i.value:(e||this.stackSlot--,i)},topStack:function(t){var e=this.isInline()?this.inlineStack:this.compileStack,i=e[e.length-1];return!t&&i instanceof s?i.value:i},quotedString:function(t){return'"'+t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r")+'"'},setupHelper:function(t,e,s){var i=[];this.setupParams(t,i,s);var n=this.nameLookup("helpers",e,"helper");return{params:i,name:n,callParams:["depth0"].concat(i).join(", "),helperMissingParams:s&&["depth0",this.quotedString(e)].concat(i).join(", ")}},setupParams:function(t,e,s){var i,n,r,a=[],o=[],h=[];a.push("hash:"+this.popStack()),n=this.popStack(),r=this.popStack(),(r||n)&&(r||(this.context.aliases.self="this",r="self.noop"),n||(this.context.aliases.self="this",n="self.noop"),a.push("inverse:"+n),a.push("fn:"+r));for(var l=0;t>l;l++)i=this.popStack(),e.push(i),this.options.stringParams&&(h.push(this.popStack()),o.push(this.popStack()));return this.options.stringParams&&(a.push("contexts:["+o.join(",")+"]"),a.push("types:["+h.join(",")+"]"),a.push("hashTypes:hashTypes")),this.options.data&&a.push("data:data"),a="{"+a.join(",")+"}",s?(this.register("options",a),e.push("options")):e.push(a),e.join(", ")}};for(var i="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield".split(" "),n=e.RESERVED_WORDS={},r=0,a=i.length;a>r;r++)n[i[r]]=!0;e.isValidJavaScriptVariableName=function(t){return!e.RESERVED_WORDS[t]&&/^[a-zA-Z_$][0-9a-zA-Z_$]+$/.test(t)?!0:!1}}(Handlebars.Compiler,Handlebars.JavaScriptCompiler),Handlebars.precompile=function(t,e){if(!t||"string"!=typeof t&&t.constructor!==Handlebars.AST.ProgramNode)throw new Handlebars.Exception("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+t);e=e||{},"data"in e||(e.data=!0);var s=Handlebars.parse(t),i=(new Handlebars.Compiler).compile(s,e);return(new Handlebars.JavaScriptCompiler).compile(i,e)},Handlebars.compile=function(t,e){function s(){var s=Handlebars.parse(t),i=(new Handlebars.Compiler).compile(s,e),n=(new Handlebars.JavaScriptCompiler).compile(i,e,void 0,!0);return Handlebars.template(n)}if(!t||"string"!=typeof t&&t.constructor!==Handlebars.AST.ProgramNode)throw new Handlebars.Exception("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+t);e=e||{},"data"in e||(e.data=!0);var i;return function(t,e){return i||(i=s()),i.call(this,t,e)}},Handlebars.VM={template:function(t){var e={escapeExpression:Handlebars.Utils.escapeExpression,invokePartial:Handlebars.VM.invokePartial,programs:[],program:function(t,e,s){var i=this.programs[t];return s?Handlebars.VM.program(e,s):i?i:i=this.programs[t]=Handlebars.VM.program(e)},programWithDepth:Handlebars.VM.programWithDepth,noop:Handlebars.VM.noop,compilerInfo:null};return function(s,i){i=i||{};var n=t.call(e,Handlebars,s,i.helpers,i.partials,i.data),r=e.compilerInfo||[],a=r[0]||1,o=Handlebars.COMPILER_REVISION;if(a!==o){if(o>a){var h=Handlebars.REVISION_CHANGES[o],l=Handlebars.REVISION_CHANGES[a];throw"Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+h+") or downgrade your runtime to an older version ("+l+")."}throw"Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+r[1]+")."}return n}},programWithDepth:function(t,e){var s=Array.prototype.slice.call(arguments,2);return function(i,n){return n=n||{},t.apply(this,[i,n.data||e].concat(s))}},program:function(t,e){return function(s,i){return i=i||{},t(s,i.data||e)}},noop:function(){return""},invokePartial:function(t,e,s,i,n,r){var a={helpers:i,partials:n,data:r};if(void 0===t)throw new Handlebars.Exception("The partial "+e+" could not be found");if(t instanceof Function)return t(s,a);if(Handlebars.compile)return n[e]=Handlebars.compile(t,{data:void 0!==r}),n[e](s,a);throw new Handlebars.Exception("The partial "+e+" could not be compiled when running in runtime-only mode")}},Handlebars.template=Handlebars.VM.template,s.exports=Handlebars});
