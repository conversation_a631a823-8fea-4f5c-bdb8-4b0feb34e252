define("arale/validator/0.9.2/async",[],function(a,b,c){var d={};c.exports=d;var e=function(a,b){if(a.forEach)return a.forEach(b);for(var c=0;a.length>c;c+=1)b(a[c],c,a)},f=function(a,b){if(a.map)return a.map(b);var c=[];return e(a,function(a,d,e){c.push(b(a,d,e))}),c},g=function(a){if(Object.keys)return Object.keys(a);var b=[];for(var c in a)a.hasOwnProperty(c)&&b.push(c);return b};d.nextTick="undefined"!=typeof process&&process.nextTick?process.nextTick:function(a){setTimeout(a,0)},d.forEach=function(a,b,c){if(c=c||function(){},!a.length)return c();var d=0;e(a,function(e){b(e,function(b){b?(c(b),c=function(){}):(d+=1,d===a.length&&c(null))})})},d.forEachSeries=function(a,b,c){if(c=c||function(){},!a.length)return c();var d=0,e=function(){b(a[d],function(b){b?(c(b),c=function(){}):(d+=1,d===a.length?c(null):e())})};e()};var h=function(a){return function(){var b=Array.prototype.slice.call(arguments);return a.apply(null,[d.forEach].concat(b))}},i=function(a){return function(){var b=Array.prototype.slice.call(arguments);return a.apply(null,[d.forEachSeries].concat(b))}},j=function(a,b,c,d){var e=[];b=f(b,function(a,b){return{index:b,value:a}}),a(b,function(a,b){c(a.value,function(c,d){e[a.index]=d,b(c)})},function(a){d(a,e)})};d.map=h(j),d.mapSeries=i(j),d.series=function(a,b){if(b=b||function(){},a.constructor===Array)d.mapSeries(a,function(a,b){a&&a(function(a){var c=Array.prototype.slice.call(arguments,1);1>=c.length&&(c=c[0]),b.call(null,a,c)})},b);else{var c={};d.forEachSeries(g(a),function(b,d){a[b](function(a){var e=Array.prototype.slice.call(arguments,1);1>=e.length&&(e=e[0]),c[b]=e,d(a)})},function(a){b(a,c)})}}}),define("arale/validator/0.9.2/utils",["./rule","./async","$","arale/widget/1.0.3/widget","arale/base/1.0.1/base","arale/class/1.0.0/class","arale/events/1.0.0/events"],function(require,exports,module){function unique(){return"__anonymous__"+u_count++}function parseRule(a){var b=a.match(/([^{}:\s]*)(\{[^\{\}]*\})?/);return{name:b[1],param:parseJSON(b[2])}}function parseJSON(str){function getValue(str){return'"'==str.charAt(0)&&'"'==str.charAt(str.length-1)||"'"==str.charAt(0)&&"'"==str.charAt(str.length-1)?eval(str):str}if(!str)return null;var NOTICE='Invalid option object "'+str+'".';str=str.slice(1,-1);var result={},arr=str.split(",");return $.each(arr,function(a,b){if(arr[a]=$.trim(b),!arr[a])throw new Error(NOTICE);var c=arr[a].split(":"),d=$.trim(c[0]),e=$.trim(c[1]);if(!d||!e)throw new Error(NOTICE);result[getValue(d)]=$.trim(getValue(e))}),result}function parseRules(a){return a?a.match(/[a-zA-Z0-9\-\_]+(\{[^\{\}]*\})?/g):null}function parseDom(a){var a=$(a),b={},c=[],d=a.attr("required");d&&(c.push("required"),b.required=!0);var e=a.attr("type");if(e&&"submit"!=e&&"cancel"!=e&&"checkbox"!=e&&"radio"!=e&&"select"!=e&&"select-one"!=e&&"file"!=e&&"hidden"!=e&&"textarea"!=e){if(!Rule.getRule(e))throw new Error('Form field with type "'+e+'" not supported!');c.push(e)}var f=a.attr("min");f&&c.push('min{"min":"'+f+'"}');var g=a.attr("max");g&&c.push("max{max:"+g+"}");var h=a.attr("minlength");h&&c.push("minlength{min:"+h+"}");var i=a.attr("maxlength");i&&c.push("maxlength{max:"+i+"}");var j=a.attr("pattern");if(j){var k=new RegExp(j),l=unique();Rule.addRule(l,k),c.push(l)}var m=a.attr("data-rule");return m=m&&parseRules(m),m&&(c=c.concat(m)),b.rule=0==c.length?null:c.join(" "),b}function helper(a,b){return b?(helpers[a]=b,this):helpers[a]}var Rule=require("./rule"),u_count=0,helpers={};module.exports={parseRule:parseRule,parseRules:parseRules,parseDom:parseDom,helper:helper}}),define("arale/validator/0.9.2/rule",["./async","$","arale/widget/1.0.3/widget","arale/base/1.0.1/base","arale/class/1.0.0/class","arale/events/1.0.0/events"],function(a,b,c){function j(a,b,c){return f.isPlainObject(a)?(f.each(a,function(a,b){f.isArray(b)?j(a,b[0],b[1]):j(a,b)}),this):(d[a]=b instanceof i?new i(a,b.operator):new i(a,b),l(a,c),this)}function k(a,b){var d,c=a.rule;return d=a.message?f.isPlainObject(a.message)?a.message[b?"success":"failure"]:b?"":a.message:e[c][b?"success":"failure"],d?o(a,d):d}function l(a,b){return f.isPlainObject(a)?(f.each(a,function(a,b){l(a,b)}),this):(e[a]=f.isPlainObject(b)?b:{failure:b},this)}function m(a){return d[a].operator}function n(a,b){if(b){var c=d[a];return new i(null,function(a,d){c.operator(f.extend(null,a,b),d)})}return d[a]}function o(a,b){var c=b,d=/\{\{[^\{\}]*\}\}/g,e=/\{\{(.*)\}\}/,g=b.match(d);return g&&f.each(g,function(b,d){var g=d.match(e)[1],h=a[f.trim(g)];c=c.replace(d,h)}),c}var d={},e={},f=a("$"),h=(a("./async"),a("arale/widget/1.0.3/widget")),i=h.extend({initialize:function(a,b){if(this.name=a,b instanceof RegExp)this.operator=function(a,c){var d=b.test(f(a.element).val());c(d?null:a.rule,k(a,d))};else{if("function"!=typeof b)throw new Error("The second argument must be a regexp or a function.");this.operator=function(a,c){var d=b(a,function(b,d){c(b?null:a.rule,d||k(a,b))});void 0!==d&&c(d?null:a.rule,k(a,d))}}},and:function(a,b){if(a instanceof i)var c=a;else var c=n(a,b);if(!c)throw new Error('No rule with name "'+a+'" found.');var d=this,e=function(a,b){d.operator(a,function(d){d?b(d,k(a,!d)):c.operator(a,b)})};return new i(null,e)},or:function(a,b){if(a instanceof i)var c=a;else var c=n(a,b);if(!c)throw new Error('No rule with name "'+a+'" found.');var d=this,e=function(a,b){d.operator(a,function(d){d?c.operator(a,b):b(null,k(a,!0))})};return new i(null,e)},not:function(a){var b=n(this.name,a),c=function(a,c){b.operator(a,function(b){b?c(null,k(a,!0)):c(!0,k(a,!1))})};return new i(null,c)}});j("required",function(a){var b=f(a.element),c=b.attr("type");switch(c){case"checkbox":case"radio":var d=!1;return b.each(function(a,b){return f(b).prop("checked")?(d=!0,!1):void 0}),d;default:return Boolean(b.val())}},"请输入{{display}}"),j("email",/^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,"{{display}}的格式不正确"),j("text",/.*/),j("password",/.*/),j("radio",/.*/),j("checkbox",/.*/),j("url",/^(http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?$/,"{{display}}的格式不正确"),j("number",/^[+-]?[1-9][0-9]*(\.[0-9]+)?([eE][+-][1-9][0-9]*)?$|^[+-]?0?\.[0-9]+([eE][+-][1-9][0-9]*)?$/,"{{display}}的格式不正确"),j("date",/^\d{4}\-[01]?\d\-[0-3]?\d$|^[01]\d\/[0-3]\d\/\d{4}$|^\d{4}年[01]?\d月[0-3]?\d[日号]$/,"{{display}}的格式不正确"),j("min",function(a){var b=a.element,c=a.min;return Number(b.val())>=Number(c)},"{{display}}必须大于或者等于{{min}}"),j("max",function(a){var b=a.element,c=a.max;return Number(b.val())<=Number(c)},"{{display}}必须小于或者等于{{max}}"),j("minlength",function(a){var b=a.element,c=b.val().length;return c>=Number(a.min)},"{{display}}的长度必须大于或等于{{min}}"),j("maxlength",function(a){var b=a.element,c=b.val().length;return Number(a.max)>=c},"{{display}}的长度必须小于或等于{{max}}"),j("mobile",/^1\d{10}$/,"请输入正确的{{display}}"),j("confirmation",function(a){var b=a.element,c=f(a.target);return b.val()==c.val()},"两次输入的{{display}}不一致，请重新输入"),c.exports={addRule:j,setMessage:l,getRule:n,getOperator:m}}),define("arale/validator/0.9.2/item",["./utils","./rule","./async","$","arale/widget/1.0.3/widget","arale/base/1.0.1/base","arale/class/1.0.0/class","arale/events/1.0.0/events"],function(a,b,c){function k(a){return a=String(a),a.charAt(0).toUpperCase()+a.slice(1)}function l(a,b,c,f,i){if(!b){var j=!1,k=a.attr("type");switch(k){case"checkbox":case"radio":var l=!1;a.each(function(a,b){return d(b).prop("checked")?(l=!0,!1):void 0}),j=l;break;default:j=Boolean(a.val())}if(!j)return i&&i(null,null),void 0}if(!d.isArray(c))throw new Error("No validation rule specified or not specified as an array.");var m=[];d.each(c,function(b,c){var g=e.parseRule(c),i=g.name,j=g.param,k=h.getOperator(i);if(!k)throw new Error('Validation rule with name "'+i+'" cannot be found.');var l=d.extend({},j,{element:a,display:j&&j.display||f,rule:i});m.push(function(a){k(l,a)})}),g.series(m,function(a,b){i&&i(a,b[b.length-1])})}function m(a){return a?(a=a[0]||a,!a.offsetHeight):!0}var d=a("$"),e=a("./utils"),f=a("arale/widget/1.0.3/widget"),g=a("./async"),h=a("./rule"),i={value:function(){},setter:function(a){return"function"!=typeof a?e.helper(a):a}},j=f.extend({attrs:{rule:"",display:null,displayHelper:null,triggerType:{setter:function(a){if(!a)return a;var b=d(this.get("element")),c=b.attr("type"),e=b.get(0).tagName.toLowerCase().indexOf("select")>-1||"radio"==c||"checkbox"==c;return e&&(a.indexOf("blur")>-1||a.indexOf("key")>-1)?"change":a}},required:!1,checkNull:!0,errormessage:null,onItemValidate:i,onItemValidated:i,showMessage:i,hideMessage:i},setup:function(){this.get("required")&&(!this.get("rule")||0>this.get("rule").indexOf("required"))&&this.set("rule","required "+this.get("rule")),this.get("display")||"function"!=typeof this.get("displayHelper")||this.set("display",this.get("displayHelper")(this))},execute:function(a,b){if(b=b||{},this.get("skipHidden")&&m(this.element))return a&&a(null,"",this.element),this;this.trigger("itemValidate",this.element,b.event);var c=e.parseRules(this.get("rule")),d=this;return c?(l(this.element,this.get("required"),c,this.get("display"),function(c,e){if(c)var f=d.get("errormessage")||d.get("errormessage"+k(c))||e;else var f=e;d.trigger("itemValidated",c,f,d.element,b.event),a&&a(c,f,d.element)}),this):(a&&a(null,"",this.element),this)}});c.exports=j}),define("arale/validator/0.9.2/core",["./async","./utils","./rule","./item","$","arale/widget/1.0.3/widget","arale/base/1.0.1/base","arale/class/1.0.0/class","arale/events/1.0.0/events"],function(a,b,c){function o(a){var b=a.element.attr(n);return b||(b=a.cid,a.element.attr(n,b)),b}var k,d=a("$"),e=a("./async"),f=a("arale/widget/1.0.3/widget"),g=a("./utils"),h=a("./item"),i=[],j={value:function(){},setter:function(a){return"function"!=typeof a?g.helper(a):a}},l=void 0,m=f.extend({attrs:{triggerType:"blur",checkOnSubmit:!0,stopOnError:!1,autoSubmit:!0,checkNull:!0,onItemValidate:j,onItemValidated:j,onFormValidate:j,onFormValidated:j,displayHelper:function(a){var b,c,e=a.element.attr("id");return e&&(b=d("label[for="+e+"]").text(),b&&(b=b.replace(/^[\*\s\:\：]*/,"").replace(/[\*\s\:\：]*$/,""))),c=a.element.attr("name"),b||c},showMessage:j,hideMessage:j,autoFocus:!0,failSilently:!1,skipHidden:!1},setup:function(){var a=this;this.items=[],k="form"==this.element.get(0).tagName.toLowerCase(),k&&(l=this.element.attr("novalidate"),this.element.attr("novalidate","novalidate"),this.get("checkOnSubmit")&&this.element.submit(function(b){b.preventDefault(),a.execute(function(b){b||a.get("autoSubmit")&&a.element.get(0).submit()})})),this.on("formValidate",function(){var a=this;d.each(this.items,function(b,c){a.query(c.element).get("hideMessage").call(a,null,c.element)})}),this.on("itemValidated",function(a,b,c){a?this.query(c).get("showMessage").call(this,b,c):this.query(c).get("hideMessage").call(this,b,c)}),this.get("autoFocus")&&this.on("formValidated",function(b,c){if(b){var e=null;d.each(c,function(a,b){var c=b[0],d=b[2];return c?(e=d,!1):void 0}),a.trigger("autoFocus",e),e.focus()}}),i.push(this)},Statics:d.extend({helper:g.helper},a("./rule"),{autoRender:function(a){var b=new this(a);d("input, textarea, select",b.element).each(function(a,c){c=d(c);var e=c.attr("type");if("button"==e||"submit"==e||"reset"==e)return!0;var f={};if(f.element="radio"==e||"checkbox"==e?d("[type="+e+"][name="+c.attr("name")+"]",b.element):c,!b.query(f.element)){var h=g.parseDom(c);if(!h.rule)return!0;d.extend(f,h),b.addItem(f)}})},query:function(a){return f.query(a)},validate:function(a){var b=d(a.element),c=new m({element:b.parents()});c.addItem(a),c.query(b).execute(),c.destroy()}}),addItem:function(a){var b=this;if(d.isArray(a))return d.each(a,function(a,c){b.addItem(c)}),this;if(a=d.extend({triggerType:this.get("triggerType"),checkNull:this.get("checkNull"),displayHelper:this.get("displayHelper"),showMessage:this.get("showMessage"),hideMessage:this.get("hideMessage"),failSilently:this.get("failSilently"),skipHidden:this.get("skipHidden")},a),0==d(a.element).length){if(a.failSilently)return this;throw new Error("element does not exist")}var c=new h(a);this.items.push(c),c.set("_handler",function(a){(c.get("checkNull")||c.element.val())&&c.execute(null,{event:a})});var e=c.get("triggerType");return e&&this.element.on(e,"["+n+"="+o(c)+"]",c.get("_handler")),c.on("all",function(){this.trigger.apply(this,[].slice.call(arguments))},this),this},removeItem:function(a){var f,b=a instanceof h?a.element:d(a),e=(this.items,this);return d.each(this.items,function(a,c){return b.get(0)==c.element.get(0)?(f=a,c.get("hideMessage").call(e,null,c.element),e.element.off(c.get("triggerType"),"["+n+"="+o(c)+"]",c.get("_handler")),c.destroy(),!1):void 0}),void 0!==f&&this.items.splice(f,1),this},execute:function(a){var b=this;this.trigger("formValidate",this.element);var c=function(){var c=null;d.each(f,function(a,b){return c=Boolean(b[0]),!c}),b.trigger("formValidated",Boolean(c),f,b.element),a&&a(Boolean(c),f,b.element)},f=[];return this.get("stopOnError")?e.forEachSeries(this.items,function(a,b){a.execute(function(a){f.push([].slice.call(arguments,0)),b(a)})},c):e.forEach(this.items,function(a,b){a.execute(function(){f.push([].slice.call(arguments,0)),b(null)})},c),this},destroy:function(){k&&(void 0==l?this.element.removeAttr("novalidate"):this.element.attr("novalidate",l),this.element.unbind("submit"));var a=this;d.each(this.items,function(b,c){a.removeItem(c)});var b;d.each(i,function(a,c){return c==this?(b=a,!1):void 0}),i.splice(b,1),m.superclass.destroy.call(this)},query:function(a){var b=f.query(a),c=null;return d.each(this.items,function(a,d){return d===b?(c=b,!1):void 0}),c}}),n="data-validator-set";c.exports=m});