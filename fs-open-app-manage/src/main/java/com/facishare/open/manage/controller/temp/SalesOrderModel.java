package com.facishare.open.manage.controller.temp;

import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;

public class SalesOrderModel {
    public enum SalesOrderStatusEnum {
        CONFIRMING(6, "确认中"), // ignoreI18n
        HAS_CONFIRMED(7, "已确认"), // ignoreI18n
        HAS_DELIVERED(10, "已发货"), // ignoreI18n
        HAS_RECEIVED(11, "已收货"), // ignoreI18n
        INVALID(99, "已作废"); // ignoreI18n

        @Getter
        private int code;
        @Getter
        private String desc;

        SalesOrderStatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getStringCode() {
            return String.valueOf(code);
        }
    }

    @Data
    public static class Arg {
        @SerializedName("ObjectType")
        private int objectType;
        @SerializedName("IDs")
        private List<String> ids;
        @SerializedName("IncludeUserDefinedFields")
        private boolean includeUserDefinedFields;
        @SerializedName("IncludeCalculationFields")
        private boolean includeCalculationFields;
    }

    @Data
    public static class Result {
        private List<SalesOrderVo> value;
        private boolean success;
        private String message;
        private int errorCode;
    }


    @Data
    public static class SaveCustomerOrderResult {
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class GetByIdResult {
        private SalesOrderVo value;
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class GetByIdsResult {
        private List<SalesOrderVo> value = Lists.newArrayList();
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class QueryOrderProductArg {
        @SerializedName("Offset")
        private Integer offset;
        @SerializedName("Limit")
        private Integer limit;
        @SerializedName("Conditions")
        private List<Condition> conditions;

        @Data
        public static class Condition {
            @SerializedName("ConditionType")
            private String conditionType;
            @SerializedName("Conditions")
            SalesOrderVo conditions;
        }
    }

    @Data
    public static class QueryOrderProductResult {
        private boolean success;
        private String message;
        private int errorCode;
        List<SalesOrderProductVO> value = Lists.newArrayList();
    }

    @Data
    public static class SetLogisticsStatusArg {
        @SerializedName("ObjectId")
        private String objectId;
        @SerializedName("LogisticsStatus")
        private Integer logisticsStatus;
    }
    @Data
    public static class QueryResult {
        private SalesOrderWithPage value;
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class SalesOrderWithPage {
        @SerializedName("Items")
        private List<SalesOrderVo> items;
        @SerializedName("Page")
        private Page page;
    }

    @Data
    public static class SetLogisticsStatusResult {
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class ExistsDeliveredOrders {
        private boolean value;
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class UpdateCustomerOrderDeliveryToReceivedTask {
        private boolean value;
        private boolean success;
        private String message;
        private int errorCode;
    }

    @Data
    public static class SalesOrderVo {
        @SerializedName("_id")
        private String id;
        @SerializedName("CustomerTradeID")
        private String customerTradeId;
        @SerializedName("CustomerID")
        private String customerId;
        @SerializedName("TradeCode")
        private String tradeCode;
        @SerializedName("ShippingWarehouseID")
        private String warehouseId;
        @SerializedName("IsDeleted")
        private Boolean isDeleted;
        @SerializedName("Status")
        private Integer status;
        private String promotionId;
        @SerializedName("TradeMoney")
        private BigDecimal tradeMoney;
        @SerializedName("LogisticsStatus")
        private Integer logisticsStatus;
        @SerializedName("DeliveredAmountSum")
        private BigDecimal deliveredAmountSum;
        @SerializedName("ConfirmReceiveTime")
        private Long confirmReceiveTime;
        @SerializedName("ConfirmDeliveryTime")
        private Long confirmDeliveryTime;
        @SerializedName("CreateTime")
        private Long createTime;
        @SerializedName("CreatorID")
        private String creatorId;
        @SerializedName("OwnerID")
        private String ownerId;         //这个不是数据负责人
        @SerializedName("BelongerID")
        private String belongerId;      //这个才是数据负责人
    }

    @Data
    public static class SalesOrderProductVO {
        @SerializedName("Name")
        private String name;
        @SerializedName("TradeProductID")
        private String tradeProductId;
        @SerializedName("TradeCode")
        private String tradeCode;
        @SerializedName("CustomerTradeID")
        private String customerTradeId;
        @SerializedName("ProductID")
        private String productId;
        @SerializedName("ProductName")
        private String productName;
        @SerializedName("Amount")
        private BigDecimal amount;
        @SerializedName("RecordType")
        private String recordType;
        @SerializedName("Price")
        private BigDecimal price;
        @SerializedName("SubTotal")
        private BigDecimal subTotal;
        @SerializedName("DeliveredCount")
        private BigDecimal deliveredCount;
    }

    @Data
    public static class CheckStockEnableResult {
        @SerializedName("value")
        private boolean value;
        @SerializedName("success")
        private boolean success;
        @SerializedName("message")
        private String message;
        @SerializedName("errorCode")
        private int errorCode;
    }
}
