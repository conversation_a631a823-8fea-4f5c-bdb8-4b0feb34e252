package com.facishare.open.manage.manager.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.app.center.api.service.CrmViewService;
import com.facishare.open.app.center.api.service.externals.AppViewBizService;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.open.manage.manager.CrmViewOpsManager;
import com.facishare.open.manage.manager.EmployeeManager;
import com.facishare.open.manage.manager.EnterpriseManager;
import com.facishare.open.manage.model.CheckCrmViewModel;
import com.facishare.open.manage.model.CheckCrmViewModel.CheckVO;
import com.facishare.open.manage.model.RepairCrmViewModel;
import com.facishare.open.manage.support.third.model.EmployeeRoleRelationResult;
import com.facishare.open.manage.support.third.service.PaasAuthService;
import com.facishare.open.manage.utils.ConfigCenter;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2021/5/20
 */
@Component
@Slf4j
public class CrmViewOpsManagerImpl implements CrmViewOpsManager {
    /**
     * 外部人员ID边界值，大于则是外部人员
     */
    private static final Integer OUT_EMPLOYEE_ID_THRESHOLD = 10000 * 10000;

    @Autowired
    private CrmViewService crmViewService;
    @Autowired
    private PaasAuthService paasAuthService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private AppViewBizService appViewBizService;
    @Resource
    private EnterpriseManager enterpriseManager;
    @Autowired
    private EmployeeManager employeeManager;

    @Override
    public CheckCrmViewModel.Result checkCrmView(CheckCrmViewModel.Arg arg) {
        List<CheckCrmViewModel.CheckVO> diffCheckVOList = Lists.newArrayList();
        List<String> diffEaList = Lists.newArrayList();
        List<String> eaList = arg.getEaList();
        List<Integer> eiList = arg.getEiList();

        if (!CollectionUtils.isEmpty(eaList)) {
            for (String ea : eaList) {
                checkDiff(arg, diffCheckVOList, diffEaList, ea);
            }
        } if (!CollectionUtils.isEmpty(eiList)) {

            for (Integer ei : eiList) {
                String ea = eieaConverter.enterpriseIdToAccount(ei);
                checkDiff(arg, diffCheckVOList, diffEaList, ea);
            }
        }  else if (arg.getCheckAll()) {

            List<Integer> normalEnterpriseIds = enterpriseManager.getNormalEnterpriseIdsByEnvironment(arg.getEnterpriseEnvironment());

            if (!CollectionUtils.isEmpty(normalEnterpriseIds)) {
                for (Integer normalEnterpriseId : normalEnterpriseIds) {
                    String ea = eieaConverter.enterpriseIdToAccount(normalEnterpriseId);
                    checkDiff(arg, diffCheckVOList, diffEaList, ea);
                }
            }
        }

        log.info("diffEaList[{}]", diffEaList);

        return CheckCrmViewModel.Result.builder().diffEaList(diffEaList).diffCheckVOList(diffCheckVOList).build();
    }

    private void checkDiff(CheckCrmViewModel.Arg arg, List<CheckVO> diffCheckVOList, List<String> diffEaList, String ea) {
        try {
            Boolean verboseResult = arg.getVerboseResult();
            CheckVO checkVO = getCheckVO(arg.getFilterStopEmployee(), verboseResult, ea);
            if (checkVO.getDiff()) {
                log.info("ea[{}] ei[{}] crmViewDiff", ea);
                diffEaList.add(checkVO.getEa());
                if (verboseResult) {
                    diffCheckVOList.add(checkVO);
                }
            }
        } catch (Exception e) {
            log.error("checkDiff error. ea[{}]", ea, e);
        }
    }

    /**
     * 获取检查结果
     * @param filterStopEmployee crm角色人员是否过滤停用员工
     * @param verboseResult 是否返回详细的员工列表（crm角色人员、crm角色人员（无停用员工）、crm可见范围）
     * @param ea
     * @return
     */
    private CheckVO getCheckVO(Boolean filterStopEmployee, Boolean verboseResult, String ea) {
        // CRM角色员工
        List<Integer> crmRoleEmployee = getCrmRoleEmployee(ea);
        List<Integer> crmRoleNormalEmployee = employeeManager.filterStopEmployee(ea, crmRoleEmployee);

        // CRM应用可见人员
        List<Integer> crmView = getCrmViewUser(ea);

        List<Integer> crmViewToAdd = crmRoleEmployee.stream().filter(employee -> !crmView.contains(employee)).collect(Collectors.toList());
        if (filterStopEmployee) {
            crmViewToAdd = crmRoleNormalEmployee.stream().filter(employee -> !crmView.contains(employee)).collect(Collectors.toList());
        }

        List<Integer> crmViewToRemove = crmView.stream().filter(employee -> !crmRoleEmployee.contains(employee)).collect(Collectors.toList());
        if (filterStopEmployee) {
            crmViewToRemove = crmView.stream().filter(employee -> !crmRoleNormalEmployee.contains(employee)).collect(Collectors.toList());
        }

        log.info("ea[{}], crmViewToAdd[{}], crmViewToRemove[{}]", ea, crmViewToAdd, crmViewToRemove);

        boolean diff = !CollectionUtils.isEmpty(crmViewToAdd) || !CollectionUtils.isEmpty(crmViewToRemove);
        CheckVO checkVO = CheckVO.builder().crmViewToAdd(crmViewToAdd)
            .crmViewToRemove(crmViewToRemove).ea(ea).ei(eieaConverter.enterpriseAccountToId(ea)).diff(diff).build();

        if (verboseResult) {
            checkVO.setCrmRoleEmployee(crmRoleEmployee);
            checkVO.setCrmRoleNormalEmployee(crmRoleNormalEmployee);
            checkVO.setCrmView(crmView);
        }
        return checkVO;
    }

    private List<Integer> getCrmRoleEmployee(String fsEa) {
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<EmployeeRoleRelationResult> employeeRoleRelationResultList =
            paasAuthService.getAllEmployeeRoleRelationEntities(String.valueOf(ei), "CRM");

        return employeeRoleRelationResultList.stream().map(EmployeeRoleRelationResult::getOrgId)
            .map(Integer::valueOf).filter(employeeId -> employeeId < OUT_EMPLOYEE_ID_THRESHOLD)
            .distinct().collect(Collectors.toList());
    }

    private List<Integer> getCrmViewUser(String fromEa) {
        BaseResult<List<Pair<Integer, Long>>> viewUserResult = crmViewService.queryUsers(fromEa);
        if (!viewUserResult.isSuccess()) {
            log.warn("queryUsers failed. fsEa[{}], usersResult[{}]", fromEa, viewUserResult);
            throw new BizException(viewUserResult);
        }

        if (!CollectionUtils.isEmpty(viewUserResult.getResult())) {
            return viewUserResult.getResult().stream().map(pair -> pair.first).collect(Collectors.toList());
        } else {
            return Lists.newArrayListWithCapacity(0);
        }

    }

    @Override
    public RepairCrmViewModel.Result repairCrmView(RepairCrmViewModel.Arg arg) {
        Boolean filterStopEmployee = arg.getFilterStopEmployee();
        List<String> repairedEaList = Lists.newArrayList();
        List<String> eaList = arg.getEaList();
        if (!CollectionUtils.isEmpty(eaList)) {
            for (String ea : eaList) {
                repair(ea, repairedEaList, filterStopEmployee);
            }
        } if (!CollectionUtils.isEmpty(arg.getEiList())) {
            for (Integer ei : arg.getEiList()) {
                String ea = eieaConverter.enterpriseIdToAccount(ei);
                repair(ea, repairedEaList, filterStopEmployee);
            }
        } else if (arg.getCheckAll()) {
            List<Integer> normalEnterpriseIds = enterpriseManager.getNormalEnterpriseIdsByEnvironment(arg.getEnterpriseEnvironment());

            if (!CollectionUtils.isEmpty(normalEnterpriseIds)) {
                for (Integer normalEnterpriseId : normalEnterpriseIds) {
                    String ea = eieaConverter.enterpriseIdToAccount(normalEnterpriseId);
                    repair(ea, repairedEaList, filterStopEmployee);
                }
            }
        }

        return RepairCrmViewModel.Result.builder().repairedEaList(repairedEaList).build();
    }

    private void repair(String ea, List<String> repairedEaList, boolean filterStopEmployee) {
        try {

            CheckVO repairVO = getCheckVO(filterStopEmployee, false, ea);
            if (repairVO.getDiff()) {
                repairedEaList.add(ea);

                // 修复crm可见范围（多的删除、少的增加）
                repair(repairVO);
            }
        } catch (Exception e) {
            log.error("repair error. ea[{}]", ea, e);
        }
    }

    private void repair(CheckVO repairVO) {
        log.info("repairEa[{}]", repairVO.getEa());

        List<Integer> addUsers = repairVO.getCrmViewToAdd();
        List<Integer> removeUsers = repairVO.getCrmViewToRemove();
        removeCrmViewUser(repairVO.getEa(), removeUsers);
        addCrmViewUser(repairVO.getEa(), addUsers);
    }

    private void addCrmViewUser(String ea, List<Integer> addUsers) {
        if (CollectionUtils.isEmpty(addUsers)) {
            return;
        }

        // 这里不校验配额了
        BaseResult<Integer> addResult = appViewBizService.addEmployeeView(ConfigCenter.getCrmComponentId(), ea, addUsers, false);
        if (!addResult.isSuccess()) {
            log.error("addCrmViewUser failed. fsEa[{}], addUsers[{}]", ea, addResult);
        }
    }

    private void removeCrmViewUser(String ea, List<Integer> removeUsers) {
        if (CollectionUtils.isEmpty(removeUsers)) {
            return;
        }

        BaseResult<Integer> removeUsersResult = appViewBizService.removeEmployeeView(ConfigCenter.getCrmComponentId(), ea, removeUsers);
        if (!removeUsersResult.isSuccess()) {
            log.warn("removeUsers failed. fsEa[{}], removeUsers[{}]", ea, removeUsers);
            throw new BizException(removeUsersResult);
        }
    }
}
