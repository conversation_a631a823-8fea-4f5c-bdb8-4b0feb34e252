package com.facishare.open.manage.ajax.result;

import com.facishare.open.manage.model.BuyRecordVO;
import com.facishare.open.support.base.Pager;
import lombok.Data;


/**
 * created by dailf on 2018/4/24
 *
 * <AUTHOR>
 */
@Data
public class BuyRecordType {
    @Data
    public static class GetBuyRecordByPageArg {
        private Integer pageSize;
        private Integer currentPage;
        private String quotaType;
        private String payType;
        private Long startTime;
        private Long endTime;

    }

    @Data
    public static class GetBuyRecordByPageResult extends BaseCrmResult {
        private ResultBody<BuyRecordVO> result;

    }
}
